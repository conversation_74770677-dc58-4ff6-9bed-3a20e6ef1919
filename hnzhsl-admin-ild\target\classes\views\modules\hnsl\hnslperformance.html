<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta
	content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
	name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet"
	href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet"
	href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css"
	rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script
	src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script
	src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
<style type="text/css">
.templateShow-Info {
	float: left;
	width: 100%;
	font-size: 20px;
	padding: 10px 25px 0;
}

.templateShow-Info p {
	font-size: 20px;
	float: left;
	text-align: center;
	margin: 10px 0 10px;
	margin: 10px 0 10px;
}

.templateShow-Info p:nth-child(2) {
	color: #999 !important;
	font-size: 16px;
	line-height: 28px;
}
</style>
</head>
<body>
	<div id="rrapp" v-cloak>
		<div v-show="showList" id="isShowList">

			<div class="row">
				<div class="form-group col-md-2">
					<label>状态:</label> <select class="form-control"
						style="height: 32%;" v-model='hnslPerformance.performanceStatus'>
						<option value=''>全部</option>
						<option value='1'>已评</option>
						<option value='2'>未评</option>
					</select>
				</div>

				<!-- <div class="form-group col-md-2">
					<label>截图:</label> <select class="form-control"
						style="height: 32%;" v-model='hnslPerformance.JieTuStatus'>
						<option value=''>全部</option>
						<option value='1'>已上传</option>
						<option value='2'>未上传</option>
					</select>
				</div> -->

				<div class="form-group col-md-2">
					<label>身份:</label> <select class="form-control"
						style="height: 32%;" v-model='hnslPerformance.statusSf'>
						<option value=''>全部</option>
						<option value='1'>校园经理</option>
						<option value='2'>一级</option>
						<option value='3'>二级</option>
						<option value='4'>三级</option>
					</select>
				</div>

				<!-- 地市下拉框:省级管理员才显示 -->
				<!-- <div v-if='statusSf==5' class="form-group col-md-2">
				   <label>地市:</label>
					<select  id="prov" name="prov" class="form-control"
						style="height: 32%" v-model="cityCode"
						@change="onChangeCity()">
						<option value="">全部</option>
						<option v-for="item in city" :value="item.cityCode"
							:key="item.cityCode">{{item.cityName}}</option>
					</select>
				</div> -->
				<!-- 学校 -->
				<!-- <div class="form-group col-md-2">
				   <label>学校:</label>
					<select id="prov" name="prov" class="form-control"
						style="height: 32%" v-model="select1"
						@change="onChangeSchool()">
						<option v-if="listProv.length==0" value="">暂无学校</option>
						<option v-else="listProv.length==0" v-for="item in listProv" :value="item.schoolCode"
							:key="item.schoolCode">{{item.schoolName}}</option>
					</select>
				</div> -->


				<div class="form-group col-md-2" style="height: 32%">
					<label>姓名   |  手机号码</label> <input type="text" class="form-control"
						placeholder="姓名或手机号码" v-model='hnslPerformance.userNameOrUserPhone' />
				</div>

				<div class="form-group col-md-2" style="height: 32%">
					<label>绩效时间:</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker " style="width: 105%;"
							   id="dateTimeRange" @keyup.enter="query" value="" type="text"
							   placeholder="日期"> <span class="input-group-addon" style="width: 0%">
							<i class="fa fa-calendar bigger-110"></i>
 						</span>
						<input name="beginTime"  id="beginTime" type="hidden" >
						<input name="endTime" id="endTime" type="hidden" >
					</div>
				</div>
				<div class="form-group col-md-2" style="height: 32%">
					<label>是否楼栋长:</label>
					<select class="form-control"
							style="height: 32%;" v-model='hnslPerformance.building'>
							<option value="">全部</option>
							<option value="1">是</option>
							<option value="0">否</option>
					</select>
				</div>
				<div class="form-group col-md-2" style="height: 40px">
					<label>学校:</label> <input type="text" class="form-control"
											  placeholder="学校" v-model="hnslPerformance.schoolName" />
				</div>
			</div>
			<div class="row">
				<div class="form-group col-md-2">
					<label>所属城市:</label> <select class="form-control"
						 style="height: 32px;" v-model="cityCode">
					<option value=''>全部</option>
					<option v-for="itme in city" v-bind:value="itme.cityCode">
						{{itme.cityName}}</option>
				</select>
				</div>
			</div>


			<div class="grid-btn" style="margin-left: 19px;">
				<a v-if="hasPermission('hnslperformance:query')"
					class="btn btn-primary" @click="query">&nbsp;查&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;询</a>
				<a class="btn btn-primary" @click="templateShowI">&nbsp;批量导入</a>
			</div>
			<table id="jqGrid"></table>
			<div id="jqGridPager"></div>
		</div>
		<!--模板下载 -->
		<div v-show="!templateShow" id="templateShow"
			class="panel panel-default">
			<div class="panel-heading">{{title}}</div>
			<div class="form-horizontal" style="padding-top: 0px; width: 100%;">
				<form id="uploadImg" enctype="multipart/form-data">
					<div class="templateShow-Info">
						<p>下载模板：</p>
						<p>为提高导入的成功率，请下载斌使用系统提供的模板:</p>
					</div>
					<div style="margin-left: 125px;">
						<a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
					</div>
					<div class="templateShow-Info">
						<p>上传文件：</p>
						<p>仅支持:xlsx.，xls.,csv.;文件大小:≤4M</p>
					</div>
					<div style="margin-left: 125px;">
						<a v-if="hasPermission('hnsduser:importUser')"
							class="btn btn-primary" @click="inputPerformance">&nbsp;开始导入</a> <input
							style="display: none;" name="uploadFile" id="uploadFile"
							type="file" @change="uploadFile" />
					</div>
					<div style="width: 100%; text-align: center;">
<!-- 						<input type="button" class="btn btn-warning" @click="reload" -->
<!-- 							value="返回" /> -->
							<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
					</div>
				</form>
			</div>
		</div>
		<div v-show="!showList" class="panel panel-default">
			<div class="panel-heading">{{title}}</div>
			<form class="form-horizontal">
				<div class="form-group">
					<div class="col-sm-2 control-label">绩效等级(A B C)</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnslPerformance.levels" placeholder="绩效等级(A B C)" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">操作时间</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnslPerformance.creationtime" placeholder="操作时间" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">创建人</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnslPerformance.createdUser" placeholder="创建人" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">创建时间</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnslPerformance.createdDate" placeholder="创建时间" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">父ID</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnslPerformance.userId" placeholder="父ID" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">是否可用状态（0:否 1:是</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnslPerformance.status" placeholder="是否可用状态（0:否 1:是" />
					</div>
				</div>
				<!-- <div class="form-group">
				<div class="col-sm-2 control-label"></div>
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div> -->
			</form>
		</div>
	</div>

	<script src="../../js/modules/hnsl/hnslperformance.js"></script>
	<script src="../../js/components.js"></script>
</body>
</html>