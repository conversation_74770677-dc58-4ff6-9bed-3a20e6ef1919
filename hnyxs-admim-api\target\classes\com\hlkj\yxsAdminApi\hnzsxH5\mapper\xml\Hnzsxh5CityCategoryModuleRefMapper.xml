<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5CityCategoryModuleRefMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsxh5_city_category_module_ref a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.cityCategoryRefId != null">
                AND a.CITY_CATEGORY_REF_ID = #{param.cityCategoryRefId}
            </if>
            <if test="param.moduleId != null">
                AND a.MODULE_ID = #{param.moduleId}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.sort != null">
                AND a.SORT = #{param.sort}
            </if>
            <if test="param.isOneBeat != null">
                AND a.IS_ONE_BEAT = #{param.isOneBeat}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5CityCategoryModuleRef">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5CityCategoryModuleRef">
        <include refid="selectSql"></include>
    </select>

</mapper>
