# 测试环境配置

# 数据源配置
spring:
  datasource:
    url: *********************************************************************************************************************
    username: hnkj_yxs_app
    password: 'Aah7z9M8eGPPm!v9'
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
  redis:
    #    password: OSV2FUz#koesGl%S
    password:
    timeout: 6000ms
    #哨兵模式
    sentinel:
      master: mymaster
      #      nodes: 134.176.97.81:26399,134.176.97.82:26379,134.176.97.82:26389
      nodes: 127.0.0.1:36379,127.0.0.1:36380,127.0.0.1:36381
    lettuce:
      pool:
        max-active: 100
        max-idle: 20
        max-wait: -1ms
        min-idle: 20
    jmx:
      default-domain: jsga_managerJmxPros

# 日志配置
logging:
  level:
    com.hlkj.yxsAdminApi: DEBUG
    com.baomidou.mybatisplus: DEBUG
# 系统配置
config:
  hnzsxFileImgPath: /app/nfs2/WM/hnzsx/uploads/ #掌上销图片上传地址
  hnzhslFilePath: /app/nfs2/PJY/hnzhsl/uploads/ #智慧扫楼文件上传地址