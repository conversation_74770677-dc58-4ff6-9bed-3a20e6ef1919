<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslWhiteListMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_white_list a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.iccid != null">
                AND a.ICCID LIKE CONCAT('%', #{param.iccid}, '%')
            </if>
            <if test="param.schoolChannel != null">
                AND a.SCHOOL_CHANNEL = #{param.schoolChannel}
            </if>
            <if test="param.customerName != null">
                AND a.CUSTOMER_NAME LIKE CONCAT('%', #{param.customerName}, '%')
            </if>
            <if test="param.customerCard != null">
                AND a.CUSTOMER_CARD LIKE CONCAT('%', #{param.customerCard}, '%')
            </if>
            <if test="param.failureDate != null">
                AND a.FAILURE_DATE LIKE CONCAT('%', #{param.failureDate}, '%')
            </if>
            <if test="param.remarks != null">
                AND a.REMARKS LIKE CONCAT('%', #{param.remarks}, '%')
            </if>
            <if test="param.numberStatus != null">
                AND a.NUMBER_STATUS = #{param.numberStatus}
            </if>
            <if test="param.batchCode != null">
                AND a.BATCH_CODE LIKE CONCAT('%', #{param.batchCode}, '%')
            </if>
            <if test="param.specifyPoints != null">
                AND a.SPECIFY_POINTS LIKE CONCAT('%', #{param.specifyPoints}, '%')
            </if>
            <if test="param.customerPhone != null">
                AND a.CUSTOMER_PHONE LIKE CONCAT('%', #{param.customerPhone}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteList">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteList">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询白名单报表    -->
    <select id="queryListTable" resultType="java.util.HashMap">
        select t.*,t1.school_name,ifnull(t.s3,0)+ifnull(t.s4,0) as s5 from (
        select t.batch_code,t.school_code,max(t.city_code) as city_code,max(t.school_channel) as school_channel
        ,max(t.created_date) as created_date,count(*) as zs,
        sum((case t.number_status when 1 then 1 else 0 end)) as s1,
        sum((case t.number_status when 2 then 1 else 0 end)) as s2,
        sum((case t.number_status when 3 then 1 else 0 end)) as s3,
        sum((case t.number_status when 4 then 1 else 0 end)) as s4
        from hnsl_white_list t
        <where>
            <if test="status != null and status !='' ">
                AND t.STATUS = #{status}
            </if>
            <if test="numberStatus != null and numberStatus !='' ">
                AND t.NUMBER_STATUS = #{numberStatus}
            </if>
            <if test="userPhone != null and userPhone !=''">
                AND t.USER_PHONE = #{userPhone}
            </if>
            <if test="cityCode != null and cityCode != '' ">
                AND t.CITY_CODE = #{cityCode}
            </if>
            <if test="beginTime != null and beginTime.trim() != ''">
                AND t.CREATED_DATE between
                #{beginTime} and
                #{endTime}
            </if>
            <if test="iccid != null and iccid != '' ">
                AND t.ICCID = #{iccid}
            </if>
            <if test="batchCode != null and batchCode != '' ">
                AND t.BATCH_CODE = #{batchCode}
            </if>
        </where>
        group by t.batch_code,t.school_code) t
        left join hnsl_school t1 on t.school_code=t1.school_code
        <where>
            <if test="schoolName != null and schoolName != '' ">
                AND t1.SCHOOL_NAME = like concat(concat('%',#{schoolName}),'%')
            </if>
        </where>
    </select>

    <select id="queryWhiteObject" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteList">
        select *
        from hnsl_white_list t
        where t.USER_PHONE = #{userPhone}
          and t.CITY_CODE = #{cityCode}
          and t.SCHOOL_CODE = #{schoolCode}
        ORDER BY t.FAILURE_DATE
    </select>
</mapper>
