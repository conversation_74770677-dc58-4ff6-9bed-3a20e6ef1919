<?xml version="1.0" encoding="UTF-8"?>

<Configuration status="OFF" monitorInterval="1800">
    <properties>
        <property name="LOG_HOME">/app/webapps/WM/logs/hnzsxAdmin/</property>
        <property name="FILE_NAME">log</property>

        <property name="debug-msg">debug</property>
        <property name="info-msg">info</property>
        <property name="error-msg">error</property>

        <property name="pattern-msg">%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n</property>
    </properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n"/>
        </Console>

        <!-- 输出到文件，文件达到一定阈值时，自动备份日志文件的策略模式 -->
        <RollingRandomAccessFile name="debug-log" fileName="${LOG_HOME}/${debug-msg}${FILE_NAME}.log" filePattern="${LOG_HOME}/$${date:yyyy-MM}/${debug-msg}${FILE_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <Filters>
                <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${pattern-msg}" /> <!-- 指定输出格式 -->
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="20"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="info-log" fileName="${LOG_HOME}/${info-msg}${FILE_NAME}.log" filePattern="${LOG_HOME}/$${date:yyyy-MM}/${info-msg}${FILE_NAME}-%d{yyyy-MM-dd}-%i.log.gz"
                                 >
            <Filters>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${pattern-msg}"/> <!-- 指定输出格式 -->
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="20"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="error-log" fileName="${LOG_HOME}/${error-msg}${FILE_NAME}.log" filePattern="${LOG_HOME}/$${date:yyyy-MM}/${error-msg}${FILE_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY" />
            </Filters>
            <PatternLayout pattern="${pattern-msg}"/> <!-- 指定输出格式 -->
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="20"/>
        </RollingRandomAccessFile>
    </Appenders>

    <Loggers>
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="debug-log"/>
            <AppenderRef ref="info-log"/>
            <AppenderRef ref="error-log"/>
        </Root>
    </Loggers>
</Configuration>