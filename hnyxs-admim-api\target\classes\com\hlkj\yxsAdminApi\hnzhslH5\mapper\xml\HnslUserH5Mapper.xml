<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslUserH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*,ss.SCHOOL_NAME
        FROM hnsl_h5_user a
        left join (select us.USER_PHONE,group_concat(DISTINCT s.SCHOOL_NAME) as SCHOOL_NAME from hnsl_h5_user_school us
        left join hnsl_h5_school s on us.SCHOOL_CODE=s.SCHOOL_CODE where us.`STATUS`=1
        GROUP BY us.USER_PHONE) ss
        on a.USER_PHONE=ss.USER_PHONE
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.userName != null">
                AND a.USER_NAME LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.userSfz != null">
                AND a.USER_SFZ LIKE CONCAT('%', #{param.userSfz}, '%')
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.userSite != null">
                AND a.USER_SITE LIKE CONCAT('%', #{param.userSite}, '%')
            </if>
            <if test="param.numbers != null">
                AND a.NUMBERS LIKE CONCAT('%', #{param.numbers}, '%')
            </if>
            <if test="param.entrydate != null">
                AND a.ENTRYDATE LIKE CONCAT('%', #{param.entrydate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.userPhoneTwo != null">
                AND a.USER_PHONE_TWO LIKE CONCAT('%', #{param.userPhoneTwo}, '%')
            </if>
            <if test="param.building != null">
                AND a.BUILDING = #{param.building}
            </if>
            <if test="param.flock != null">
                AND a.FLOCK LIKE CONCAT('%', #{param.flock}, '%')
            </if>
            <if test="param.integral != null">
                AND a.INTEGRAL = #{param.integral}
            </if>
            <if test="param.imageFront != null">
                AND a.IMAGE_FRONT LIKE CONCAT('%', #{param.imageFront}, '%')
            </if>
            <if test="param.imageVerso != null">
                AND a.IMAGE_VERSO LIKE CONCAT('%', #{param.imageVerso}, '%')
            </if>
            <if test="param.fullFaced != null">
                AND a.FULL_FACED LIKE CONCAT('%', #{param.fullFaced}, '%')
            </if>
            <if test="param.performance != null">
                AND a.PERFORMANCE LIKE CONCAT('%', #{param.performance}, '%')
            </if>
            <if test="param.papersDataAgo != null">
                AND a.PAPERS_DATA_AGO LIKE CONCAT('%', #{param.papersDataAgo}, '%')
            </if>
            <if test="param.papersDataLater != null">
                AND a.PAPERS_DATA_LATER LIKE CONCAT('%', #{param.papersDataLater}, '%')
            </if>
            <if test="param.statusSf != null">
                AND a.STATUS_SF = #{param.statusSf}
            </if>
            <if test="param.statusSuperior != null and param.statusSuperior!=''">
                AND a.STATUS_SUPERIOR LIKE CONCAT('%', #{param.statusSuperior}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null  and param.createdDate!=''">
                AND a.CREATED_DATE &gt;= #{param.createdDate}
            </if>
            <if test="param.beginTime != null and param.beginTime.trim() != ''">
                and a.CREATED_DATE between #{param.beginTime} and #{param.endTime}
            </if>
            <if test="param.ethnic != null">
                AND a.ETHNIC LIKE CONCAT('%', #{param.ethnic}, '%')
            </if>
            <if test="param.userGender != null">
                AND a.USER_GENDER = #{param.userGender}
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.openid != null">
                AND a.OPENID LIKE CONCAT('%', #{param.openid}, '%')
            </if>
            <if test="param.imageUrl != null">
                AND a.IMAGE_URL LIKE CONCAT('%', #{param.imageUrl}, '%')
            </if>
            <if test="param.userWechat != null">
                AND a.USER_WECHAT LIKE CONCAT('%', #{param.userWechat}, '%')
            </if>
            <if test="param.salesCode != null">
                AND a.SALES_CODE LIKE CONCAT('%', #{param.salesCode}, '%')
            </if>
            <if test="param.department != null">
                AND a.DEPARTMENT LIKE CONCAT('%', #{param.department}, '%')
            </if>
            <if test="param.userMail != null">
                AND a.USER_MAIL LIKE CONCAT('%', #{param.userMail}, '%')
            </if>
            <if test="param.userManger != null">
                AND a.USER_MANGER LIKE CONCAT('%', #{param.userManger}, '%')
            </if>
            <if test="param.qrcode != null">
                AND a.QRCODE LIKE CONCAT('%', #{param.qrcode}, '%')
            </if>
            <if test="param.userCode != null">
                AND a.USER_CODE LIKE CONCAT('%', #{param.userCode}, '%')
            </if>
            <if test="param.uploadNorm != null">
                AND a.UPLOAD_NORM = #{param.uploadNorm}
            </if>
            <if test="param.explainNorm != null">
                AND a.EXPLAIN_NORM = #{param.explainNorm}
            </if>
            <if test="param.channelId != null">
                AND a.CHANNEL_ID LIKE CONCAT('%', #{param.channelId}, '%')
            </if>
            <if test="param.channelNbr != null">
                AND a.CHANNEL_NBR LIKE CONCAT('%', #{param.channelNbr}, '%')
            </if>
            <if test="param.loginDate != null">
                AND a.LOGIN_DATE LIKE CONCAT('%', #{param.loginDate}, '%')
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.choiceSetMeal != null">
                AND a.CHOICE_SET_MEAL LIKE CONCAT('%', #{param.choiceSetMeal}, '%')
            </if>
            <if test="param.updateTimes != null">
                AND a.UPDATE_TIMES = #{param.updateTimes}
            </if>
            <if test="param.versionHint != null">
                AND a.VERSION_HINT LIKE CONCAT('%', #{param.versionHint}, '%')
            </if>
            <if test="param.positionLimitation != null">
                AND a.POSITION_LIMITATION = #{param.positionLimitation}
            </if>
            <if test="param.livingLimitation != null">
                AND a.LIVING_LIMITATION = #{param.livingLimitation}
            </if>
            <if test="param.faceScoreLimitation != null">
                AND a.FACE_SCORE_LIMITATION = #{param.faceScoreLimitation}
            </if>
            <if test="param.cardLimitationSwitch != null">
                AND a.CARD_LIMITATION_SWITCH = #{param.cardLimitationSwitch}
            </if>
            <if test="param.courierStationSwitch != null">
                AND a.COURIER_STATION_SWITCH = #{param.courierStationSwitch}
            </if>
            <if test="param.abnormalIntegral != null">
                AND a.ABNORMAL_INTEGRAL = #{param.abnormalIntegral}
            </if>
            <if test="param.activateBpsSwitch != null">
                AND a.ACTIVATE_BPS_SWITCH = #{param.activateBpsSwitch}
            </if>
            <if test="param.referrerId != null">
                AND a.REFERRER_ID LIKE CONCAT('%', #{param.referrerId}, '%')
            </if>
            <if test="param.sendOrdersType != null">
                AND a.SEND_ORDERS_TYPE = #{param.sendOrdersType}
            </if>
            <if test="param.channelType != null">
                AND a.CHANNEL_TYPE = #{param.channelType}
            </if>
            <if test="param.userNameOrUserPhone !=null and param.userNameOrUserPhone !=''">
                and (
                a.USER_NAME like concat(concat('%',#{param.userNameOrUserPhone}),'%')
                or a.USER_PHONE = #{param.userNameOrUserPhone}
                or a.USER_SFZ = #{param.userNameOrUserPhone}
                )
            </if>
            <if test="param.schoolName !=null and param.schoolName!=''">
                and ss.school_name LIKE CONCAT('%',#{param.schoolName},'%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">
        SELECT u.*,ss.SCHOOL_NAME FROM hnsl_h5_user u
        <choose>
            <when test="param.schoolList !=null and param.schoolList.size() >0">
                right join
            </when>
            <otherwise>
                left join
            </otherwise>
        </choose>
        (select us.USER_PHONE,group_concat(s.SCHOOL_NAME) as SCHOOL_NAME from hnsl_h5_user_school us
        left join hnsl_h5_school s on us.SCHOOL_CODE=s.SCHOOL_CODE where us.`STATUS`=1
        <if test="param.schoolList !=null and param.schoolList.size() >0">
            AND us.school_code in
            <foreach collection="param.schoolList" item="schoolCode" open="(" separator="," close=")">
                #{schoolCode}
            </foreach>
        </if>
        GROUP BY us.USER_PHONE) ss
        on u.USER_PHONE=ss.USER_PHONE
        <where>
            <if test="param.userNameOrUserPhone !=null and param.userNameOrUserPhone !=''">
                (u.USER_NAME like concat(concat('%',#{param.userNameOrUserPhone}),'%') or
                u.USER_SFZ like concat(concat('%',#{param.userNameOrUserPhone}),'%') or
                u.USER_PHONE like concat(concat('%',#{param.userNameOrUserPhone}),'%'))
            </if>
            <if test="param.level==1">
                and u.STATUS_SF not in (1,5,6)
            </if>
            <if test="param.level==5">
                and u.STATUS_SF not in (5)
            </if>
            <if test="param.level==6">
                and u.STATUS_SF not in (5,6)
            </if>
            <if test="param.statusSf !=null">
                and u.STATUS_SF = #{param.statusSf}
            </if>
            <if test="param.status !=null">
                and u.STATUS = #{param.status}
            </if>
            <if test="param.cityCode !=null and param.cityCode !=''">
                and u.CITY_CODE =#{param.cityCode}
            </if>
            <if test="param.statusSuperior !=null and param.statusSuperior!=''">
                and u.status_superior =#{param.statusSuperior}
            </if>
            <if test="param.hnslChannel != null and param.hnslChannel != '' and param.hnslChannel !=1">
                and u.HNSL_CHANNEL =#{param.hnslChannel}
            </if>
            <if test="param.beginTime != null and param.beginTime.trim() != ''">
                and u.CREATED_DATE between
                str_to_date(#{param.beginTime},'%Y-%m-%d %T')and
                str_to_date(#{param.endTime},'%Y-%m-%d %T')
            </if>
            <if test="param.schoolName !=null and param.schoolName!=''">
                and ss.school_name like concat(concat('%',#{param.schoolName}),'%')
            </if>
        </where>
        order by u.ID desc
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">
        <include refid="selectSql"></include>
    </select>

    <!-- 获得对应学校的校园经理 -->
    <select id="queryManager" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">
        select t2.*
        from hnsl_h5_user_school t1
        left join hnsl_h5_user t2 on t1.user_phone = t2.user_phone
        left join hnsl_h5_school t3 on
        t3.school_code=t1.school_code
        where t2.status_sf='1'
        <if test="schoolCode!=null and schoolCode!=''">
            AND t3.school_code=#{schoolCode}
        </if>
    </select>

    <select id="querySchoolByUserPhoneTwo" resultType="int">
        select count(*)
        from (select t.* from hnsl_h5_user_school t where t.user_phone = #{phone1} and t.status = 1) t1
                 INNER JOIN
             (select t.* from hnsl_h5_user_school t where t.user_phone = #{phone2} and t.status = 1) t2
             on t1.school_code = t2.school_code
    </select>

    <select id="updateAllPhone" parameterType="java.util.HashMap" statementType="CALLABLE">
        {call updateUserPhone(
                #{v_phone,mode=IN},
                #{v_new_phone,mode=IN},
                #{v_user_name,mode=IN}
            )}
    </select>

    <update id="updateBYPhone" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">
        update HNSL_h5_ORDER
        set hhid=#{newPhone}
        where hhid = #{userPhone};
        update HNSL_h5_USER
        set user_phone=#{newPhone}
        where user_phone = #{userPhone};
        update HNSL_h5_USER
        set status_superior=#{newPhone}
        where status_superior = #{userPhone};
    </update>

    <delete id="deleteBYPhone">
        delete
        from HNSL_BUILDING
        where user_id = #{userPhone};
        delete
        from HNSL_BUILDING_USER
        where user_id = #{userPhone};
        delete
        from HNSL_NOTE
        where PHONE = #{userPhone};
        delete
        from HNSL_PERFORMANCE
        where user_id = #{userPhone};
        delete
        from HNSL_TASK_PLAN
        where USER_PHONE = #{userPhone};
        delete
        from HNSL_TASK_RECEIVE
        where USER_PHONE = #{userPhone};
        delete
        from HNSL_TASK_UPLOAD_PICTURES
        where USER_PHONE = #{userPhone};
        delete
        from HNSL_USER
        where user_phone = #{userPhone};
        delete
        from HNSL_USER
        where referrer_id = #{userPhone};
        delete
        from HNSL_USER
        where status_superior = #{userPhone};
        delete
        from HNSL_USER_BANKCARD
        where user_id = #{userPhone};
        delete
        from HNSL_USER_QQFLOCK
        where user_id = #{userPhone};
        delete
        from HNSL_USER_SCHOOL
        where user_phone = #{userPhone};
        delete
        from hnsl_team_member
        where user_code = #{userPhone};
    </delete>

    <update id="updateBySuperior" parameterType="java.util.HashMap">
        update hnsl_h5_user
        set STATUS_SUPERIOR=null
        where STATUS_SUPERIOR = #{phone}
        and STATUS_SF in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="queryListUserBy" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">

        select a.* from hnsl_h5_user a
        <if test="list !=null and list.size>0">
            left join hnsl_h5_user_school b on a.user_phone=b.user_phone
        </if>
        <if test="buildingLong !=null and buildingLong!=''">
            LEFT JOIN (select distinct user_id from hnsl_h5_building where USER_ID IS NOT NULL) d ON
            a.USER_PHONE=d.USER_ID
        </if>
        <if test="grouping !=null and grouping !=''">
            right join hnsl_h5_grouping t on t.user_id=a.user_phone
        </if>
        where a.status=1
        <if test="list !=null and list.size>0">
            and ( b.school_code IN
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">#{item}
            </foreach>
            or b.school_code IN ( select SCHOOL_ID from hnsl_h5_building where building_id IN
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            ) )
        </if>
        <if test="userName !=null and userName !=''">
            and a.USER_NAME like concat(concat('%',#{userName}),'%')
        </if>
        <if test="cityCode !=null and cityCode !=''">
            and a.CITY_CODE =#{cityCode}
        </if>
        <if test="userSfz !=null and userSfz!=''">
            and a.USER_SFZ =#{userSfz}
        </if>
        <if test="userPhone !=null and userPhone!=''">
            and a.USER_PHONE =#{userPhone}
        </if>
        <if test="level != null and level.size>0">
            and a.STATUS_SF in
            <foreach collection="level" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="buildingLong !=null and buildingLong!=''">
            and d.USER_ID IS NOT NULL
        </if>
        <if test="grouping !=null and grouping !=''">
            and t.grouping_code=#{grouping}
        </if>
    </select>

    <update id="updateUserByIntegral" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">
        update hnsl_h5_user
        set INTEGRAL =INTEGRAL + #{integral}
        where USER_PHONE = #{userPhone}
          and STATUS = 1
    </update>

    <!-- 查询合伙人所有下级信息包括是否是楼栋长-->
    <select id="queryALLStatusSuperior" resultType="java.util.HashMap">
        select t.USER_PHONE,t.USER_NAME,t.STATUS_SUPERIOR,t.SL
        <if test="type == 2 ">
            ,t.integral as INTEGRALS
        </if>
        <if test="type == 1 ">
            ,t.monthintegral as INTEGRALS
        </if>
        from (
        select au.* from (select t.*,t1.sl
        <if test="type == 1 ">
            ,t2.monthintegral
        </if>
        from hnsl_h5_user t left join
        (select t1.user_id,count(*) as sl from hnsl_h5_building_user t1 where t1.status=1 group by t1.user_id ) t1 on
        t1.user_id=t.user_phone
        <if test="type == 1 ">
            left join (select t.user_id,sum(t.integral_operation) as monthintegral from hnsl_h5_integral t where 1=1
            <if test="beginTime != null and beginTime.trim() != ''">
                and t.created_date between
                str_to_date(#{beginTime},'%Y-%m-%d %T')and
                str_to_date(#{endTime},'%Y-%m-%d %T')
            </if>
            <if test="beginTime == null or beginTime == '' ">
                and date_format(t.created_date,'%Y-%m')=date_format(sysdate(),'%Y-%m')
            </if>
            group by t.user_id) t2
            on t2.user_id=t.user_phone
        </if>
        ) au,
        (select @pid := (select GROUP_CONCAT(a.USER_PHONE SEPARATOR ',')
        from (select *,'1' as type from hnsl_h5_user
        where STATUS=1 and user_phone=#{userPhone}) a GROUP BY a.type)) pd
        where FIND_IN_SET(au.STATUS_SUPERIOR,@pid)>0 AND @pid :=CONCAT(@pid,',',au.USER_PHONE)
        ) t
        where t.status=1
        <if test="type == 2 ">
            and t.user_phone !=#{userPhone}
        </if>
    </select>

    <select id="queryExportUserList" resultType="java.util.Map">
        SELECT
        u.CITY_CODE,
        sc.SCHOOL_NAME,
        us.SCHOOL_CODE,
        u.USER_NAME,
        u.USER_PHONE,
        u.USER_SFZ,
        u.USER_SITE,
        u.STATUS_SF,
        u.SALES_CODE,
        u.STATUS_SUPERIOR,
        u1.USER_NAME AS NAME_SUPERIOR,
        u2.USER_NAME AS MANAGER_NAME,
        u1.STATUS_SUPERIOR AS MANAGER_SUPERIOR,
        u.CHANNEL_TYPE,
        u.HNSL_CHANNEL,
        u.STATUS,
        u.CREATED_DATE
        FROM hnsl_h5_user u
        LEFT JOIN hnsl_h5_user_school us ON u.USER_PHONE = us.USER_PHONE
        LEFT JOIN hnsl_h5_school sc ON us.SCHOOL_CODE = sc.SCHOOL_CODE
        LEFT JOIN hnsl_h5_user u1 ON u.STATUS_SUPERIOR = u1.USER_PHONE
        LEFT JOIN hnsl_h5_user u2 ON u1.STATUS_SUPERIOR = u2.USER_PHONE
        where 1=1
        <if test="schoolCode !=null and schoolCode.size > 0">
            and sc.SCHOOL_CODE IN
            <foreach item="id" collection="schoolCode" open="(" separator=","
                     close=")">
                #{id}
            </foreach>
        </if>
        <if test="schoolName !=null and schoolName !=''">
            and sc.SCHOOL_NAME like concat('%', #{schoolName} ,'%')
        </if>
        <if test="userNameOrUserPhone !=null and userNameOrUserPhone !=''">
            and (u.USER_NAME like concat('%', #{userNameOrUserPhone} ,'%') or
            u.USER_SFZ like concat('%', #{userNameOrUserPhone} ,'%') or
            u.USER_PHONE like concat('%', #{userNameOrUserPhone} ,'%'))
        </if>
        <if test="statusSf !=null and statusSf!=''">
            and u.STATUS_SF = #{statusSf}
        </if>
        <if test="status !=null and status !=''">
            and u.STATUS = #{status}
        </if>
        <if test="cityCode !=null and cityCode !=''">
            and u.CITY_CODE =#{cityCode}
        </if>
        <if test="numbers !=null and numbers!=''">
            and u.NUMBERS =#{numbers}
        </if>
        <if test="beginTime != null and beginTime.trim() != ''">
            and u.CREATED_DATE between
            str_to_date(#{beginTime},'%Y-%m-%d %T')and
            str_to_date(#{endTime},'%Y-%m-%d %T')
        </if>
        <!-- 根据登陆用户身份权限查询身份以下数据 -->
        <if test="loginUserSf==1">
            and (u.status_sf != 1 and u.status_sf != 5 and u.status_sf != 6)
        </if>
        <if test="loginUserSf==5">
            and (u.status_sf != 5)
        </if>
        <if test="loginUserSf == 6">
            and (u.status_sf != 5 and u.status_sf != 6)
        </if>
    </select>

    <!--    导出用户20210310需求积分表-->
    <select id="queryExportUserIntegral" resultType="java.util.HashMap">
        SELECT T.USER_NAME,T.USER_PHONE,T.USER_SFZ,DATE_FORMAT(T.CREATED_DATE,'%Y-%m-%d %H:%i:%S') AS CREATED_DATES,
        T.CITY_CODE,S2.SCHOOL_NAME,T.STATUS_SF,T1.USER_NAME AS SUPERIOR_NAME,T1.USER_PHONE AS SUPERIOR_PHONE,T.STATUS,
        T.NUMBERS,T.SALES_CODE,
        IFNULL(B1.LDZ,0) AS LDC,I1.QT,O1.S1,O1.S1F,O1.S2,O1.S2F,O1.S3,O1.S3F,O1.S4,O1.S4F,O1.S5,O1.S5F,I1.S6,I1.S6F,
        (IFNULL(O1.S1,0) +IFNULL(O1.S2,0)+IFNULL(O1.S3,0)+IFNULL(O1.S4,0)+IFNULL(O1.S5,0)+IFNULL(I1.S6,0) ) AS ZJ,
        (IFNULL(O1.S1F,0) +IFNULL(O1.S2F,0)+IFNULL(O1.S3F,0)+IFNULL(O1.S4F,0)+IFNULL(O1.S5F,0)+IFNULL(I1.S6F,0) ) AS ZJF
        FROM hnsl_h5_user T LEFT JOIN (SELECT T.USER_PHONE,GROUP_CONCAT(S2.SCHOOL_NAME,',') AS SCHOOL_NAME FROM
        HNSL_USER T
        LEFT JOIN hnsl_h5_user_school S1
        ON T.USER_PHONE=S1.USER_PHONE LEFT JOIN HNSL_SCHOOL S2 ON S1.SCHOOL_CODE=S2.SCHOOL_CODE GROUP BY T.USER_PHONE )
        S2 ON T.USER_PHONE=S2.USER_PHONE
        LEFT JOIN hnsl_h5_user T1 ON T.STATUS_SUPERIOR=T1.USER_PHONE
        LEFT JOIN (SELECT B1.USER_ID,COUNT(*) AS LDZ FROM hnsl_h5_building_user B1 WHERE B1.STATUS=1 GROUP BY
        B1.USER_ID)
        B1 ON T.USER_PHONE=B1.USER_ID
        LEFT JOIN (SELECT T.HHID,SUM(CASE T.SAFL_TYPE WHEN 1 THEN 1 WHEN 6 THEN 1 ELSE 0 END) AS S1,
        SUM(CASE T.SAFL_TYPE WHEN 1 THEN IFNULL(CAST(G1.GOOD_INTEGRAL AS UNSIGNED INT),0) WHEN 6 THEN
        IFNULL(CAST(G1.GOOD_INTEGRAL AS UNSIGNED INT),0) ELSE 0 END) AS S1F,
        SUM(CASE T.SAFL_TYPE WHEN 2 THEN 1 WHEN 3 THEN 1 ELSE 0 END) AS S2,
        SUM(CASE T.SAFL_TYPE WHEN 2 THEN IFNULL(CAST(G1.GOOD_INTEGRAL AS UNSIGNED INT),0) WHEN 3 THEN
        IFNULL(CAST(G1.GOOD_INTEGRAL AS UNSIGNED INT),0) ELSE 0 END) AS S2F,
        SUM(CASE T.SAFL_TYPE WHEN 8 THEN 1 WHEN 9 THEN 1 ELSE 0 END) AS S3,
        SUM(CASE T.SAFL_TYPE WHEN 8 THEN IFNULL(I1.INTEGRAL_OPERATION,0) WHEN 9 THEN IFNULL(I1.INTEGRAL_OPERATION,0)
        ELSE 0 END) AS S3F,
        SUM(CASE WHEN T.SAFL_TYPE=4 AND T.GOODS_NUMBER!='9015088' AND T.GOODS_NUMBER!='9015084' THEN 1 ELSE 0 END) AS
        S4,
        SUM(CASE WHEN T.SAFL_TYPE=4 AND T.GOODS_NUMBER!='9015088' AND T.GOODS_NUMBER!='9015084' THEN
        IFNULL(CAST(G1.GOOD_INTEGRAL AS UNSIGNED INT),0) ELSE 0 END) AS S4F,
        SUM(CASE T.GOODS_NUMBER WHEN '9015088' THEN 1 WHEN '9015084' THEN 1 ELSE 0 END) AS S5,
        SUM(CASE T.GOODS_NUMBER WHEN '9015088' THEN IFNULL(CAST(G1.GOOD_INTEGRAL AS UNSIGNED INT),0) WHEN '9015084' THEN
        IFNULL(CAST(G1.GOOD_INTEGRAL AS UNSIGNED INT),0) ELSE 0 END) AS S5F
        FROM hnsl_h5_order T LEFT JOIN hnsl_h5_goods G1 ON T.GOODS_NUMBER=G1.GOODS_NUMBER
        LEFT JOIN (SELECT DISTINCT T.ORDER_ID,T.INTEGRAL_OPERATION FROM hnsl_h5_integral T) I1 ON T.ORDER_ID=I1.ORDER_ID
        WHERE T.ORDER_STATUS IN(3,15,23)
        <if test="beginTime != null and beginTime.trim() != ''">
            and T.ORDER_ACTIVATE_DATE between
            str_to_date(#{beginTime},'%Y-%m-%d %T')and
            str_to_date(#{endTime},'%Y-%m-%d %T')
        </if>
        GROUP BY T.HHID ) O1 ON T.USER_PHONE=O1.HHID
        LEFT JOIN (SELECT T.USER_ID,SUM(CASE WHEN T.INTEGRAL_TYPE!=8 THEN T.INTEGRAL_TYPE ELSE 0 END) AS QT,
        SUM(CASE T.INTEGRAL_TYPE WHEN 8 THEN 1 ELSE 0 END) AS S6,
        SUM(CASE T.INTEGRAL_TYPE WHEN 8 THEN T.INTEGRAL_OPERATION ELSE 0 END) AS S6F
        FROM hnsl_h5_integral T WHERE T.INTEGRAL_TYPE IN (3,4,5,6,8,9)
        <if test="beginTime != null and beginTime.trim() != ''">
            and T.created_date between
            str_to_date(#{beginTime},'%Y-%m-%d %T')-5 and
            str_to_date(#{endTime},'%Y-%m-%d %T')+5
        </if>
        GROUP BY T.USER_ID) I1
        ON T.USER_PHONE=I1.USER_ID
        WHERE T.STATUS=1 AND T.STATUS_SF IN (1,2,3,4)
    </select>

    <select id="queryListUserRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">

        select a.* from hnsl_h5_user a
        <if test="param.list !=null and param.list.size>0">
            left join hnsl_h5_user_school b on a.user_phone=b.user_phone
        </if>
        <if test="param.buildingLong !=null and param.buildingLong!=''">
            LEFT JOIN (select distinct user_id from hnsl_h5_building where USER_ID IS NOT NULL) d ON
            a.USER_PHONE=d.USER_ID
        </if>
        <if test="param.grouping !=null and param.grouping !=''">
            right join hnsl_h5_grouping t on t.user_id=a.user_phone
        </if>
        where a.status=1
        <if test="param.list !=null and param.list.size>0">
            and ( b.school_code IN
            <foreach collection="param.list" index="index" item="item" open="(" separator="," close=")">#{item}
            </foreach>
            or b.school_code IN ( select SCHOOL_ID from hnsl_h5_building where building_id IN
            <foreach collection="param.list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            ) )
        </if>
        <if test="param.userName !=null and param.userName !=''">
            and a.USER_NAME like concat(concat('%',#{param.userName}),'%')
        </if>
        <if test="param.cityCode !=null and param.cityCode !=''">
            and a.CITY_CODE =#{param.cityCode}
        </if>
        <if test="param.userSfz !=null and param.userSfz!=''">
            and a.USER_SFZ =#{param.userSfz}
        </if>
        <if test="param.userPhone !=null and param.userPhone!=''">
            and a.USER_PHONE =#{param.userPhone}
        </if>
        <if test="param.level != null and param.level.size>0">
            and a.STATUS_SF in
            <foreach collection="level" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.buildingLong !=null and param.buildingLong!=''">
            and d.USER_ID IS NOT NULL
        </if>
        <if test="param.grouping !=null and param.grouping !=''">
            and t.grouping_code=#{param.grouping}
        </if>
    </select>

    <!-- 根据当前用户手机号码查询能够导出的校园编码 -->
    <select id="querySchoolCodeByPhone" resultType="java.lang.String">
        select t.school_code AS schoolCode
        from (select s.school_code from HNSL_H5_USER_SCHOOL s where s.user_phone = #{userPhone}) t
    </select>

    <update id="updateByUser" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">
        update hnsl_h5_user
        <set>
            <if test="userName != null and userName != ''">USER_NAME = #{userName},</if>
            <if test="userSite != null and userSite != ''">USER_SITE = #{userSite},</if>
            <if test="statusSf != null and statusSf != ''">STATUS_SF = #{statusSf},</if>
            <if test="userSfz != null and userSfz != ''">USER_SFZ = #{userSfz},</if>
            <if test="userPhone != null and userPhone != ''">USER_PHONE = #{userPhone},</if>
            <if test="statusSuperior != null">STATUS_SUPERIOR = #{statusSuperior},</if>
            <if test="channelType != 0">CHANNEL_TYPE = #{channelType},</if>
            <if test="hnslChannel != null and hnslChannel != 0">HNSL_CHANNEL = #{hnslChannel},</if>
            <if test="updatedDate != null">UPDATED_DATE = #{updatedDate},</if>
            <if test="updatedUser != null and updatedUser != ''">UPDATED_USER = #{updatedUser},</if>
            <if test="createdDate != null">CREATED_DATE = #{createdDate},</if>
            <if test="cityCode != null and cityCode != ''">CITY_CODE = #{cityCode},</if>
            <if test="createdUser != null and createdUser != ''">CREATED_USER = #{createdUser},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="updateTimes != 0">UPDATE_TIMES = #{updateTimes},</if>
            <if test="salesCode != null and salesCode != ''">SALES_CODE = #{salesCode},</if>
            <if test="selCityCode != null and selCityCode != ''">SEL_CITY_CODE = #{selCityCode}</if>
        </set>
        where ID = #{id}
    </update>

    <select id="queryManagerList" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">
        SELECT tt.*
        FROM (
        select * from hnsl_h5_user WHERE STATUS=1
        <if test="statusSf!=null and statusSf!='' ">
            AND STATUS_SF =#{statusSf}
        </if>
        <if test="userName!='' and userName!=null">
            AND USER_NAME like concat(concat('%',#{userName}),'%')
        </if>
        <if test="userSfz!='' and userSfz!=null">
            AND USER_SFZ=#{userSfz}
        </if>
        <if test="userPhone !='' and userPhone!=null">
            AND USER_PHONE=#{userPhone}
        </if>
        <if test="statusSuperior !='' and statusSuperior!=null">
            AND STATUS_SUPERIOR=#{statusSuperior}
        </if>
        <if test="hnslChannel !=null and hnslChannel!='' and hnslChannel!=1">
            and HNSL_CHANNEL =#{hnslChannel}
        </if>
        order by ID desc
        ) tt
    </select>

    <select id="queryList" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User">
        SELECT u.*,ss.SCHOOL_NAME FROM hnsl_h5_user u
        <choose>
            <when test="schoolList !=null and schoolList.size() >0">
                right join
            </when>
            <otherwise>
                left join
            </otherwise>
        </choose>
        (select us.USER_PHONE,group_concat(s.SCHOOL_NAME) as SCHOOL_NAME from hnsl_h5_user_school us
        left join hnsl_h5_school s on us.SCHOOL_CODE=s.SCHOOL_CODE where us.`STATUS`=1
        <if test="schoolList !=null and schoolList.size() >0">
            AND us.school_code in
            <foreach collection="schoolList" item="schoolCode" open="(" separator="," close=")">
                #{schoolCode}
            </foreach>
        </if>
        GROUP BY us.USER_PHONE) ss
        on u.USER_PHONE=ss.USER_PHONE
        <where>
            <if test="userName !=null and userName !=''">
                and u.USER_NAME like concat(concat('%',#{userName}),'%')
            </if>
            <if test="userManager!=null and userManager!=''">
                and (u.USER_MANGER is null or u.USER_MANGER=#{userManager})
            </if>
            <if test="condition !=null and condition !=''">
                and (u.USER_NAME like concat(concat('%',#{condition}),'%') or
                u.USER_SFZ like concat(concat('%',#{condition}),'%') or
                u.USER_PHONE like concat(concat('%',#{condition}),'%'))
            </if>
            <if test="statusSf !=null and statusSf!=''">
                and u.STATUS_SF = #{statusSf}
            </if>
            <if test="level==1">
                and u.STATUS_SF not in (1,5,6)
            </if>
            <if test="level==5">
                and u.STATUS_SF not in (5)
            </if>
            <if test="level==6">
                and u.STATUS_SF not in (5,6)
            </if>
            <if test="status !=null and status !=''">
                and u.STATUS = #{status}
            </if>
            <if test="cityCode !=null and cityCode !=''">
                and u.CITY_CODE =#{cityCode}
            </if>
            <if test="numbers !=null and numbers!=''">
                and u.NUMBERS =#{numbers}
            </if>
            <if test="userSfz !=null and userSfz!=''">
                and u.USER_SFZ =#{userSfz}
            </if>
            <if test="userPhone !=null and userPhone!=''">
                and u.USER_PHONE =#{userPhone}
            </if>
            <if test="statusSuperior !=null and statusSuperior!=''">
                and u.status_superior =#{statusSuperior}
            </if>
            <if test="hnslChannel !=null and hnslChannel!=''  and hnslChannel!=1 ">
                and u.HNSL_CHANNEL =#{hnslChannel}
            </if>
            <if test="beginTime != null and beginTime.trim() != ''">
                and u.CREATED_DATE between
                str_to_date(#{beginTime},'%Y-%m-%d %T')and
                str_to_date(#{endTime},'%Y-%m-%d %T')
            </if>
            <if test="schoolName !=null and schoolName!=''">
                and ss.school_name like concat(concat('%',#{schoolName}),'%')
            </if>
        </where>
        order by u.ID desc
    </select>
</mapper>
