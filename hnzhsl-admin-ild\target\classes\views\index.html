<html xmlns:th="http://www.thymeleaf.org">
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>湖南智慧扫楼管理后台</title>
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico">
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <link rel="stylesheet" href="css/font-awesome.bootstrap.min.css">
    <link rel="stylesheet" href="css/AdminLTE.min.css">
    <!-- AdminLTE Skins. Choose a skin from the css/skins
         folder instead of downloading all of them to reduce the load. -->
    <link rel="stylesheet" href="css/all-skins.min.css">
    <link rel="stylesheet" href="css/main.css">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

    <style type="text/css">
        .modal-dialog {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }
        .modal-content {
            width: 500px;
            height: 200px;
            position: relative;
            text-align: center;
            padding: 20px;
        }
        .modal-footer {
            position: absolute;
            bottom: 0;
            right: 10px;
        }
    </style>
</head>
<!-- ADD THE CLASS layout-boxed TO GET A BOXED LAYOUT -->
<body class="hold-transition skin-blue sidebar-mini">
<!-- Site wrapper -->
<div class="wrapper" id="rrapp" v-cloak>
    <header class="main-header">
        <a href="javascript:;" class="logo">
            <!-- mini logo for sidebar mini 50x50 pixels -->
            <span class="logo-mini"><b>hnsd</b></span>
            <!-- logo for regular state and mobile devices -->
            <span class="logo-lg"><b>湖南智慧扫楼管理后台</b></span>
        </a>
        <!-- Header Navbar: style can be found in header.less -->
        <nav class="navbar navbar-static-top" role="navigation">
            <!-- Sidebar toggle button-->
            <a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
                <span class="sr-only">Toggle navigation</span>
            </a>
            <div style="float:left;color:#fff;padding:15px 10px;">欢迎&nbsp;&nbsp;&nbsp;{{userName}}{{statusSf}}</div>
            <div class="navbar-custom-menu">
                <ul class="nav navbar-nav">
                    <li>
                        <a href="#modules/sys/sysmsg.html">
                            <i class="fa fa-bell-o"></i>
                            <span class="label label-warning">{{msgcount}}</span>
                        </a>
                    </li>
                    <li><a href="javascript:;" @click="extension"><i class="fa fa-edit"></i> &nbsp;账号延期</a></li>
<!--                    <li><a href="javascript:;" @click="updatePassword"><i class="fa fa-lock"></i> &nbsp;修改密码</a>-->
                    </li>
                    <li><a href="javascript:;" @click="logout"><i class="fa fa-sign-out"></i> &nbsp;退出系统</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- =============================================== -->

    <!-- Left side column. contains the sidebar -->
    <aside class="main-sidebar">
        <!-- sidebar: style can be found in sidebar.less -->
        <section class="sidebar">
            <!-- /.search form -->
            <!-- sidebar menu: : style can be found in sidebar.less -->
            <ul class="sidebar-menu">
                <li class="header">导航菜单</li>

                <!-- vue生成的菜单 -->
                <menu-item :item="item" v-for="item in menuList"></menu-item>
            </ul>
        </section>
        <!-- /.sidebar -->
    </aside>
    <!-- =============================================== -->
    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <ol class="breadcrumb" id="nav_title" style="position:static;float:none;">
                <li class="active"><i class="fa fa-home"
                                      style="font-size:20px;position:relative;top:2px;left:-3px;"></i> &nbsp; 首页
                </li>
                <li class="active">{{navTitle}}</li>
            </ol>
        </section>

        <!-- Main content -->
        <section class="content" style="background:#fff;">
            <iframe scrolling="yes" frameborder="0"
                    style="width:100%;min-height:200px;overflow:visible;background:#fff;" :src="main"></iframe>
        </section>
        <!-- /.content -->
    </div>
    <!-- /.content-wrapper -->

    <footer class="main-footer">
        <div class="pull-right hidden-xs">
            Version 1.0.0
        </div>
        Copyright &copy; 2017 <a href="http://wx.hn.189.cn" target="_blank">湖南智慧扫楼管理后台</a> All Rights Reserved
        <a href="https://beian.miit.gov.cn" target="_blank">ICP 证号:京 ICP 备 12007914号</a>
    </footer>

    <!-- Add the sidebar's background. This div must be placed
         immediately after the control sidebar -->
    <div class="control-sidebar-bg"></div>

    <!-- 修改密码 -->
    <div id="passwordLayer" style="display: none;">
        <form class="form-horizontal">
            <div class="form-group" style="margin-left: -2%;">
                <div class="form-group" style="margin-left: -2%;">
                    <div class="col-md-2 control-label">账号</div>
                    <span class="label label-success" style="vertical-align: bottom;">{{user.username}}</span>
                </div>
                <div class="form-group" style="margin-left: -2%;">
                    <div class="col-md-2 control-label">原密码</div>
                    <div class="col-md-10">
                        <input type="password" class="form-control" v-model="password" placeholder="原密码"/>
                    </div>
                </div>
                <div class="form-group" style="margin-left: -2%;">
                    <div class="col-md-2 control-label">新密码</div>
                    <div class="col-md-10">
                        <input type="password" class="form-control" v-model="newPassword" placeholder="新密码"/>
                    </div>
                </div>
            </div>
        </form>
        <div id="extensionLayer" style="display: none;">
            <form class="form-horizontal">
                <div class="form-group">
                    <div class="form-group">
                        <div class="col-sm-2 control-label">手机号</div>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" v-model="mobile" placeholder="手机号"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-2 control-label">身份证</div>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" v-model="cardsid" placeholder="身份证"/>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 商秘下载提示 -->
<!--    <div class="modal"  id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="display: block;">-->
<!--        <div class="modal-dialog">-->
<!--            <div class="modal-content">-->
<!--                <h4 style="padding: 15px">-->
<!--                    本系统下载的文件须在打开《商秘文档安全客户端》下才能正常打开，否则会存在无法解密打开的情况，请知悉！ 客户端下载地址：-->
<!--                    <a target="_blank" href="https://portal1.hnx.ctc.com:37808/noLoginTools">https://portal1.hnx.ctc.com:37808/noLoginTools</a>-->
<!--                </h4>-->
<!--                <div class="modal-footer">-->
<!--                    <a class="btn btn-primary" style="height: 30px;background-color: #1E9FFF" @click="closeModal">确定</a>-->
<!--                </div>-->
<!--            </div>&lt;!&ndash; /.modal-content &ndash;&gt;-->
<!--        </div>&lt;!&ndash; /.modal-dialog &ndash;&gt;-->
<!--    </div>&lt;!&ndash; /.modal &ndash;&gt;-->

</div>
<!-- ./wrapper -->
<script type="text/javascript" th:inline="javascript">
    var data = [[${data}]]
    console.log(data);
    localStorage.setItem("token", data.token);
    localStorage.setItem("loginStatus", "ok");
    window.document.cookie="token="+data.token;
    window.permissions = data.permissions;
</script>
<script src="libs/jquery.min.js"></script>

<!-- select2组件 -->
<script src="plugins/select2/js/select2.min.js"></script>
<script src="plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="libs/vue.min.js"></script>
<script src="libs/router.js"></script>
<script src="libs/bootstrap.min.js"></script>
<script src="libs/app.js"></script>
<script src="plugins/layer/layer.js"></script>
<script src="js/common.js"></script>
<script type="text/javascript" src="common/js/jsbn.js"></script>
<script type="text/javascript" src="common/js/prng4.js"></script>
<script type="text/javascript" src="common/js/rng.js"></script>
<script type="text/javascript" src="common/js/rsa.js"></script>
<script type="text/javascript" src="common/js/base64.js"></script>
<script src="js/index.js"></script>
<script src="js/warterMarkJS.js"></script>
<script>
    $(function () {
        var loginStatus = localStorage.getItem("loginStatus");
        //  console.info(loginStatus);
        //  layer.confirm('您的账号已在别处登录，即将强制下线',{ btn:['确定']},function (index) {})
        if (loginStatus === "ok") {
            $.ajax({
                type: "POST",
                url: baseURL + "hnslorder/remindAdmin",
                success: function (r) {
                    if (r.code === 0) {
                        if (r.msg != null && r.msg !== '') {
                            layer.confirm(r.msg, {
                                btn: ['处理']
                            }, function (index) {
                                localStorage.removeItem("loginStatus");
                                window.location.href = '#modules/hnsl/hnslorder.html';
                                layer.close(index);
                            })
                        }
                    }
                }
            })
        }
    });
</script>
</body>
</html>
