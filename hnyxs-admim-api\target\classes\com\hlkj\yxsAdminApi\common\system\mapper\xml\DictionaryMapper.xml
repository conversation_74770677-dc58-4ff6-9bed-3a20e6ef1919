<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.common.system.mapper.DictionaryMapper">

    <resultMap id="dictionaryMap" type="com.hlkj.yxsAdminApi.common.system.entity.Dictionary">
        <result column="id" property="dictId"/>
        <result column="dict_code" property="dictCode"/>
        <result column="dict_name" property="dictName"/>
        <result column="show_contents" property="showContents"/>
        <result column="sort_number" property="sortNumber"/>
        <result column="comments" property="comments"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <collection  property="dictionaryData" ofType="com.hlkj.yxsAdminApi.common.system.entity.DictionaryData" javaType="ArrayList">
            <id column="dict_data_id" property="dictDataId"/>
            <result column="dict_id" property="dictId"/>
            <result column="dict_data_code" property="dictDataCode"/>
            <result column="dict_data_name" property="dictDataName"/>
            <result column="sort_number" property="sortNumber"/>
            <result column="data_comments" property="comments"/>
            <result column="deleted" property="deleted"/>
            <result column="create_time" property="createTime"/>
            <result column="update_time" property="updateTime"/>
        </collection >
    </resultMap>

    <select id="queryDictionary" resultMap="dictionaryMap" parameterType="com.hlkj.yxsAdminApi.common.system.param.DictionaryParam">
        select *,b.comments as data_comments from hnyxs_sys_dictionary a
        left join hnyxs_sys_dictionary_data b on a.dict_id = b.dict_id
        <where>
            a.deleted = 0 and b.deleted = 0
            <if test="param.dictCode != null">
                and dict_code LIKE CONCAT('%', #{param.dictCode}, '%')
            </if>
        </where>
    </select>
</mapper>
