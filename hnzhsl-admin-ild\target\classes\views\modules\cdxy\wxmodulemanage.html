<!DOCTYPE html>
<html>
<head>
<title>模块管理</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<link rel="stylesheet" href="../../css/AdminLTE.min.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->


<script src="../../libs/ajaxfileupload.js"></script>
<script src="../../libs/jquery.form.js"></script>

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
	<div class="row" >
		<div class="col-md-12"  >
			<div class="nav-tabs-custom">
	            <ul class="nav nav-tabs">
	              <li class="active"  @click="changeLi(1)" ><a href="#tab_1" data-toggle="tab" aria-expanded="false">更多查询模块</a></li>
	              <li class=""  @click="changeLi(2)" ><a href="#tab_2" data-toggle="tab" aria-expanded="false">更多办理模块</a></li>
	              <li class="" @click="changeLi(3)" ><a href="#tab_3" data-toggle="tab" aria-expanded="true">宽带模块</a></li>
				  <li class="" @click="changeLi(4)" ><a href="#tab_4" data-toggle="tab" aria-expanded="true">个人中心单移模块</a></li>
					<li class="" @click="changeLi(5)" ><a href="#tab_5" data-toggle="tab" aria-expanded="true">个人中心单宽模块</a></li>
					<li class="" @click="changeLi(6)" ><a href="#tab_6" data-toggle="tab" aria-expanded="true">个人中心其他用户模块</a></li>
					<li class="" @click="changeLi(7)" ><a href="#tab_7" data-toggle="tab" aria-expanded="true">个人中心融合用户模块</a></li>
					<li class="" @click="changeLi(8)" ><a href="#tab_8" data-toggle="tab" aria-expanded="true">个人中心更多模块上</a></li>
					<li class="" @click="changeLi(9)" ><a href="#tab_9" data-toggle="tab" aria-expanded="true">个人中心更多模块下</a></li>
	            </ul>
	            <div class="tab-content">
		              <div class="row" style="background-color: rgb(241, 242, 247)">
		              	<div class="col-md-4" >
              <!-- USERS LIST -->
              <div class="box " >
                <div class="box-header with-border text-center">
<!--                   <h3 class="box-title">Latest Members</h3> -->
					<a v-if="hasPermission('wxmodulemanage:save')" class="uppercase btn " @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
<!--                   <div class="box-tools pull-right"> -->
<!--                     <span class="label label-danger">8 New Members</span> -->
<!--                     <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i> -->
<!--                     </button> -->
<!--                     <button type="button" class="btn btn-box-tool" data-widget="remove"><i class="fa fa-times"></i> -->
<!--                     </button> -->
<!--                   </div> -->
                </div>
                <!-- /.box-header -->
                <div class="box-body no-padding" >
                  <ul class="users-list clearfix">
                    <li style="width:33.3%;cursor:pointer;" @click="update(wxModule.id)" v-if="wxModuleList !=null && wxModuleList.length>0" v-for="(wxModule,index) in wxModuleList">
	                    <div class="box-tools pull-right" style="background-color: aliceblue">
	                    	<button type="button" class="btn btn-box-tool" data-widget="remove" @click="del(wxModule.id)"><i class="fa fa-times"></i></button>
	                    </div>
                      <img :src="wxModule.imgUrl" :alt="wxModule.name" style="border: 1px solid #f7f4f9;height: 100px"  >
                      <a class="users-list-name" href="#">{{wxModule.name}}</a>
                      <span class="users-list-date"><a href="#">
                      	<i :class="(wxModule.state==1) ? 'fa fa-circle text-success':'fa fa-circle text-danger' "></i>
                      	</a></span>
                    </li>
                  </ul>
                  <!-- /.users-list -->
                </div>
                <!-- /.box-body -->
<!--                 <div class="box-footer text-center"> -->
<!--                   <a href="javascript:void(0)" class="uppercase">View All Users</a> -->
<!--                 </div> -->
                <!-- /.box-footer -->
              </div>
              <!--/.box -->
            </div>
            <div class="col-md-8" v-show="!showList">
            <div class="panel panel-default">
<!-- 		<div class="panel-heading">{{title}}</div> -->
		<form class="form-horizontal">
			<div class="form-group">
			   	<div class="col-sm-2 control-label">菜单名称</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="wxModuleManage.name" placeholder="菜单名称，不能超过6个字符"/>
			    </div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">排序</div>
			   	<div class="col-sm-10">
			      <input type="number" class="form-control" min="0" size="1"  v-model="wxModuleManage.orders" placeholder="排序,默认为0"/>
			    </div>
			</div>
<!-- 			<div class="form-group"> -->
<!-- 			   	<div class="col-sm-2 control-label">图片</div> -->
<!-- 			   	<div class="col-sm-10"> -->
<!-- 			      <input type="text" class="form-control" v-model="wxModuleManage.imgUrl" placeholder="图片地址链接"/> -->
<!-- 			    </div> -->
<!-- 			</div> -->
			<div class="form-group">
				<div class="col-sm-2 control-label">图片</div>
				<div class="col-sm-10">
				<div class=" btn btn-default btn-file" style="text-align: center;width:100%">
					<img alt=""  :src="wxModuleManage.imgUrl"  style="height: 200px;"/> 
					<input type="hidden" id="picimageHidden" name="picimageHidden" v-model="wxModuleManage.imgUrl"> 
					<input type="file" id="picimage" name="picimage" value=""  @change="fileChange"> 
					<i class="fa fa-paperclip"></i> 选择图片
                </div>
                	 <p class="help-block" style="color: orange;">建议尺寸：900×500像素,不超过2M</p>
				</div>
             </div>
<!-- 			<div class="form-group"> -->
<!-- 			   	<div class="col-sm-2 control-label">1,更多查询模块管理，2更多办理模块管理，3宽带模块管理</div> -->
<!-- 			   	<div class="col-sm-10"> -->
<!-- 			      <input type="text" class="form-control" v-model="wxModuleManage.modeltype" placeholder="1,更多查询模块管理，2更多办理模块管理，3宽带模块管理"/> -->
<!-- 			    </div> -->
<!-- 			</div> -->
			<div class="form-group">
				<div class="col-sm-2 control-label">图标svg地址</div>
				<div class="col-sm-10">
					<input type="text" class="form-control" v-model="wxModuleManage.svgUrl" placeholder="图标svg地址"/>
				</div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">跳转地址</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="wxModuleManage.redirectUri" placeholder="跳转地址链接"/>
			    </div>
			</div>
<!-- 			<div class="form-group"> -->
<!-- 			   	<div class="col-sm-2 control-label">状态</div> -->
<!-- 			   	<div class="col-sm-10"> -->
<!-- 			      <input type="text" class="form-control" v-model="wxModuleManage.state" placeholder="状态，1显示，2不显示"/> -->
<!-- 			    </div> -->
<!-- 			</div> -->
			<div class="form-group">
			   	<div class="col-sm-2 control-label">状态</div>
			   	<label class="radio-inline">
					<input type="radio" name="state" value="1" v-model="wxModuleManage.state"/> 显示
				</label>
				<label class="radio-inline">
					<input type="radio" name="state" value="2" v-model="wxModuleManage.state"/> 不显示
				</label>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">是否免登陆</div>
				<label class="radio-inline">
					<input type="radio" name="isnologin" value="0" v-model="wxModuleManage.isnologin"/> 否
				</label>
			   	<label class="radio-inline">
					<input type="radio" name="isnologin" value="1" v-model="wxModuleManage.isnologin"/> 是
				</label>
			</div>
			<div class="form-group" v-if="wxModuleManage.isnologin==1">
			   	<div class="col-sm-2 control-label">加密字段</div>
		   	  	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="wxModuleManage.encryptionField" placeholder="加密字段，phone,mobile,openid等"/>
			    </div>
			</div>
			<div class="form-group" v-if="wxModuleManage.isnologin==1">
			   	<div class="col-sm-2 control-label">加密方式</div>
			   	<div class="col-sm-10">
<!-- 			      <input type="text" class="form-control" v-model="wxModuleManage.encryptionMode" placeholder="加密字段，只支持MD5,DES,AES三种"/> -->
				   	<label class="radio-inline">
						<input type="radio" name="encryptionMode"  value="DES" v-model="wxModuleManage.encryptionMode"/> DES
					</label>
					<label class="radio-inline">
						<input type="radio" name="encryptionMode" value="AES" v-model="wxModuleManage.encryptionMode"/> AES
					</label>
					<label class="radio-inline">
						<input type="radio" name="encryptionMode" value="MD5" v-model="wxModuleManage.encryptionMode"/> MD5
					</label>
					 <p class="help-block" style="color: orange;">注:加密秘钥咨询开发人员，由开发人员统一定制</p>
				 </div>
			</div>
			<div class="box-footer text-left">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="保存"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="取消"/>
			</div>
		</form>
	</div>
	</div>
	             </div>
	             </div>
	          </div>
	      </div>
      </div>
<!-- 	<div v-show="showList"> -->
<!-- 		<div class="grid-btn"> -->
<!-- 			<a v-if="hasPermission('wxmodulemanage:save')" class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a> -->
<!-- 			<a v-if="hasPermission('wxmodulemanage:update')" class="btn btn-primary" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a> -->
<!-- 			<a v-if="hasPermission('wxmodulemanage:delete')" class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a> -->
<!-- 		</div> -->
<!-- 	    <table id="jqGrid"></table> -->
<!-- 	    <div id="jqGridPager"></div> -->
<!--     </div> -->
    
    
</div>

<script src="../../js/modules/cdyx/wxmodulemanage.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>