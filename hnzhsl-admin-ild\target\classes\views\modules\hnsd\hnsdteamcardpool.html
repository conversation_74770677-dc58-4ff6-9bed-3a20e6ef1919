<!DOCTYPE html>
<html>
<head>
    <title>2</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
            name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet"
          href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet"
          href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css"
          rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script
            src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script
            src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showList">

        <div class="row">
            <div class="form-group col-md-2">
                <label>团队编码</label> <input type="text" class="form-control"
                                           placeholder="团队名称" v-model="hnsdTeam.teamCode"/>
            </div>
            <div class="form-group col-md-2">
                <label>团队名称</label> <input type="text" class="form-control"
                                           placeholder="号池编码" v-model="hnsdTeam.teamName"/>
            </div>
            <div class="form-group col-md-2">
                <label>城市名称</label> <input type="text" class="form-control"
                                           placeholder="城市名称" v-model="hnsdTeam.cityName"/>
            </div>
        </div>

        <div class="row2"></div>

        <div class="grid-btn" style="margin-left: 19px;">
            <a v-if="hasPermission('hnsdteam:save')" class="btn btn-primary"
               @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a> <a
                v-if="hasPermission('hnsdteam:update')" class="btn btn-primary"
                @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a><a
                v-if="hasPermission('hnsdteam:delete')" class="btn btn-primary"
                @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a><a
                class="btn btn-primary" href="javascript:history.go(-1)">
            <i></i>&nbsp;返回</a>
        </div>
        <!--<a class="layui-btn layui-btn-small sxBtn" href="javascript:history.go(-1)" title="返回">
            <i class="layui-icon" style="font-size: 125%;margin-left: 86%;">返回</i>
        </a>-->
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal">

            <div class="form-group">
                <div class="col-sm-2 control-label">号池编号</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdTeamcardpool.cardPoolNumber" placeholder="号池编号"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">号池名称</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdTeamcardpool.cardPoolName" placeholder="号池名称"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">城市编号</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdTeamcardpool.cityCode" placeholder="城市编号"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">状态</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdTeamcardpool.status" placeholder="状态"/>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-2 control-label"></div>
                <input type="button" class="btn btn-primary" @click="saveOrUpdate"
                       value="确定"/> &nbsp;&nbsp;<input type="button"
                                                       class="btn btn-warning" @click="reload"
                                                       value="返回"/>
            </div>
        </form>
    </div>
</div>
<script src="../../js/modules/hnsd/hnsdteamcardpool.js"></script>
<script src="../../js/components.js"></script>
</div>
</div>
</div>
</div>
</body>
</html>