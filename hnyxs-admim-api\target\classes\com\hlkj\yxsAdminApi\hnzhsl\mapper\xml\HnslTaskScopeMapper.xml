<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslTaskScopeMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_task_scope a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.scopeCode != null">
                AND a.SCOPE_CODE LIKE CONCAT('%', #{param.scopeCode}, '%')
            </if>
            <if test="param.taskCode != null">
                AND a.TASK_CODE LIKE CONCAT('%', #{param.taskCode}, '%')
            </if>
            <if test="param.scopeType != null">
                AND a.SCOPE_TYPE = #{param.scopeType}
            </if>
            <if test="param.scopeStatus != null">
                AND a.SCOPE_STATUS = #{param.scopeStatus}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskScope">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskScope">
        <include refid="selectSql"></include>
    </select>

</mapper>
