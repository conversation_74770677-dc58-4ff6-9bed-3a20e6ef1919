<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>

      <div class="row">
      <div class="form-group col-md-2">
					<label>本地网:</label> <select class="form-control"
						style="height: 32px;" v-model="hnzsxOutbreakRecords.cityCode">
						<option value=''>全部</option>
						<option v-for="itme in city" v-bind:value="itme.cityCode">
							{{itme.cityName}}</option>
					</select>
				</div>
	<div class="form-group col-md-2" style="height: 32px;">
					<label for="meeting">日期：</label><input id="times" v-model='hnzsxOutbreakRecords.times' type="date" value="2014-01-13"/>
				</div>
	  </div>
     
	<div v-show="showList">
		<div class="grid-btn" style="margin-left: 1.5%">
			<a v-if="hasPermission('hnzsxoutbreakrecords:query')" class="btn btn-primary" @click="query">&nbsp;查询</a>
		 	<a class="btn btn-primary" @click="exportOpenStore">&nbsp;导出</a>
		
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
</div>

<script src="../../js/modules/hnzsx/hnzsxoutbreakrecords.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>