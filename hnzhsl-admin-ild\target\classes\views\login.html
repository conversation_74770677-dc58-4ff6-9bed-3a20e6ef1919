<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>湖南扫楼管理系统</title>
  <!-- Tell the browser to be responsive to screen width -->
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
  <link rel="shortcut icon" type="image/x-icon" href="favicon.ico">
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <link rel="stylesheet" href="css/font-awesome.min.css">
  <link rel="stylesheet" href="css/AdminLTE.min.css">
  <!-- AdminLTE Skins. Choose a skin from the css/skins
       folder instead of downloading all of them to reduce the load. -->
  <link rel="stylesheet" href="css/all-skins.min.css">
  <link rel="stylesheet" href="css/main.css">
    <script src="js/warterMarkJS.js"></script>
  <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
  <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
  <!--[if lt IE 9]>
  <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
  <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
  <![endif]-->
</head>
<body class="hold-transition login-page" style="background: rgb(204, 236, 220)">
<script type="text/javascript" color="255, 0, 0" opacity='0.7' zIndex="-2" count="99" src="//cdn.bootcss.com/canvas-nest.js/1.0.1/canvas-nest.min.js"></script>
<div class="login-box" id="rrapp" v-cloak>
 <div v-show="showIndex" id="isShowList">
  <div class="login-logo">
    <b>湖南智慧扫楼管理后台</b>
  </div>
  <!-- /.login-logo -->
  <div class="login-box-body">
      <p class="login-box-msg">管理员登录</p>
      <div v-if="error" class="alert alert-danger alert-dismissible">
        <h4 style="margin-bottom: 0px;"><i class="fa fa-exclamation-triangle"></i> {{errorMsg}}</h4>
      </div>
      <div class="form-group has-feedback">
          <input type="text" class="form-control" v-model="username" placeholder="账号">
          <span class="glyphicon glyphicon-user form-control-feedback"></span>
      </div>
      <div class="form-group has-feedback">
          <input type="password" class="form-control" v-model="password" placeholder="密码">
          <span class="glyphicon glyphicon-lock form-control-feedback"></span>
      </div>
      <div class="form-group has-feedback" style="display: flex;align-items: center;justify-content: space-between;">
          <div class="fl" style="flex: 1; margin-right: 15px;">
              <input type="text" class="form-control" v-model="captcha" @keyup.enter="login" placeholder="图形验证码">
<!--              <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>-->
          </div>
          <div class="fr">
              <img alt="如果看不清楚，请单击图片刷新！" class="pointer" :src="src" @click="refreshCode">
<!--              <a href="javascript:;" @click="refreshCode">点击刷新</a>-->
          </div>
      </div>
<!--      <div class="form-group has-feedback">-->
<!--          <img alt="如果看不清楚，请单击图片刷新！" class="pointer" :src="src" @click="refreshCode">-->
<!--          &nbsp;&nbsp;&nbsp;&nbsp;<a href="javascript:;" @click="refreshCode">点击刷新</a>-->
<!--      </div>-->
    <!--  <div class="form-group has-feedback">
          <input type="text" class="form-control" v-model="noteValue" placeholder="验证码">
          <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
      </div>
      <div class="form-group has-feedback">
          <button type="button" class="btn btn-primary btn-block btn-flat" id="msg1" @click="getVerificationCode" >获取短信验证码</button>
          <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
      </div>-->

<!--      短信验证码-->
      <div class="row" style="display:flex;align-items:center;justify-content:space-between;">
          <div class="col-xs-4" style="margin-right:15px; padding: 0; flex: 1;">
              <div class="form-group has-feedback">
                  <input type="text" class="form-control" v-model="noteValue" placeholder="短信验证码">
                  <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
              </div>          </div>
          <!-- /.col -->
          <div class="col-xs-4" style="padding: 0; width: 210px;">
              <div class="form-group has-feedback">
                  <button type="button" class="btn btn-primary btn-block btn-flat" id="msg1" @click="getVerificationCode" >获取短信验证码</button>
              </div>
          </div>
          <!-- /.col -->
      </div>


      <div class="row" style="display: flex; justify-content: flex-start; flex-direction: column;">
<!--        <div class="col-xs-4">-->
<!--          <div class="checkbox icheck">-->
<!--          </div>-->
<!--        </div>-->
          <div class="col-xs-4" style="width: 100%; margin-bottom: 20px; padding: 0; flex: 1;">
              <button type="button" class="btn btn-primary btn-block btn-flat" @click="login">登录</button>
          </div>
          <div class="col-xs-4" style="background: transparent; padding: 0;">
          <button type="button" class="btn btn-primary btn-block btn-flat" @click="forgotPassword" style="background: transparent;color: #f00;border: none;width: auto;height: auto; padding: 0;text-decoration: underline;">忘记密码</button>
        </div>
        <!-- /.col -->

        <!-- /.col -->
      </div>
    <!-- /.social-auth-links -->

  </div>
  <!-- /.login-box-body -->
 </div>
    <!-- 修改密码 -->
    <div v-show="showUpdatePassword" id="showUpdatePassword">
        <div class="login-logo">
            <b>湖南智慧扫楼管理后台</b>
        </div>
        <!-- /.login-logo -->
        <div class="login-box-body">
            <p class="login-box-msg">修改密码</p>
            <div class="form-group has-feedback">
                <input type="text" class="form-control" v-model="username" placeholder="账号">
                <span class="glyphicon glyphicon-user form-control-feedback"></span>
            </div>
            <div class="form-group has-feedback">
                <span>密码规则如下:密码最小长度:10位,必须包含大小写字母、数字、特殊字符且强度等级大于9</span>
            </div>
            <div class="form-group has-feedback">
                <input type="password" class="form-control" v-model="newPassword" placeholder="新密码">
                <span class="glyphicon glyphicon-lock form-control-feedback"></span>
            </div>
            <div class="form-group has-feedback">
                <input type="password" class="form-control" v-model="confirmPassword" placeholder="确认密码">
                <span class="glyphicon glyphicon-lock form-control-feedback"></span>
            </div>
            <div class="form-group has-feedback">
                <input type="text" class="form-control" v-model="verificationCode" placeholder="验证码">
                <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
            </div>
            <div class="form-group has-feedback">
                <button type="button" class="btn btn-primary btn-block btn-flat" id="msg" @click="getVerificationCode" >获取短信验证码</button>
                <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
            </div>

            <div class="row">
                <div class="col-xs-4">
                    <button type="button" class="btn btn-primary btn-block btn-flat" @click="reload">返回</button>
                </div>
                <div class="col-xs-4">
                    <div class="checkbox icheck">
                    </div>
                </div>
                <!-- /.col -->
                <div class="col-xs-4">
                    <button type="button" class="btn btn-primary btn-block btn-flat" @click="determine">确认</button>
                </div>
                <!-- /.col -->
            </div>
        </div>
    </div>
</div>
<!-- /.login-box -->
<script src="libs/jquery.min.js"></script>
<script src="plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="libs/vue.min.js"></script>
<script src="plugins/layer/layer.js"></script>
<script src="libs/bootstrap.min.js"></script>
<script src="libs/app.js"></script>
<script src="js/common.js"></script>
<script type="text/javascript" src="common/js/jsbn.js"></script>
<script type="text/javascript" src="common/js/prng4.js"></script>
<script type="text/javascript" src="common/js/rng.js"></script>
<script type="text/javascript" src="common/js/rsa.js"></script>
<script type="text/javascript" src="common/js/base64.js"></script>
<!-- 	<script type="text/javascript" src="common/rsa/RSA.js"></script> -->
<!-- 	<script type="text/javascript" src="common/rsa/BigInt.js"></script> -->
<!-- 	<script type="text/javascript" src="common/rsa/Barrett.js"></script> -->
<script type="text/javascript">
    //设置水印名称
 //   watermark('湖南智慧扫楼', "HNSL")
var vm = new Vue({
	el:'#rrapp',
	data:{
		username: '',
		password: '',
        captcha: '',
		error: false,
		errorMsg: '',
        src: 'captcha.jpg',
        showIndex:true,
        showUpdatePassword:false,
        verificationCode:null,
        newPassword:null,
        confirmPassword:null,
        noteValue:null,
	},
	beforeCreate: function(){
		if(self != top){
			top.location.href = self.location.href;
		}
	},
	methods: {
        refreshCode: function(){
            this.src = "captcha.jpg?t=" + $.now();
        },
        forgotPassword:function () {
            vm.showUpdatePassword = true;
            vm.showIndex = false;
            vm.username = null;
            vm.newPassword = null;
            vm.verificationCode = null;
        },
        reload:function () {
            vm.showUpdatePassword = false;
            vm.showIndex = true;
        },
        //校验手机号是否合法
        isPhoneNum :function (){
            if(vm.username == null || vm.username == ''){
                alert("请输入手机号再获取短信验证码");
                return false;
            }
            // var reg = /^1[3456789]\d{9}$/;
            // if(!reg.test(vm.username) && vm.username!='admin'){
            //     alert('请输入有效的手机号码！');
            //     return false;
            // }else{
            //     return true;
            // }
            return true;
        },
        //60s倒计时实现逻辑
        setTime:function (msg,timer) {

        },
        getVerificationCode:function () {
           // 校验手机号是否合法
           var flag = vm.isPhoneNum();
           if(flag){
               var msg = $("#msg");
               var msg1 = $("#msg1");
               var count=60,timer = null;
               console.info(vm.username);
               var data = "username="+vm.username;
               $.ajax({
                   type: "POST",
                   url: baseURL + "public/sendVerificationCode",
                   dataType: "json",
                   data: data,
                   success: function(r){
                       alert("发送短信验证码成功,请注意查看手机")
                       /*if(r.code === 0){
                           alert("发送短信验证码成功,请注意查看手机")
                       }else{
                           alert(r.msg);
                           clearInterval(timer);
                           msg.attr("disabled",false);
                       }*/
                   }
               });
               if(timer == null){
                   msg.attr("disabled",true);
                   msg1.attr("disabled",true);
                   timer = setInterval(function () {
                       count--;
                       msg.text(count+"秒后再次获取验证码");
                       msg1.text(count+"秒后再次获取验证码");
                       if(count <= 0){
                           clearInterval(timer);
                           msg.attr("disabled",false);
                           msg.text("获取验证码");
                           msg1.attr("disabled",false);
                           msg1.text("获取验证码");
                           timer = null;
                       }
                   },1000)
               }
           }
        },
		login: function () {
// 			bodyRSA();
// 			var result = encryptedString(key, encodeURIComponent(thisPwd));
			$.ajax({
				url : baseURL + "public/public_key",
				type : "GET",
				dataType : "json",
// 				data:  "username="+vm.username,
				cache : false,
				success : function(data) {
					var rsaKey = new RSAKey();
					rsaKey.setPublic(b64tohex(data.modulus),b64tohex(data.exponent));
					//enPassword  
					vm.password  = hex2b64(rsaKey.encrypt(vm.password));
				      var data = "username="+vm.username+"&password="+vm.password+"&captcha="+vm.captcha
                          +"&noteValue="+vm.noteValue;
						$.ajax({
							type: "POST",
						    url: baseURL + "sys/login",
						    data: data,
						    dataType: "json",
						    success: function(r){
								if(r.code == 0){//登录成功
			                        localStorage.setItem("token", r.token);
			                        localStorage.setItem("loginStatus","ok");
                                    window.document.cookie="token="+r.token;
                                    parent.location.href ='index.html';
								}else{
									vm.error = true;
									vm.errorMsg = r.msg;
									vm.password=null;
			                        vm.refreshCode();
								}
							}
						});
 
				}
	
			});
      
		},
        determine:function () {
            if(vm.username == null || vm.username == ''){
                alert("请输入修改账号");
                return;
            }
            var reg = /^1[3456789]\d{9}$/;
            if(!reg.test(vm.username)){
                alert('请输入有效的手机号码！');
                return;
            }
            if(vm.newPassword == null || vm.newPassword == ''){
                alert("请输入修改密码");
                return;
            }
            if(vm.confirmPassword == null || vm.confirmPassword == ''){
                alert("请输入确认密码");
                return;
            }
            if(!(vm.newPassword === vm.confirmPassword)){
                alert("确认密码与新密码不一致");
                return;
            }
            if(vm.verificationCode == null || vm.verificationCode == ''){
                alert("请输入验证码");
                return;
            }
            $.ajax({
                url : baseURL + "public/public_key",
                type : "GET",
                dataType : "json",
// 				data:  "username="+vm.username,
                cache : false,
                success : function(data) {
                    var rsaKey = new RSAKey();
                    rsaKey.setPublic(b64tohex(data.modulus),b64tohex(data.exponent));
                    //enPassword
                    vm.newPassword  = hex2b64(rsaKey.encrypt(vm.newPassword));
                    var data = "username="+vm.username+"&password="+vm.newPassword+"&verificationCode="+vm.verificationCode;
                    $.ajax({
                        type: "POST",
                        url: baseURL + "public/forgotPassword",
                        data: data,
                        dataType: "json",
                        success: function(r){
                            if(r.code == 0){//修改成功
                                var count = 4;
                                setInterval(function () {
                                    count--;
                                    alert("将在"+count+"秒后返回登陆页面");
                                    if(count <= 0){
                                        parent.location.href ='login.html';
                                    }
                                },1000)
                            }else{
                                alert(r.msg);
                            }
                        }
                    });
                }
            });
        },
        /*JSEncryptRSA:function(word){
            var PUBLIC_KEY = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8HMr2CBpoZPm3t9tCVlrKtTmI4jNJc7/HhxjIEiDjC8czP4PV+44LjXvLYcSV0fwi6nE4LH2c5PBPEnPfqp0g8TZeX+bYGvd70cXee9d8wHgBqi4k0J0X33c0ZnW7JruftPyvJo9OelYSofBXQTcwI+3uIl/YvrgQRv6A5mW01QIDAQAB';
            var encrypt = new JSEncrypt();
            encrypt.setPublicKey(PUBLIC_KEY);
            return encrypt.encrypt(JSON.stringify(word));
        }*/

// 		getPublicKey: function () {
// 			 var data = "username="+vm.username;
			
// 		}
	}
});
</script>
</body>
</html>
