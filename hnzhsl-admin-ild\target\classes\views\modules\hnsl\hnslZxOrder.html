<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<link rel="stylesheet" href="../../css/goods.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">

	<!-- 条件查询 -->
	<div class="row">
			<div class="form-group col-md-2" style="height:40px">
			  <label>联系电话</label>
		     <input type="text" class="form-control" placeholder="请输出联系电话" v-model="zxOrder.clientPhone"/>
		     </div>

		   <div class="form-group col-md-2" style="height:40px">
			   <label>配送方式</label>
			   <select class="form-control" style="height: 32px;" v-model="zxOrder.modeDistribution">
				   <option value='' >全部</option>
				   <option value='1' >班级</option>
				   <option value='2' >快递</option>
			   </select>
		   </div>

				<div class="form-group col-md-2">
					<label>订单状态</label>
					<select class="form-control" style="height: 32px;" v-model="zxOrder.orderStatus">
					  <option value='' >全部</option>
					  <option value='1' >已提交</option>
					  <option value='2' >已支付</option>
                      <option value='3' >已发货</option>
                    </select>
				</div>

				<div class="form-group col-md-2">
					<label>年级</label>
					<select class="form-control" style="height: 32px;" v-model="zxOrder.grade">
					  <option value='' >全部</option>
					  <option value="1">初一</option>
					  <option value="2">初二</option>
						<option value="3">初三</option>
					</select>
				</div>

				<div class="row2" style="padding-top: 25px;">
					<div class="form-group col-md-2" style="height: 32px;">
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							id="dateTimeRange" @keyup.enter="query" value="" type="text"
							placeholder="创建日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i> 
 						</span>
 						 <input name="beginTime" id="beginTime" type="hidden" >
 						 <input name="endTime" id="endTime" type="hidden">
 					</div>
				  </div>
			    </div>
		   <!--<a v-if="hasPermission('hnsdgoods:update')" class="btn btn-primary" @click="update" ><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a>-->
	</div>
		<div class="row">
			<a v-if="hasPermission('hnsdgoods:query')" class="btn btn-primary"  @click="query" style="margin-left:1%">&nbsp;查询</a>
			<a v-if="hasPermission('hnsdgoods:save')" class="btn btn-primary"  @click="outUserOrder"><i class="fa fa-plus"></i>&nbsp;导出</a>
			<a v-if="hasPermission('hnsdgoods:save')" class="btn btn-primary"  @click="importUser"><i class="fa fa-plus"></i>&nbsp;导入</a>
<!--			<a v-if="hasPermission('hnsdgoods:save')" class="btn btn-primary"  @click="importNumber"><i class="fa fa-plus"></i>&nbsp;号池导入</a>-->
			<form id="uploadImg" enctype="multipart/form-data">
			<input style="display: none;" name="uploadFile" id="uploadFile" type="file" @change="uploadFile" />
				<input style="display: none;" name="uploadFileNumber" id="uploadFileNumber" type="file" @change="uploadFileNumber" />
			</form>
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>

	</div>

</div>
<script  src="../../js/modules/hnsl/hnslZxOrder.js" ></script>
<script src="../../js/components.js"></script>
</body>
</html>