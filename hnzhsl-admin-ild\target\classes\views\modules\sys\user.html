<!DOCTYPE html>
<html>
<head>
<title>管理员列表</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<link rel="stylesheet" href="../../css/goods.css">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>
<script type="text/javascript" src="../../common/js/jsbn.js"></script>
<script type="text/javascript" src="../../common/js/prng4.js"></script>
<script type="text/javascript" src="../../common/js/rng.js"></script>
<script type="text/javascript" src="../../common/js/rsa.js"></script>
<script type="text/javascript" src="../../common/js/base64.js"></script>
<!-- <link href="../../plugins/Daterangepicker/css/daterangepicker.css" -->
<!-- 	rel="stylesheet"> -->


<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>
<!-- <script src="../../plugins/Daterangepicker/js/common.js"></script> -->
<!-- <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script> -->
<!-- <script -->
<!-- 	src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script> -->
<!-- <script -->
<!-- 	src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script> -->

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak >
	<div v-show="showList">
		<div class="row">
<!-- 				<div class="form-group col-md-2"> -->
<!-- 					<label>日期:</label> -->
<!-- 					<div class="input-group col-ms-2 "> -->
<!-- 										style="width: 240px; margin-left: -5px;" -->
<!-- 						<input class="form-control pull-left dateRange date-picker " -->
<!-- 							id="dateTimeRange" @keyup.enter="query" value="" type="text" -->
<!-- 							placeholder="日期"> <span class="input-group-addon"> -->
<!-- 							<i class="fa fa-calendar bigger-110"></i> -->
<!-- 						</span> <input name="beginTime" id="beginTime" v-model="q.beginTime" -->
<!-- 							type="hidden"> <input name="endTime" id="endTime" -->
<!-- 							v-model="q.endTime" type="hidden">  -->
<!-- 					</div> -->
<!-- 				</div> -->
				<div class="form-group col-md-2">
					<label>用户名:</label>
					<input type="text" class="form-control" v-model="q.linkman" @keyup.enter="query" placeholder="用户名" />
				</div>
				<div class="form-group col-md-2">
					<label>手机号:</label>
					<input type="text" class="form-control" v-model="q.mobile" @keyup.enter="query" placeholder="手机号" />
				</div>

				<div class="form-group col-md-2">
					<label>状态:</label>
					<select v-model="q.status" class="form-control"   @keyup.enter="query" >
					  <option v-for="option in statusOptions" v-bind:value="option.value">
					    {{ option.text }}
					  </option>
					</select>
				</div>
				<div class="form-group col-md-6" style="top:25px ;" align="left" >
					<a class="btn btn-default" sty @click="query">查询</a>
					<a v-if="hasPermission('sys:user:save')" class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
					<a v-if="hasPermission('sys:user:update')" class="btn btn-primary" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
					<a v-if="hasPermission('sys:user:delete')" class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a>
			    </div>
<!-- 				<div class="form-group col-md-2"> -->
<!-- 					<label>类型:</label> -->
<!-- 					<select v-model="q.type" class="form-control"   @keyup.enter="query" >   -->
<!-- 					  <option v-for="option in typeOptions" v-bind:value="option.value">   -->
<!-- 					    {{ option.text }}   -->
<!-- 					  </option>   -->
<!-- 					</select>   -->
<!-- 				</div> -->

<!-- 				<div class="form-group col-md-6 grid-btn" align="left" style="top: 25px"> -->
<!-- 			</div> -->
			</div>
		<div class=" row " style="border-top: 0px; padding: 0px "  >
		 	<div class="form-group col-md-2" v-if="areaList.length != 0  && areaList != null">
					<label>地市:</label>
					<select2 :options="areaList" id="areaList" class="form-control select" v-model="q.areaId"></select2>
		 	</div>
		 	<div class="form-group col-md-2" v-if="storeList.length != 0  && storeList != null">
					<label>营业厅:</label>
					<select2 :options="storeList" id="storeList" class="form-control select" v-model="q.storeId"></select2>
		 	</div>

<!-- 			<div class="form-group col-sm-2"> -->
<!-- 				<input type="text" class="form-control" v-model="q.username" @keyup.enter="query" placeholder="用户名"> -->
<!-- 			</div> -->
<!-- 			<a class="btn btn-default" @click="query">查询</a> -->
<!-- 			<a v-if="hasPermission('sys:user:save')" class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a> -->
<!-- 			<a v-if="hasPermission('sys:user:update')" class="btn btn-primary" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a> -->
<!-- 			<a v-if="hasPermission('sys:user:delete')" class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a> -->
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>

    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
			<div class="form-group">
			   	<div class="col-sm-2 control-label">登录账号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="user.username" placeholder="登录账号"/>
			    </div>
			</div>
 			<div class="form-group">
 			   	<div class="col-sm-2 control-label">密码</div>
 			   	<div class="col-sm-10">
 			      <input type="text" class="form-control" v-model="user.password" placeholder="密码"/>
 			    </div>
 			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">联系人</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="user.linkman" placeholder="联系人"/>
			    </div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">邮箱</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="user.email" placeholder="邮箱"/>
			    </div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">手机号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="user.mobile" placeholder="手机号"/>
			    </div>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label">身份证</div>
				<div class="col-sm-10">
					<input type="text" class="form-control" v-model="user.userSfz" placeholder="身份证"/>
				</div>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label">工单号</div>
				<div class="col-sm-10">
					<input type="text" class="form-control" v-model="user.workId" placeholder="工单号"/>
				</div>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label">厂商名称</div>
				<div class="col-sm-10">
					<input type="text" class="form-control" v-model="user.manuName" placeholder="厂商名称"/>
				</div>
			</div>
			<div class="form-group" v-if="areaList.length != 0  && areaList != null">
			   	<div class="col-sm-2 control-label" >地市 </div>
			   	<div class="col-sm-10"  >
			   		<select2 :options="areaList" id="areaList" class="form-control select" v-model="user.areaId" ></select2>
			    </div>
			</div>
			<div class="form-group" v-if="storeList.length != 0  && storeList != null">
				<div class="col-sm-2 control-label" >营业厅</div>
				<div class="col-sm-10" >
			   		<select2 :options="storeList" id="storeList" class="form-control select" v-model="user.storeId" ></select2>
			    </div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">角色</div>
			   	<div class="col-sm-10">
				   	<label v-for="role in roleList" class="checkbox-inline">
					  <input type="checkbox" :value="role.roleId" v-model="user.roleIdList">{{role.roleName}}
					</label>
				</div>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label">状态</div>
				<label class="radio-inline">
				  <input type="radio" name="status" value="0" v-model="user.status"/> 禁用
				</label>
				<label class="radio-inline">
				  <input type="radio" name="status" value="1" v-model="user.status"/> 正常
				</label>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label">工具</div>
				<label class="radio-inline">
				  <input type="radio" name="toolType" value="1" v-model="user.toolType"/> 智慧扫楼
				</label>
				<label class="radio-inline">
				  <input type="radio" name="toolType" value="2" v-model="user.toolType"/> 快甩
				</label>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label"></div>
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div>
		</form>
	</div>


	<div class="fadeBox" id="tcBox" v-show="editIPShow">
		<div class="dao_pag package" style="width: 400px; position: relative; padding: 15px 20px; color: #000; font-size: 14px;">
			<h5 style="width: 100%; position: relative; padding: 15px 20px; color: #000; font-size: 14px; text-align: center;background: #fff">IP编辑</h5>
			<div class="layui-form mt20 inpBox inp-flex-box" style="height: 150px">
				<div class="layui-form-item inp_pag" style="flex-wrap: wrap">
					<div style="width: 100%">
						<label >当前登录IP数量: <input style="border: none; width: 10px;" v-model="ipCount" />
                            <button class="layui-btn" @click="clearIP()" style="line-height: 30px; height: 30px; margin-left: 10px;">清空</button>
                        </label>
					</div>
					<div style="width: 100%; display: flex; align-items: center;margin-top: 10px">
						<label style="margin-right: 10px">IP白名单:</label>
						<select class="form-control" v-model="user.accWhite" id="vehicle1" @change="handleSelectChange()" style="width: 100px">
							<option value='1' >禁用</option>
							<option value='2' >开启</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item inpBtn">
				<button class="layui-btn" @click="cancel()">取消</button>
				<button class="layui-btn" @click="saveIP()" >保存</button>
			</div>
		</div>
	</div>
</div>

<script src="../../js/modules/sys/user.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>