<!DOCTYPE html>
<html>
<head>
    <title>签名审核</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showList">
        <div class="row">
            <div class="form-group col-md-2" style="height: 40px">
                <label>姓名/手机号:</label>
                <input type="text" class="form-control" placeholder="请输入手机号/姓名" v-model="hnsluserswitch.value" />
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>学校:</label>
                <input type="text" class="form-control" placeholder="请输入学校名" v-model="hnsluserswitch.schoolName" />
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>地市:</label>
                <select class="form-control" style="height: 32px;" v-model="hnsluserswitch.cityCode">
                    <option value=''>全部</option>
                    <option v-for="itme in city" v-bind:value="itme.cityCode">
                        {{itme.cityName}}
                    </option>
                </select>
            </div>
            <div class="form-group col-md-2">
                <label>是否驳回:</label> <select class="form-control" style="height: 32px;" v-model="hnsluserswitch.commitmentStatus">
                <option value=''>全部</option>
                <option value='2'>是</option>
                <option value='1'>否</option>+
            </select>
            </div>
        </div>
        <div class="grid-btn" style="margin-left: 17Px; margin-top: 18px">
            <a v-if="hasPermission('hnsluserswitch:query')" class="btn btn-primary" @click="query">
                <i></i>&nbsp;查询
            </a>
<!--            <a v-if="hasPermission('hnsluserswitch:query')" class="btn btn-primary" @click="importList">-->
<!--                <i></i>&nbsp;导出-->
<!--            </a>-->
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>


</div>

<script src="../../js/modules/hnsl/hnslUserSwitch.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>