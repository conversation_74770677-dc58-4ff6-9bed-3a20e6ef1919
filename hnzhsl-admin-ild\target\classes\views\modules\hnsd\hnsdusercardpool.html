<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
<style type="text/css">
  .form-control2{display:block;width:100%;height:34px;padding:6px 12px;font-size:14px;line-height:1.42857143;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;border-radius:4px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075);box-shadow:inset 0 1px 1px rgba(0,0,0,.075);-webkit-transition:border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;-o-transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s}
</style>
</head>
<body>
 <div id="rrapp" v-cloak>

	<div v-show="showList">
	    
	    <div class="row">
	    
	    
	       <div class="form-group col-md-2" style="height:52px">
	          <label>号池编码  / 名称:</label>
	          <input class="form-control2"  placeholder="号池编码  / 名称" v-model="seachUserCardpool.cardPoolNumberOrName"/>
	       </div>
	       
	        <div class="form-group col-md-2" style="height:40px">
		      <label>用户姓名 / 揽机工号：</label> 
		     <input type="text" class="form-control2" placeholder="用户姓名  /  揽机工号" v-model="seachUserCardpool.userNameOrUserId"/>
		   </div>
		   
		    <div class="form-group col-md-2" style="height:40px">
		      <label>状态：</label> 
		     <select class="form-control" style="height: 32px;"  v-model="seachUserCardpool.status">
					  <option value='-1' >全部</option>
					  <option value='1' >有效</option>
					  <option value='0' >无效</option>
					</select>
<!-- 		     <input type="text" class="form-control2" placeholder="状态" v-model="seachUserCardpool.status"/>
 -->		   </div>
	
		 <div class="form-group col-md-2" style="height: 32px;">
		 		   <label>创建日期：</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							id="dateTimeRange" @keyup.enter="query" value="" type="text" 
							placeholder="创建日期"> <span class="input-group-addon"> 
							<i class="fa fa-calendar bigger-110"></i> 
 						</span>
 						 <input name="beginTime" id="beginTime" type="hidden" >
 						 <input name="endTime" id="endTime" type="hidden">  
 					</div> 
		</div> 
	    
	    	    </div>
	  <form id="uploadImg" enctype="multipart/form-data">
	    <div class="row2">
	    <div  class="grid-btn"  style="margin-left: 19px;">
			<a v-if="hasPermission('hnsdgoods:query')" class="btn btn-primary"  @click="query">&nbsp;查询</a>
			<a v-if="hasPermission('hnsduser:importCardPool')" class="btn btn-primary" @click="importCardPool">&nbsp;导入</a>
			<input style="display:none;" name="uploadFile" id="uploadFile" type="file" @change="uploadFile" />
			<a v-if="hasPermission('hnsdgoods:save')" class="btn btn-primary"  @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
       </div>
       </div>
       </form>
	    
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
			
			<div class="form-group">
			   	<div class="col-sm-2 control-label" style="width:32%;margin-left: -82%">揽机工号:</div>
			   <!-- 	<div class="col-sm-10"> -->
			      <input type="text" class="form-control" style="width: 68%;margin-left: -48%" placeholder="请输入揽机工号" @blur.prevent="focus" v-model="hnsdUser.userId"/>
			   <!--  </div> -->
			</div>
			
			          <div class="form-group">
			   	<div class="col-sm-2 control-label" style="margin-left: -74%;">地市:</div>
			   <!-- 	<div class="col-sm-10"> -->
			      <input type="text" class="form-control" style="width: 68%;margin-left:-48%" disabled="disabled" v-model="hnsdUser.city"/>
			    <!-- </div> -->
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label" style="margin-left: -74%;">姓名:</div>
			   <!-- 	<div class="col-sm-10"> -->
			      <input type="text" class="form-control" style="width: 68%;margin-left:-48%" disabled="disabled" v-model="hnsdUser.userName"/>
			    <!-- </div> -->
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label" style="width:32%;margin-left: -82%">号池编码:</div>
			   <!-- 	<div class="col-sm-10"> -->
			      <input type="text" class="form-control" style="width: 68%;margin-left:-48%" placeholder="请输入号池编码" v-model="hnsdUserCardpool.cardPoolNumber"/>
			    <!-- </div> -->
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label" style="width:32%;margin-left: -82%">号池名称:</div>
			   	<!-- <div class="col-sm-10"> -->
			      <input type="text" class="form-control" style="width: 68%;margin-left:-48%"placeholder="请输入号池名称" v-model="hnsdUserCardpool.cardPoolName"/>
			    <!-- </div> -->
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label" style="margin-left: -74%">状态:</div>
			   	<div class="col-sm-10">
			   	 <select class="form-control" style=";width: 106%;margin-left: -75%;" v-model="hnsdUserCardpool.status">
			   	   <option value="1">有效</option>
			   	   <option value="0">无效</option>
			   	 </select>
			    </div>
			</div>
		     <div class="form-group" v-if="sure">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定" style="margin-left: -63%;margin-top: 1%;"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回" style="margin-left: 1%;margin-top: 1%;"/>
			</div>
			<div class="form-group" v-else="sure">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-warning" @click="reload" value="返回" style="margin-left: -53%;margin-top: 1%;"/>
			</div>
		</form>
	</div>
</div>

<script src="../../js/modules/hnsd/hnsdusercardpool.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>