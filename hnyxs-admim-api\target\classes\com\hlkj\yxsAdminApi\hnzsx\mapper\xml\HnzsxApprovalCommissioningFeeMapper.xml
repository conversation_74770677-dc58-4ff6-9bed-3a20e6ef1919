<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxApprovalCommissioningFeeMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_approval_commissioning_fee a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.expenseName != null">
                AND a.EXPENSE_NAME LIKE CONCAT('%', #{param.expenseName}, '%')
            </if>
            <if test="param.expenseType != null">
                AND a.EXPENSE_TYPE LIKE CONCAT('%', #{param.expenseType}, '%')
            </if>
            <if test="param.terminalType != null">
                AND a.TERMINAL_TYPE LIKE CONCAT('%', #{param.terminalType}, '%')
            </if>
            <if test="param.escrowPackageId != null">
                AND a.ESCROW_PACKAGE_ID LIKE CONCAT('%', #{param.escrowPackageId}, '%')
            </if>
            <if test="param.escrowPackageName != null">
                AND a.ESCROW_PACKAGE_NAME LIKE CONCAT('%', #{param.escrowPackageName}, '%')
            </if>
            <if test="param.escrowPackageMoney != null">
                AND a.ESCROW_PACKAGE_MONEY LIKE CONCAT('%', #{param.escrowPackageMoney}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.escrowPackageMoneyLimit != null">
                AND a.ESCROW_PACKAGE_MONEY_LIMIT LIKE CONCAT('%', #{param.escrowPackageMoneyLimit}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxApprovalCommissioningFee">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxApprovalCommissioningFee">
        <include refid="selectSql"></include>
    </select>

</mapper>
