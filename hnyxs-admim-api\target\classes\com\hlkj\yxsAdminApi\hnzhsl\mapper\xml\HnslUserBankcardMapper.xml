<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslUserBankcardMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_user_bankcard a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.bankName != null">
                AND a.BANK_NAME LIKE CONCAT('%', #{param.bankName}, '%')
            </if>
            <if test="param.bankSubName != null">
                AND a.BANK_SUB_NAME LIKE CONCAT('%', #{param.bankSubName}, '%')
            </if>
            <if test="param.bankNumber != null">
                AND a.BANK_NUMBER LIKE CONCAT('%', #{param.bankNumber}, '%')
            </if>
            <if test="param.userName != null">
                AND a.USER_NAME LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.userId != null">
                AND a.USER_ID LIKE CONCAT('%', #{param.userId}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.bankCode != null">
                AND a.BANK_CODE LIKE CONCAT('%', #{param.bankCode}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserBankcard">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserBankcard">
        <include refid="selectSql"></include>
    </select>

</mapper>
