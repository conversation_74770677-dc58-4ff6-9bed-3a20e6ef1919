<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsAddPackageMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_add_package a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.packageScene != null">
                AND a.PACKAGE_SCENE = #{param.packageScene}
            </if>
            <if test="param.packageName != null">
                AND a.PACKAGE_NAME LIKE CONCAT('%', #{param.packageName}, '%')
            </if>
            <if test="param.packageGoodsId != null">
                AND a.PACKAGE_GOODS_ID LIKE CONCAT('%', #{param.packageGoodsId}, '%')
            </if>
            <if test="param.packageGoodsName != null">
                AND a.PACKAGE_GOODS_NAME LIKE CONCAT('%', #{param.packageGoodsName}, '%')
            </if>
            <if test="param.packageGrade != null">
                AND a.PACKAGE_GRADE LIKE CONCAT('%', #{param.packageGrade}, '%')
            </if>
            <if test="param.packageLevel != null">
                AND a.PACKAGE_LEVEL = #{param.packageLevel}
            </if>
            <if test="param.packageCode != null">
                AND a.PACKAGE_CODE LIKE CONCAT('%', #{param.packageCode}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.saleName != null">
                AND a.SALE_NAME LIKE CONCAT('%', #{param.saleName}, '%')
            </if>
            <if test="param.eqAction != null">
                AND a.EQ_ACTION LIKE CONCAT('%', #{param.eqAction}, '%')
            </if>
            <if test="param.acceptanceSteps != null">
                AND a.ACCEPTANCE_STEPS LIKE CONCAT('%', #{param.acceptanceSteps}, '%')
            </if>
            <if test="param.exhibitStatus != null">
                AND a.EXHIBIT_STATUS LIKE CONCAT('%', #{param.exhibitStatus}, '%')
            </if>
            <if test="param.addPackageName != null">
                AND a.ADD_PACKAGE_NAME LIKE CONCAT('%', #{param.addPackageName}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsAddPackage">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsAddPackage">
        <include refid="selectSql"></include>
    </select>

    <!--  查询商品加包数据  -->
    <select id="queryCgrPackage" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsAddPackage">
        select t2.* from (select * from hnzsx_goods_cgr_package where status=1) t1
        left join (select * from hnzsx_goods_add_package where status=1) t2 on t1.package_id=t2.id
        WHERE t1.GOODS_ID = #{id}
    </select>

</mapper>
