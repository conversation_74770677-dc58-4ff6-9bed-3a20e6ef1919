<!DOCTYPE html>
<html>
<head>
    <title>2</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->
    <script src="../../js/common.js"></script>


    <style>
        .form-custom tr {
            width: 100%;
            height: 50%;
        }

        .form-custom tr td:nth-child(1) {
            width: 10%;
        }

        .form-custom tr td:nth-child(2n) {
            width: 20%;
        }

        .form-custom td {
            text-align: center;
        }
    </style>
</head>
<body>

<div id="rrapp" v-cloak>
    <div v-show="showList">
        <div class="grid-btn">
            <a v-if="hasPermission('hnzsxancenter:save')" class="btn btn-primary" @click="add"><i
                    class="fa fa-plus"></i>&nbsp;新增</a>
            <a v-if="hasPermission('hnzsxancenter:update')" class="btn btn-primary" @click="update"><i
                    class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
            <a v-if="hasPermission('hnzsxancenter:delete')" class="btn btn-primary" @click="del"><i
                    class="fa fa-trash-o"></i>&nbsp;删除</a>
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>

        <form class="form-horizontal" style="width: 100%;padding-top: 0px">
            <table class="form-custom" style="width: 100%; height:150px">
                <tr>
                    <td class="leftTd">
                        <label style="text-align: center;">
                            <span>标题</span>
                        </label>
                    </td>
                    <td>
                        <input type="text" class="form-control" v-model="hnzsxAncenter.title"
                               placeholder="标题"/>
                    </td>
                    <td class="leftTd">
                        <label style="text-align: center;">
                            <span>类型</span>
                        </label>
                    </td>
                    <td>
                        <select class="form-control" id = "type"
                                style="height: 32px;" v-model="hnzsxAncenter.type">
                            <option value='1'>左边轮播</option>
                            <option value='2'>中间轮播</option>
                            <option value='3'>右边轮播</option>
                        </select>
                    </td>
                    <td class="leftTd">
                        <label style="text-align: center;">
                            <span>是否下架</span>
                        </label>
                    </td>
                    <td>
                        <select class="form-control" v-model="hnzsxAncenter.status" id = "status" style="height: 32px;">
                            <option value="1">上架</option>
                            <option value="0">下架</option>
                        </select>
                    </td>
                    <input type="hidden" name = "content" class="form-control" id = "editor_txt" v-model="hnzsxAncenter.content">
                </tr>
            </table>

            <p>公告中心&nbsp;&nbsp;&nbsp;<b>内容编辑器</b></p>
            <div id="editor" v-show="!showList" class="panel panel-default"></div>
            <tr class="form-group">
                <input type="button" id="determine" style="margin-left: 45%" class="btn btn-primary"  value="确定"/>
                &nbsp;&nbsp;<input id="return" type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </tr>
        </form>

        <script src="../../js/components.js"></script>
    </div>
</div>
<script src="../../plugins/wangEditor/wangEditor.js"></script>

<script type="text/javascript">

    var vm = new Vue({
        el: '#rrapp',
        data: {
            showList: true,
            title: null,
            hnzsxAncenter: {}
        },
        methods: {
            query: function () {
                vm.reload();
            },
            add: function () {
                $("#determine").show();
                $("#return").attr("style","");
                vm.showList = false;
                vm.title = "新增";
                vm.hnzsxAncenter = {};
            },
            update: function (event) {
                $("#determine").show();
                $("#return").attr("style","");
                var id = getSelectedRow();
                vm.hnzsxAncenter.id=id;
                if (id == null) {
                    return;
                }
                vm.showList = false;
                vm.title = "修改";
                vm.updateButton=true;
                vm.getInfo(id);

            },

            //查看详情
            detail:function(id){
                $("#determine").hide();
                $("#return").attr("style","margin-left: 45%");
                vm.showList = false;
                vm.title = "公告详情";
                if(id!=null & id !=''){
                    vm.getInfo(id);
                }else{
                    alert("详情ID为空");
                }
            },
            // 禁用
            modify:function (id,status){
                console.log(id+'  '+status);
                vm.hnzsxAncenter.id = id;
                vm.hnzsxAncenter.status = status;
                $.ajax({
                    type: "POST",
                    url: baseURL + "hnzsxancenter/disableStatus",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        "id":vm.hnzsxAncenter.id,
                        "status":vm.hnzsxAncenter.status
                    },
                    success: function(r){
                        console.log(r);
                        if(r.code === 0){
                            alert('操作成功',function(){
                                vm.reload();
                            });
                        }else{
                            alert(r.msg);
                        }
                    }
                });
            },
            /*editorClick : function (editor) {
                console.info(editor);
                var editor_txt = editor.txt.text();
                console.info(editor_txt);
                vm.hnzsxAncenter.content = editor.txt.text();
            },*/
            saveOrUpdate: function (event) {
                var url = vm.hnzsxAncenter.id == null ? "hnzsxancenter/save" : "hnzsxancenter/update";
               /* var imgBase = vm.hnzsxAncenter.content;
                var encodeContent = encodeURIComponent(imgBase);*/
                // vm.hnzsxAncenter.content = encodeURIComponent(vm.hnzsxAncenter.content);
                console.info(vm.hnzsxAncenter.title);
                $.ajax({
                    type: "POST",
                    url: baseURL + url,
                    contentType: "application/json;charset=utf-8",
                    data: {
                        'id':vm.hnzsxAncenter.id,
                        'type':vm.hnzsxAncenter.type,
                        'status':vm.hnzsxAncenter.status,
                        'title':vm.hnzsxAncenter.title,
                        'encodeContent':vm.hnzsxAncenter.content},
                           // JSON.stringify(vm.hnzsxAncenter),

                    success: function (r) {
                        if (r.code === 0) {
                            alert('操作成功', function (index) {
                                vm.reload();
                            });
                        } else {
                            alert(r.msg);
                        }
                    }
                });
            },
            del: function (event) {
                var contents = getSelectedRows();
                if (contents == null) {
                    return;
                }

                confirm('确定要删除选中的记录？', function () {
                    $.ajax({
                        type: "POST",
                        url: baseURL + "hnzsxancenter/delete",
                        contentType: "application/json",
                        data: JSON.stringify(contents),
                        success: function (r) {
                            if (r.code == 0) {
                                alert('操作成功', function (index) {
                                    $("#jqGrid").trigger("reloadGrid");
                                });
                            } else {
                                alert(r.msg);
                            }
                        }
                    });
                });
            },
            getInfo: function (id) {
                $.get(baseURL + "hnzsxancenter/info/" + id, function (r) {
                    vm.hnzsxAncenter = r.hnzsxAncenter;
                    $(".editorName").html(vm.hnzsxAncenter.content);
                    // $("#type").append(vm.hnzsxAncenter.type);
                    // $("#status").append(vm.hnzsxAncenter.status);

                });
            },
            reload: function (event) {
                vm.showList = true;
                $(".editorName").html("");
                var page = $("#jqGrid").jqGrid('getGridParam', 'page');
                $("#jqGrid").jqGrid('setGridParam', {
                    page: page
                }).trigger("reloadGrid");
            }
        }
    });


    var E = window.wangEditor;
    var editor = new E('#editor');
    // 开启上传图片功能
    editor.customConfig.uploadImgShowBase64 = true;   // 使用 base64 保存图片
    // editor.customConfig.uploadImgServer = '/upload';  // 上传图片到服务器
    // 隐藏“网络图片”tab
    editor.customConfig.showLinkImg = false;
    // 或者 var editor = new E( document.getElementById('editor') )
    editor.create();

    document.getElementById("determine").addEventListener('click',function () {
        /*var editor_txt = editor.txt.text();
        console.info(editor_txt);*/
        vm.hnzsxAncenter.content = editor.txt.html();
        vm.saveOrUpdate();
        // this.hnzsxAncenter.content = editor_txt;
    },false)

</script>
<script src="../../js/modules/hnzsx/hnzsxancenter.js"></script>
</body>
</html>