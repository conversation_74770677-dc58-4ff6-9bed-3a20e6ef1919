<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5TemplateInfoMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsxh5_template_info a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.templateId != null">
                AND a.TEMPLATE_ID LIKE CONCAT('%', #{param.templateId}, '%')
            </if>
            <if test="param.templateName != null">
                AND a.TEMPLATE_NAME LIKE CONCAT('%', #{param.templateName}, '%')
            </if>
            <if test="param.serviceOfferId != null">
                AND a.SERVICE_OFFER_ID LIKE CONCAT('%', #{param.serviceOfferId}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.masterPackageCode != null">
                AND a.MASTER_PACKAGE_CODE LIKE CONCAT('%', #{param.masterPackageCode}, '%')
            </if>
            <if test="param.masterPackageName != null">
                AND a.MASTER_PACKAGE_NAME LIKE CONCAT('%', #{param.masterPackageName}, '%')
            </if>
            <if test="param.forceOpen != null">
                AND a.FORCE_OPEN = #{param.forceOpen}
            </if>
            <if test="param.forceOpenNum != null">
                AND a.FORCE_OPEN_NUM = #{param.forceOpenNum}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5TemplateInfo">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5TemplateInfo">
        <include refid="selectSql"></include>
    </select>

</mapper>
