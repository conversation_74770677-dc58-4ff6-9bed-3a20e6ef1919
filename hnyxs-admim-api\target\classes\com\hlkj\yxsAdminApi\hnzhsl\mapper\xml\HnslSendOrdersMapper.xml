<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslSendOrdersMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_send_orders a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.ordersName != null">
                AND a.ORDERS_NAME LIKE CONCAT('%', #{param.ordersName}, '%')
            </if>
            <if test="param.ordersDate != null">
                AND a.ORDERS_DATE LIKE CONCAT('%', #{param.ordersDate}, '%')
            </if>
            <if test="param.ordersCount != null">
                AND a.ORDERS_COUNT = #{param.ordersCount}
            </if>
            <if test="param.ordersExpiredDate != null">
                AND a.ORDERS_EXPIRED_DATE LIKE CONCAT('%', #{param.ordersExpiredDate}, '%')
            </if>
            <if test="param.ordersMainMeal != null">
                AND a.ORDERS_MAIN_MEAL LIKE CONCAT('%', #{param.ordersMainMeal}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.activityId != null">
                AND a.ACTIVITY_ID LIKE CONCAT('%', #{param.activityId}, '%')
            </if>
            <if test="param.ordersEffectDate != null">
                AND a.ORDERS_EFFECT_DATE LIKE CONCAT('%', #{param.ordersEffectDate}, '%')
            </if>
            <if test="param.ordersMainNumber != null">
                AND a.ORDERS_MAIN_NUMBER LIKE CONCAT('%', #{param.ordersMainNumber}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrders">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrders">
        <include refid="selectSql"></include>
    </select>

    <!-- 关联查询sql -->
    <sql id="selectSqlMap">
        SELECT
        uod.SCHOOL_CITY as cityCode,
        #{param.auditDate}  as month,
        uod.SCHOOL_NAME,
        so.ORDERS_NAME,
        uod.ordersDate,param.
        uod.count,
        uoo.ordersCount,
        ifnull(uoo.yw,0) as yw,
        ifnull(CONCAT( ROUND(uoo.yw/uoo.ordersCount, 2 ),'%'),'0.00%') as yhRate,
        ifnull(uoo.hs,0) as hs,
        ifnull(uoo.hd,0) as hd,
        ifnull(uoo.yq,0) as yq,
        ifnull(CONCAT( ROUND(uoo.yq/uoo.ordersCount, 2 ),'%'),'0.00%') as yqRate,
        ifnull(uoo.yxSuccess,0) as yxSuccess,
        ifnull(uoo.yxFail,0) as yxFail,
        ifnull(CONCAT( ROUND(uoo.yxSuccess/uoo.yw, 2 ),'%'),'0.00%') as successRate,
        ifnull(CONCAT( ROUND(uoo.yxFail/uoo.yw, 2 ),'%'),'0.00%') as failRate
        FROM
        (
        SELECT
        s.SCHOOL_CITY,
        s.SCHOOL_NAME,
        o.ACTIVITY_LOGO,
        date_format(sod.CREATED_DATE,'%Y%m%d') as ordersDate,
        sum( CASE WHEN o.SEND_ORDERS_STATUS IN ( 2, 3, 4, 5 ) THEN 1 ELSE 0 END ) AS yw,
        sum( CASE o.SEND_ORDERS_STATUS WHEN 8 THEN 1 ELSE 0 END ) AS hs,
        sum( CASE o.SEND_ORDERS_STATUS WHEN 6 THEN 1 ELSE 0 END ) AS hd,
        sum( CASE o.SEND_ORDERS_STATUS WHEN 7 THEN 1 ELSE 0 END ) AS yq,
        sum( CASE o.ORDER_RECEIPT_RESULT WHEN 1 THEN 1 ELSE 0 END ) AS yxSuccess,
        sum( CASE WHEN o.ORDER_RECEIPT_RESULT IN ( 2, 3, 4, 5, 6, 7 ) THEN 1 ELSE 0 END ) AS yxFail,
        count( * ) AS ordersCount
        FROM
        hnsl_user_orders_data AS o
        left join hnsl_send_orders_data sod on o.TOUCH_ID=sod.TOUCH_ID
        left join hnsl_school s on o.SCHOOL_SIX_ID=s.SCHOOL_SIX_ID
        <where>
            <if test="param.cityCode != null and param.cityCode != ''">
                and s.SCHOOL_CITY=#{param.cityCode}
            </if>
            <if test="param.querySchoolName != null and param.querySchoolName != ''">
                and s.SCHOOL_NAME like concat(concat('%',#{param.querySchoolName}),'%')
            </if>
            <if test="param.auditDate != null and param.auditDate != ''">
                and sod.CREATED_DATE BETWEEN #{param.startTime} and #{param.endTime}
            </if>
        </where>
        GROUP BY
        s.SCHOOL_CITY,s.SCHOOL_NAME,o.ACTIVITY_LOGO,date_format(sod.CREATED_DATE,'%Y%m%d')
        ) uoo
        right join (SELECT s.SCHOOL_CITY,s.SCHOOL_NAME,sod.ACTIVITY_LOGO,count(*) as count
        ,date_format(sod.CREATED_DATE,'%Y%m%d') as ordersDate FROM
        (SELECT ACTIVITY_LOGO,CREATED_DATE,SCHOOL_SIX_ID from hnsl_send_orders_data
        <where>
            <if test="param.auditDate != null and param.auditDate != ''">
                and CREATED_DATE BETWEEN #{param.startTime} and #{param.endTime}
            </if>
        </where>) sod
        left join hnsl_school s on sod.SCHOOL_SIX_ID=s.SCHOOL_SIX_ID
        GROUP BY s.SCHOOL_CITY,s.SCHOOL_NAME,sod.ACTIVITY_LOGO
        ,date_format(sod.CREATED_DATE,'%Y%m%d')) uod
        on uoo.SCHOOL_CITY=uod.SCHOOL_CITY and uoo.SCHOOL_NAME=uod.SCHOOL_NAME
        and uoo.ACTIVITY_LOGO=uod.ACTIVITY_LOGO
        and uoo.ordersDate=uod.ordersDate
        left join hnsl_send_orders so on uod.ACTIVITY_LOGO=so.ACTIVITY_ID
        <where>
            so.STATUS=1
            <if test="param.queryName != null and param.queryName != ''">
                and so.ORDERS_NAME like concat(concat('%',#{param.queryName}),'%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageMapRel" resultType="java.util.HashMap">
        <include refid="selectSqlMap"></include>
    </select>
    <!-- 全部查询 -->
    <select id="selectListMapRel" resultType="com.alibaba.fastjson.JSONObject">
        <include refid="selectSqlMap"></include>
    </select>

    <!--  导出数据查询  -->
    <select id="queryAllList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT s.SCHOOL_CITY as cityCode,
        s.SCHOOL_NAME,
        so.ORDERS_NAME,
        os.ordersDate,
        ifnull(os.sl,0) as count,
        ifnull(uoo.usl,0) as ordersCount,
        ifnull(uoo.yw,0) as yw,
        ifnull(CONCAT( ROUND(uoo.yw/uoo.usl, 2 ),'%'),'0.00%') as yhRate,
        ifnull(uoo.hs,0) as hs,
        ifnull(uoo.hd,0) as hd,
        ifnull(uoo.yq,0) as yq,
        ifnull(uoo.yq,0) as yq,
        ifnull(CONCAT( ROUND(uoo.yq/uoo.usl, 2 ),'%'),'0.00%') as yqRate,
        ifnull(uoo.yxSuccess,0) as yxSuccess,
        ifnull(uoo.yxFail,0) as yxFail,
        ifnull(CONCAT( ROUND(uoo.yxSuccess/uoo.yw, 2 ),'%'),'0.00%') as successRate,
        CONCAT( ROUND(uoo.yxFail/uoo.yw, 2 ),'%') as failRate
        from hnsl_school s left join
        ( SELECT od.SCHOOL_SIX_ID,od.ACTIVITY_LOGO,count(*) as sl,date_format(od.CREATED_DATE,'%Y%m%d') as ordersDate FROM `hnsl_send_orders_data` od
        GROUP BY od.SCHOOL_SIX_ID,od.ACTIVITY_LOGO,date_format(od.CREATED_DATE,'%Y%m%d')
        ) os on s.SCHOOL_SIX_ID=os.SCHOOL_SIX_ID
        left join
        (
        SELECT
        o.SCHOOL_SIX_ID,
        o.ACTIVITY_LOGO,
        date_format(sod.CREATED_DATE,'%Y%m%d') as ordersDate,
        sum( CASE WHEN o.SEND_ORDERS_STATUS IN ( 2, 3, 4, 5 ) THEN 1 ELSE 0 END ) AS yw,
        sum( CASE o.SEND_ORDERS_STATUS WHEN 8 THEN 1 ELSE 0 END ) AS hs,
        sum( CASE o.SEND_ORDERS_STATUS WHEN 6 THEN 1 ELSE 0 END ) AS hd,
        sum( CASE o.SEND_ORDERS_STATUS WHEN 7 THEN 1 ELSE 0 END ) AS yq,
        sum( CASE o.ORDER_RECEIPT_RESULT WHEN 1 THEN 1 ELSE 0 END ) AS yxSuccess,
        sum( CASE WHEN o.ORDER_RECEIPT_RESULT IN ( 2, 3, 4, 5, 6, 7 ) THEN 1 ELSE 0 END ) AS yxFail,
        count(*) as usl
        FROM
        hnsl_user_orders_data AS o
        left join hnsl_send_orders_data sod on o.TOUCH_ID=sod.TOUCH_ID
        where o.`STATUS` = 1
        GROUP BY
        o.SCHOOL_SIX_ID,ACTIVITY_LOGO,date_format(sod.CREATED_DATE,'%Y%m%d')

        ) uoo on s.SCHOOL_SIX_ID=uoo.SCHOOL_SIX_ID and uoo.ACTIVITY_LOGO=os.ACTIVITY_LOGO and uoo.ordersDate=os.ordersDate
        left join hnsl_send_orders so on os.ACTIVITY_LOGO=so.ACTIVITY_ID
        <where>
            <if test="cityCode != null and cityCode != ''">
                and s.SCHOOL_CITY=#{cityCode}
            </if>
            <if test="querySchoolName != null and querySchoolName != ''">
                and s.SCHOOL_NAME like concat(concat('%',#{querySchoolName}),'%')
            </if>
            <if test="queryName != null and queryName != ''">
                and so.ORDERS_NAME like concat(concat('%',#{queryName}),'%')
            </if>
        </where>
    </select>

</mapper>
