<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">
	<div class="row">
				<span style="font-size: 19px; height: 32px"> 房间名称:
					&nbsp;&nbsp;{{room.roomName}}</span>
				<div>
					<span style="font-size: 15px; margin-left =5%; height: 32px">
						登记人数 &nbsp;&nbsp;&nbsp;: &nbsp;&nbsp;&nbsp;{{room.roomRegisterExisting}}</span>
				   <a class="layui-btn layui-btn-small sxBtn" href="javascript:history.go(-1)"  title="返回">
				     <i class="layui-icon" style="font-size: 125%;margin-left: 87%;">返回</i>
				   </a>
				</div>
				<span style="font-size: 15px; height: 32px">本网用户 &nbsp;&nbsp;&nbsp;:
					&nbsp;&nbsp;&nbsp;{{room.networkPhoneExisting+room.networkExisting}}</span>
				
	 </div>
	 <div style="margin-top: 1%;"></div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
     </div>
    
    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading" style="font-size: 23px">{{title}}</div>
			<form class="form-horizontal">

				<!-- 用户信息 -->
				<table class="textTable">
					<tr>
						<td class="leftTd"><label>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名:</label></td>
						<td style="width: 31%;">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.name}}</td>
						<td class="leftTd"><label>性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别:</label></td>
						<td v-if="hnslSchoolUser.sex==0">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;女
						</td>
						<td v-else>
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;男
						</td>
					</tr>
					<tr>
						<td class="leftTd"><label>地&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;址:</label></td>
						<td style="width: 31%;">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.clientSfzSite}}</td>
						<td class="leftTd"><label>籍贯:</label></td>
						<td >
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.nativeplace}}</td>
					</tr>
					<tr>
						
						<td class="leftTd"><label>入学年份:</label></td>
						<td style="width: 31%;">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.enroltime}}</td>
						<td class="leftTd"><label>手机号码1:</label></td>
						<td>
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.phone}}</td>
					</tr>
					<tr>
						<td class="leftTd"><label>手机号码2:</label></td>
						<td style="width: 31%;">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.phoneTwo}}</td>
						<td class="leftTd"><label>本异网:</label></td>
						<td v-if="hnslSchoolUser.network==0">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;异网
						</td>
						<td v-else>
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本网
						</td>
					</tr>
					
					<tr>
						<td class="leftTd"><label>账户余额:</label></td>
						<td style="width: 31%;">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.balance}}</td>
						<td class="leftTd"><label>手机型号:</label></td>
						<td>
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.phoneBrand}}</td>
					</tr>
					<tr>
						<td class="leftTd"><label>套餐费用:</label></td>
						<td style="width: 31%;">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.setmeal}}</td>
						<td class="leftTd"><label>流量套餐:</label></td>
						<td>
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.flow}}</td>
					</tr>
					<tr>
						<td class="leftTd"><label>语音套餐:</label></td>
						<td style="width: 31%;">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.voice}}</td>
						<td class="leftTd"><label>翼支付状态:</label></td>
						<td v-if="hnslSchoolUser.yiPay==0">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;没开通
						</td>
						<td v-else>
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;已开通
						</td>
					</tr>
					<tr>
						<td class="leftTd"><label>宽带运营商:</label></td>
						<td style="width: 31%;">
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.operator}}</td>
						<td class="leftTd"><label>宽带速度:</label></td>
						<td>
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{hnslSchoolUser.networkVelocity}}</td>
					</tr>
				</table>
				<div class="form-group">
					<div class="col-sm-2 control-label"></div>
					&nbsp;&nbsp;<input type="button"
						style="margin-left: 123%;margin-top: 3%;font-size: 120%;"
						class="btn btn-warning" @click="location.href='/hnxtadmin/modules/hnsl/hnslbuilding.html'" value="视图列表" />
						<input type="button"
						style="margin-left: 74%;margin-top: -20%;font-size: 122%;"
						class="btn btn-warning" @click="cancle" value="返回上页" />
				</div>
				</div>
		</form>
	</div>
</div>

<script src="../../js/modules/hnsl/hnslschooluser.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>