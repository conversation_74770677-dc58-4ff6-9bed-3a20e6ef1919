<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslConfigurationMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_configuration a
        <where>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.configurationName != null">
                AND a.configuration_name LIKE CONCAT('%', #{param.configurationName}, '%')
            </if>
            <if test="param.configurationNumber != null">
                AND a.configuration_number LIKE CONCAT('%', #{param.configurationNumber}, '%')
            </if>
            <if test="param.configurationType != null">
                AND a.configuration_type LIKE CONCAT('%', #{param.configurationType}, '%')
            </if>
            <if test="param.status != null">
                AND a.status LIKE CONCAT('%', #{param.status}, '%')
            </if>
            <if test="param.configurationSet != null">
                AND a.configuration_set LIKE CONCAT('%', #{param.configurationSet}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.updated_date LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslConfiguration">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslConfiguration">
        <include refid="selectSql"></include>
    </select>

</mapper>
