<!-- 订单统计 -->
<template>
  <div class="ele-body">
    <el-card shadow="never">
      <div slot="header">
        <el-form :inline="true" class="ele-form-search">
          <el-form-item label="统计日期:">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="dateChange"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>

      <!-- 统计数据展示 -->
      <div class="statistics-container">
        <!-- 订单总数展示 -->
        <el-row :gutter="20" class="mb-20">
          <el-col :span="24">
            <div class="total-orders">
              <span class="total-label">订单总数：</span>
              <span class="total-value">{{ totalOrders }}</span>
              <span class="total-unit">单</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div ref="chartContainer" style="width: 100%; height: 400px;"></div>
          </el-col>
        </el-row>

        <!-- 每日订单走势图 -->
        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <div ref="trendChartContainer" style="width: 100%; height: 400px;"></div>
          </el-col>
        </el-row>

        <!-- 按模块名称汇总发展总量统计 -->
        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <div class="chart-title">按模块名称汇总发展总量统计</div>
            <el-table :data="moduleTypeTableData" border stripe class="full-width-table">
              <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
              <el-table-column prop="moduleName" label="模块名称" min-width="200" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="moduleId" label="模块编号" min-width="150" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="totalCount" label="订单总量" min-width="120" align="center">
                <template slot-scope="scope">
                  <span class="total-count-highlight">{{ scope.row.totalCount }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="cityCount" label="涉及地市数" min-width="120" align="center"></el-table-column>
              <el-table-column prop="percentage" label="占比(%)" min-width="100" align="center">
                <template slot-scope="scope">
                  <span class="percentage-text">{{ scope.row.percentage }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>

        <!-- 按地市汇总发展总量统计 -->
        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <div class="chart-title">按地市汇总发展总量统计</div>
            <el-table :data="cityTableData" border stripe class="full-width-table">
              <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
              <el-table-column prop="cityCode" label="地市编码" min-width="120" align="center"></el-table-column>
              <el-table-column prop="cityName" label="地市名称" min-width="120" align="center"></el-table-column>
              <el-table-column prop="totalCount" label="订单总量" min-width="120" align="center">
                <template slot-scope="scope">
                  <span class="total-count-highlight">{{ scope.row.totalCount }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="moduleCount" label="涉及模块数" min-width="120" align="center"></el-table-column>
              <el-table-column prop="percentage" label="占比(%)" min-width="100" align="center">
                <template slot-scope="scope">
                  <span class="percentage-text">{{ scope.row.percentage }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>

        <!-- 各地市按模块分类订单数量统计表 -->
        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <div class="chart-title">各地市按模块分类订单数量统计</div>
            <el-table :data="moduleTableData" border stripe>
              <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
              <el-table-column prop="cityCode" label="地市编码" width="100" align="center"></el-table-column>
              <el-table-column prop="cityName" label="地市名称" width="100" align="center"></el-table-column>
              <el-table-column prop="totalCount" label="总订单数" width="100" align="center">
                <template slot-scope="scope">
                  <span
                    class="clickable-total-count"
                    @click="showDailyOrdersChart(scope.row)"
                  >
                    {{ scope.row.totalCount }}
                  </span>
                </template>
              </el-table-column>
              <!-- 动态生成模块列 -->
              <el-table-column
                v-for="module in moduleList"
                :key="module.id"
                :prop="'module_' + module.id"
                :label="module.moduleName"
                align="center">
                <template slot-scope="scope">
                  {{ getModuleCount(scope.row, module.id) }}
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 每日订单数量条形图弹窗 -->
    <el-dialog
      :title="selectedCity ? `${selectedCity.cityName} 地市每日订单数量` : '每日订单数量统计'"
      :visible.sync="dailyOrdersDialogVisible"
      width="70%"
      custom-class="city-daily-orders-dialog"
    >
      <div ref="dailyOrdersChartContainer" style="width: 100%; height: 400px;"></div>

      <!-- 添加订单数量走势图 -->
      <div class="chart-subtitle"></div>
      <div ref="dailyOrdersTrendChartContainer" style="width: 100%; height: 400px;"></div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { getOrderCount, getDailyOrdersByCityCode, getOrderCountByModule, getOrderCountByModuleType, getOrderCountByCity } from '@/api/hnzsxH5/order';

export default {
  name: 'OrderStatistics',
  data() {
    return {
      dateRange: [this.formatDate(new Date()), this.formatDate(new Date())], // 默认今天到今天
      tableData: [],
      chartInstance: null,
      trendChartInstance: null,
      dailyOrdersChartInstance: null,
      dailyOrdersData: [],
      totalOrders: 0,
      moduleTableData: [], // 按模块分类的表格数据
      moduleList: [], // 模块列表
      moduleTypeTableData: [], // 按模块类型汇总的表格数据
      cityTableData: [], // 按地市汇总的表格数据
      dailyOrdersDialogVisible: false, // 每日订单数量弹窗显示状态
      selectedCity: null, // 当前选中的城市
      dailyOrdersTrendChartInstance: null, // 订单数量走势图实例
      cityMap: {
        '730': '岳阳',
        '731': '长沙',
        '732': '湘潭',
        '733': '株洲',
        '734': '衡阳',
        '735': '郴州',
        '736': '常德',
        '737': '益阳',
        '738': '娄底',
        '739': '邵阳',
        '743': '湘西',
        '744': '张家界',
        '745': '怀化',
        '746': '永州'
      }
    };
  },
  computed: {
    // 图表所需数据
    chartData() {
      return this.tableData.map(item => ({
        name: this.getCityName(item.cityCode),
        value: item.saleNum
      }));
    }
  },
  mounted() {
    this.initChart();
    this.initTrendChart();
    this.fetchData();
    this.fetchDailyOrdersData();
    this.fetchModuleData();
    this.fetchModuleTypeData();
    this.fetchCityData();
    // 监听窗口大小变化，重绘图表
    window.addEventListener('resize', this.resizeCharts);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.resizeCharts);
    // 销毁图表实例
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }
    if (this.trendChartInstance) {
      this.trendChartInstance.dispose();
      this.trendChartInstance = null;
    }
    if (this.dailyOrdersChartInstance) {
      this.dailyOrdersChartInstance.dispose();
      this.dailyOrdersChartInstance = null;
    }
    if (this.dailyOrdersTrendChartInstance) {
      this.dailyOrdersTrendChartInstance.dispose();
      this.dailyOrdersTrendChartInstance = null;
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      this.chartInstance = echarts.init(this.$refs.chartContainer);
      this.updateChart();
    },
    // 初始化趋势图表
    initTrendChart() {
      this.trendChartInstance = echarts.init(this.$refs.trendChartContainer);
      this.updateTrendChart();
    },
    // 更新图表数据
    updateChart() {
      if (!this.chartInstance) return;

      const option = {
        title: {
          text: '各地市订单数量统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: this.chartData.map(item => item.name)
        },
        series: [
          {
            name: '订单数量',
            type: 'pie',
            radius: '50%',
            data: this.chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };

      this.chartInstance.setOption(option);
    },
    // 更新趋势图表数据
    updateTrendChart() {
      if (!this.trendChartInstance) return;

      // 处理数据，按日期和城市分组
      const dates = [...new Set(this.dailyOrdersData.map(item => item.date))].sort();
      const cities = [...new Set(this.dailyOrdersData.map(item => item.cityCode))].sort();

      // 准备每个城市的数据系列
      const series = cities.map(cityCode => {
        // 筛选该城市的数据
        const cityData = dates.map(date => {
          const item = this.dailyOrdersData.find(d => d.date === date && d.cityCode === cityCode);
          return item ? item.orderCount : 0;
        });

        return {
          name: this.getCityName(cityCode),
          type: 'line',
          data: cityData,
          smooth: true
        };
      });

      const option = {
        title: {
          text: '各地市每日订单走势图',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: cities.map(cityCode => this.getCityName(cityCode)),
          type: 'scroll',
          orient: 'horizontal',
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '订单数量'
        },
        series: series
      };

      this.trendChartInstance.setOption(option);
    },
    // 窗口大小变化时重绘图表
    resizeCharts() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
      if (this.trendChartInstance) {
        this.trendChartInstance.resize();
      }
      if (this.dailyOrdersChartInstance && this.dailyOrdersDialogVisible) {
        this.dailyOrdersChartInstance.resize();
      }
      if (this.dailyOrdersTrendChartInstance && this.dailyOrdersDialogVisible) {
        this.dailyOrdersTrendChartInstance.resize();
      }
    },
    // 获取统计数据
    async fetchData() {
      try {
        // 如果没有选择日期范围，使用默认值（今天）
        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());
        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());

        const data = await getOrderCount({
          date: startDate,
          endDate: endDate
        });
        this.tableData = data || [];
        this.totalOrders = this.tableData.reduce((sum, item) => sum + (parseInt(item.saleNum) || 0), 0);
        this.$nextTick(() => {
          this.updateChart();
        });
      } catch (error) {
        this.$message.error('获取统计数据失败: ' + error.message);
      }
    },
    // 获取各地市每日订单数据
    async fetchDailyOrdersData() {
      try {
        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());
        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());

        const data = await getDailyOrdersByCityCode({
          date: startDate,
          endDate: endDate
        });

        this.dailyOrdersData = data || [];
        this.$nextTick(() => {
          this.updateTrendChart();
        });
      } catch (error) {
        this.$message.error('获取每日订单数据失败: ' + error.message);
      }
    },
    // 获取按模块分类的订单数据
    async fetchModuleData() {
      try {
        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());
        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());

        const result = await getOrderCountByModule({
          date: startDate,
          endDate: endDate
        });

        // 设置模块列表
        this.moduleList = result.modules || [];

        // 设置表格数据
        this.moduleTableData = result.cityData || [];

        // 计算总订单数
        this.totalOrders = this.moduleTableData.reduce((sum, city) => sum + (city.totalCount || 0), 0);

      } catch (error) {
        this.$message.error('获取按模块分类的订单数据失败: ' + error.message);
      }
    },
    // 获取按模块类型汇总的订单数据
    async fetchModuleTypeData() {
      try {
        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());
        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());

        const result = await getOrderCountByModuleType({
          date: startDate,
          endDate: endDate
        });

        // 设置模块类型汇总表格数据
        this.moduleTypeTableData = result || [];

      } catch (error) {
        this.$message.error('获取按模块类型汇总的订单数据失败: ' + error.message);
      }
    },
    // 获取按地市汇总的订单数据
    async fetchCityData() {
      try {
        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());
        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());

        const result = await getOrderCountByCity({
          date: startDate,
          endDate: endDate
        });

        // 设置地市汇总表格数据
        this.cityTableData = result || [];

      } catch (error) {
        this.$message.error('获取按地市汇总的订单数据失败: ' + error.message);
      }
    },
    // 获取指定城市和模块的订单数
    getModuleCount(row, moduleId) {
      if (!row.modules || !row.modules[moduleId]) {
        return 0;
      }
      return row.modules[moduleId];
    },
    // 日期变化事件
    dateChange(val) {
      if (val) {
        this.dateRange = val;
      } else {
        this.dateRange = [this.formatDate(new Date()), this.formatDate(new Date())];
      }
      this.fetchData();
      this.fetchDailyOrdersData();
      this.fetchModuleData();
      this.fetchModuleTypeData();
      this.fetchCityData();
    },
    // 获取城市名称
    getCityName(cityCode) {
      return this.cityMap[cityCode] || cityCode;
    },
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = ('0' + (date.getMonth() + 1)).slice(-2);
      const day = ('0' + date.getDate()).slice(-2);
      return `${year}-${month}-${day}`;
    },
    // 显示每日订单数量图表
    showDailyOrdersChart(city) {
      this.selectedCity = city;
      this.dailyOrdersDialogVisible = true;

      // 下一帧初始化图表
      this.$nextTick(() => {
        // 初始化柱状图
        if (this.dailyOrdersChartInstance) {
          this.dailyOrdersChartInstance.dispose();
        }
        this.dailyOrdersChartInstance = echarts.init(this.$refs.dailyOrdersChartContainer);
        this.updateDailyOrdersChart();

        // 初始化走势图
        if (this.dailyOrdersTrendChartInstance) {
          this.dailyOrdersTrendChartInstance.dispose();
        }
        this.dailyOrdersTrendChartInstance = echarts.init(this.$refs.dailyOrdersTrendChartContainer);
        this.updateDailyOrdersTrendChart();
      });
    },
    // 更新每日订单数量图表
    updateDailyOrdersChart() {
      if (!this.dailyOrdersChartInstance || !this.selectedCity) return;

      // 筛选当前城市的每日订单数据
      const cityDailyOrders = this.dailyOrdersData.filter(item =>
        item.cityCode === this.selectedCity.cityCode
      );

      // 按日期排序
      cityDailyOrders.sort((a, b) => a.date.localeCompare(b.date));

      // 提取日期和订单数量
      const dates = cityDailyOrders.map(item => item.date);
      const orderCounts = cityDailyOrders.map(item => item.orderCount);

      // 设置图表选项
      const option = {
        title: {
          text: `${this.selectedCity.cityName}地市每日订单数量`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates,
          axisLabel: {
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '订单数量'
        },
        series: [
          {
            name: '订单数量',
            type: 'bar',
            barWidth: '60%',
            data: orderCounts,
            itemStyle: {
              color: '#409EFF'
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}'
            }
          }
        ]
      };

      this.dailyOrdersChartInstance.setOption(option);
    },
    // 更新每日订单数量走势图（折线图）
    updateDailyOrdersTrendChart() {
      if (!this.dailyOrdersTrendChartInstance || !this.selectedCity) return;

      // 筛选当前城市的每日订单数据
      const cityDailyOrders = this.dailyOrdersData.filter(item =>
        item.cityCode === this.selectedCity.cityCode
      );

      // 按日期排序
      cityDailyOrders.sort((a, b) => a.date.localeCompare(b.date));

      // 提取日期和订单数量
      const dates = cityDailyOrders.map(item => item.date);
      const orderCounts = cityDailyOrders.map(item => item.orderCount);

      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '订单数量'
        },
        series: [
          {
            name: '订单数量',
            type: 'line',
            stack: '总量',
            emphasis: {
              focus: 'series'
            },
            data: orderCounts,
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              width: 3,
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(64,158,255,0.5)' // 渐变色起始颜色
                }, {
                  offset: 1, color: 'rgba(64,158,255,0.1)' // 渐变色结束颜色
                }]
              }
            }
          }
        ]
      };

      this.dailyOrdersTrendChartInstance.setOption(option);
    }
  }
};
</script>

<style scoped>
.statistics-container {
  padding: 10px;
}
.mt-20 {
  margin-top: 20px;
}
.mb-20 {
  margin-bottom: 20px;
}
/* 订单总数样式 */
.total-orders {
  text-align: center;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.total-label {
  font-size: 16px;
  color: #606266;
  margin-right: 10px;
}
.total-value {
  font-size: 24px;
  color: #409EFF;
  font-weight: bold;
  margin-right: 5px;
}
.total-unit {
  font-size: 14px;
  color: #909399;
}
.chart-title {
  font-size: 16px;
  color: #303133;
  text-align: center;
  margin-bottom: 15px;
  font-weight: normal;
  height: 22px;
  line-height: 22px;
}
/* 可点击的总订单数样式 */
.clickable-total-count {
  color: #409EFF;
  font-weight: bold;
  cursor: pointer;
  text-decoration: underline;
}
.clickable-total-count:hover {
  color: #66b1ff;
}
/* 对话框标题样式 */
.dialog-title {
  font-size: 16px;
  color: #303133;
  text-align: center;
  margin-bottom: 15px;
  font-weight: bold;
}
/* 图表副标题样式 */
.chart-subtitle {
  font-size: 16px;
  color: #303133;
  text-align: center;
  margin: 20px 0 15px 0;
  font-weight: bold;
}
/* 弹窗位置调整 */
.city-daily-orders-dialog {
  margin-left: 5% !important;
}
/* 模块类型汇总统计样式 */
.total-count-highlight {
  color: #409EFF;
  font-weight: bold;
  font-size: 16px;
}
.percentage-text {
  color: #67C23A;
  font-weight: bold;
}
/* 汇总统计表格样式优化 */
.full-width-table {
  width: 100%;
}
.full-width-table .el-table__cell {
  padding: 12px 8px;
  font-size: 14px;
}
.full-width-table .el-table__header-wrapper th {
  background-color: #f5f7fa;
  font-weight: bold;
  color: #303133;
}
.full-width-table .el-table__row {
  height: 50px;
}
.full-width-table .el-table__row:hover > td {
  background-color: #f5f7fa !important;
}
/* 表格标题间距调整 */
.chart-title {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}
</style>
