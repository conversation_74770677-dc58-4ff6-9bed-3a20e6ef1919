<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppCardCityMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        select
            a.*,
            m.MODULE_NAME,
            cs.*
        from
            hnzsx_card_city a
        inner join hnzsx_module m on a.MODULE_ID = m.ID
        inner join hnzsx_card_city_sales cs on a.ID = cs.CARD_CITY_ID
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE = #{param.cityCode}
            </if>
         
            <if test="param.moduleId != null">
                AND a.MODULE_ID = #{param.moduleId}
            </if>
            <if test="param.plateType != null">
                AND a.PLATE_TYPE = #{param.plateType}
            </if>
            <if test="param.plateName != null">
                AND a.PLATE_NAME LIKE CONCAT('%', #{param.plateName}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
        </where>
    </sql>

    <resultMap id="dataMap" type="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxCardCity">
        <id property="id" column="id"></id>
        <result property="plateType" column="PLATE_TYPE"></result>
        <result property="plateName" column="PLATE_NAME"></result>
        <result property="cityCode" column="CITY_CODE"></result>
        <result property="cityName" column="CITY_NAME"></result>
        <result property="moduleId" column="MODULE_ID"></result>
        <result property="moduleName" column="MODULE_NAME"></result>
        <result property="createdDate" column="CREATED_DATE"></result>
        <result property="updatedDate" column="UPDATED_DATE"></result>
        <collection property="salesList" javaType="list" ofType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxCardCitySales" column="id"
                    select="querySalesList">
            <id property="id" column="id"></id>
            <result property="cardCityId" column="CARD_CITY_ID"></result>
            <result property="grade" column="GRADE"></result>
            <result property="salesId" column="SALES_ID"></result>
            <result property="salesName" column="SALES_NAME"></result>
            <result property="blockNumber" column="BLOCK_NUMBER"></result>
            <result property="createdDate" column="CREATED_DATE"></result>
            <result property="updatedDate" column="UPDATED_DATE"></result>
        </collection>
    </resultMap>

    <select id="querySalesList" parameterType="Integer" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxCardCitySales">
        select * from hnzsx_card_city_sales
        where CARD_CITY_ID = #{id}
    </select>
    
    <!-- 分页查询 -->
    <select id="selectPageRel" resultMap="dataMap">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxCardCity">
        <include refid="selectSql"></include>
    </select>

    <!--  查询地市销售品号卡配置  -->
    <select id="queryCardConf" resultType="map">
        select
            CITY_CODE,
            CITY_NAME,
            max(BLOCK_NUMBER) as BLOCK_NUMBER
        from
            hnzsx_card_city c
        inner join hnzsx_card_city_sales s on
            c.ID = s.CARD_CITY_ID
        where
            <if test="param.cityCodeList != null and param.cityCodeList.size() > 0">
                c.CITY_CODE in
                <foreach item="item" collection="param.cityCodeList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.moduleId != null and param.moduleId != ''">
                and c.MODULE_ID = #{param.moduleId}
            </if>
            <if test="param.salesId != null and param.salesId != ''">
                and s.SALES_ID = #{param.salesId}
            </if>
            <if test="param.grade != null and param.grade != ''">
                and s.GRADE = #{param.grade}
            </if>

    </select>

</mapper>
