<!DOCTYPE html>
<html>
<head>
	<title>2</title>
	<meta charset="UTF-8">
	<META HTTP-EQUIV="pragma" CONTENT="no-cache">
	<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
	<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
	<META HTTP-EQUIV="expires" CONTENT="0">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
	<link rel="stylesheet" href="../../css/bootstrap.min.css">
	<link rel="stylesheet" href="../../css/font-awesome.min.css">
	<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
	<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
	<link rel="stylesheet" href="../../css/main.css">
	<link rel="stylesheet" href="../../css/task.css">
	<script src="../../libs/jquery.min.js"></script>
	<script src="../../plugins/layer/layer.js"></script>
	<script src="../../libs/bootstrap.min.js"></script>
	<script src="../../libs/vue.min.js"></script>
	<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
	<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
	<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

	<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
	<!-- select2组件 -->
	<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />
	<link rel="stylesheet" href="../../css/task.css">
	<!-- select2组件 -->
	<script src="../../plugins/select2/js/select2.min.js"></script>

	<script src="../../plugins/Daterangepicker/js/common.js"></script>
	<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

	<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
	<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
	<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

	<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">

		<!--头部引入-->
		<div class="row">
			<div class="form-group col-md-2">
				<label>任务:</label>
				<select class="form-control" style="height: 32px;" v-model="hnslTask.taskType">
					<option value="">所有任务</option>
					<option value="通用任务">通用任务</option>
					<option value="现时任务">现时任务</option>
				</select>
			</div>
			<div class="form-group col-md-2">
				<label>种类:</label>
				<select class="form-control" style="height: 32px;" v-model="hnslTask.taskKind">
					<option value="">所有种类</option>
					<option value="1">信息采集</option>
					<option value="2">老用户升级</option>
					<option value="3">异网策反</option>
					<option value="4">单宽带融合</option>
					<option value="5">线上软文转发</option>
					<option value="6">Q群截图</option>
					<option value="7">其他</option>
					<option value="8">服务截图</option>
				</select>
			</div>

			<div class="form-group col-md-2">
				<label>状态:</label>
				<select class="form-control" style="height: 32px;" v-model="hnslTask.status">
					<option value="">所有状态</option>
					<option value="已完成">已完成</option>
					<option value="未完成">未完成</option>
				</select>
			</div>

			<div class="form-group col-md-2">
				<label>时效:</label>
				<select class="form-control" style="height: 32px;" v-model="hnslTask.taskStatus">
					<option value="">所有时效</option>
					<option value="进行中">进行中</option>
					<option value="已过期">已过期</option>
				</select>
			</div>

			<div class="form-group col-md-2" style="height:40px">
				<label>搜索条件:</label>
				<input type="text" class="form-control" placeholder="项目名称/编号" v-model="hnslTask.taskCode" />
			</div>
		</div>
		<!--end-->

		<div class="grid-btn">
			<a v-if="hasPermission('hnsduser:query')" class="btn btn-primary" @click="query"><i class="fa fa-plus"></i>&nbsp;&nbsp;&nbsp;&nbsp;查询</a>
			<a v-if="hasPermission('hnsltask:save')" class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
			<a v-if="hasPermission('hnsltask:update')" class="btn btn-primary" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
			<a v-if="hasPermission('hnsltask:delete')" class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a>
		</div>
		<table id="jqGrid"></table>
		<div id="jqGridPager"></div>
	</div>

	<div v-show="addList" class="panel panel-default">
		<div class="panel-heading">添加任务</div>
		<form class="form-horizontal " style="width: 600px;">
			<div class="form-group "  style="margin-left: 0px;">
				<div class="col-sm-2 control-label">任务类型</div>
				<div class="col-sm-10">
					<!--<input type="text" class="form-control" v-model="hnslTask.taskType" placeholder="任务类型"/>-->
					<input name="sex" type="radio" v-model="hnslTaskAdd.taskKind" checked="checked" value="1"/>信息采集
					<input name="sex" type="radio" v-model="hnslTaskAdd.taskKind" value="2" />老用户升级
					<input name="sex" type="radio" v-model="hnslTaskAdd.taskKind" value="3"/>异网策反
					<input name="sex" type="radio" v-model="hnslTaskAdd.taskKind" value="4"/>单宽带融合
					<input name="sex" type="radio" v-model="hnslTaskAdd.taskKind" value="5"/>线上软文转发
					<input name="sex" type="radio" v-model="hnslTaskAdd.taskKind" value="6" />Q群截图上传
					<input name="sex" type="radio" v-model="hnslTaskAdd.taskKind" value="7"/>其他
					<input name="sex" type="radio" v-model="hnslTaskAdd.taskKind" value="8"/>服务截图上传
				</div>
			</div>
			<div class="form-group" style="margin-left: 0px;">
				<div class="col-sm-2 control-label">任务时间</div>
				<div class="input-group col-ms-2 ">
					<input class="form-control pull-left dateRange date-picker " style="width: 225px;"
						   id="dateTimeRange" @keyup.enter="query" value="" type="text"
						   placeholder="日期"> <span class="input-group-addon" style="width: 0%">
							<i class="fa fa-calendar bigger-110"></i>
 						</span>
					<input name="beginTime" id="beginTime" type="hidden" >
					<input name="endTime" id="endTime" type="hidden" >
				</div>
			</div>
			<div class="form-group" style="margin-left: 0px;">
				<div class="col-sm-2 control-label">任务范围</div>
				<div class="col-sm-10">
					<div class="col-sm-10" style="width: 50%;">
						<input type="text" class="form-control" v-model="hnslTaskAdd.taskScope" placeholder="请添加任务范围"/>
					</div>
					<div class="col-sm-2">
						<a class="btn btn-primary" @click="team_show()"><i class="fa fa-plus"></i>&nbsp;添加</a>
					</div>
					<div class="col-sm-2">
						<a class="btn btn-primary" @click="allSchool()"><i class="fa fa-plus"></i>&nbsp;全选</a>
					</div>
				</div>
			</div>
			<div v-show="taskCode" style="width: 100%;margin-left: 200px;">
				<!--所属楼栋树-->
				<table class="textTable">
					<span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;</span>
					<div class="tree_pag" style="height: 450px;">
						<div class="tree_content" style="height:50px;">
							<h5 style="height: 30px;float: left;width:250px;position: relative;overflow: auto;">选择楼栋</h5>
							<div style="height: 40px;float: right;width: 450px;position: relative;overflow: auto;">
								<input type="text" style="width:150px" v-model="schoolName"  placeholder="请输入学校名称" />
								<a v-if="hasPermission('hnsdgoods:query')" class="btn btn-primary"  @click="querySchool" style="margin-left:1%">&nbsp;查询</a>
							</div>
						</div>

						<div class="tree_content" style="height: 310px;">
							<!--左边树状图-->
							<div class="left_tree" >
								<div class="treebox scrollXY">
									<div class="tree">
										<ul >
											<li class="main" v-for="(teamTrees , index) in teamTree" @dblclick="addCity(teamTrees,index)" v-show="teamTrees.hnslBuilding.length!=0">
												<a @click="openBuildingLeft(teamTrees.schoolCode)">{{teamTrees.schoolName}}</a>
												<ul :id="teamTrees.schoolCode+'left'">
													<li v-for="(itme , indexs ) in teamTrees.hnslBuilding"><a @click="addTeam(teamTrees,itme,index,indexs)">{{itme.buildingName}}</a></li>
												</ul>
											</li>
										</ul>
									</div>
								</div>
							</div>

							<!--右边树状图-->
							<div class="right_tree">
								<div class="treebox scrollXY">
									<div class="tree">
										<ul>
											<li class="main" v-for="(teamTrees , index) in teamTreeLeft" @dblclick="delCity(index)" v-show="teamTrees.hnslBuilding.length!=0">
												<a @click="openBuildingRight(teamTrees.schoolCode)">{{teamTrees.schoolName}}</a>
												<ul :id="teamTrees.schoolCode+'right'">
													<li v-for="(itme , indexs ) in teamTrees.hnslBuilding"><a @click="delTeam(index,indexs)">{{itme.buildingName}}</a></li>
												</ul>
											</li>
										</ul>
									</div>
								</div>
							</div>
						</div>
						<div class="task_submit">
							<a  class="btn btn-primary" @click="team_close()" style="float: left; margin-left: 4px;">确定</a>
							<a  class="btn btn-warning" @click="team_back()" style="float: left; margin-left: 10px;">取消</a>
						</div>
					</div>
				</table>
			</div>
			<div class="form-group"  style="margin-left: 0px;" >
				<div class="col-sm-2 control-label">接收人</div>
				<div class="col-sm-10">
					<div class="col-sm-10">
						<input type="text" class="form-control" v-model="hnslTaskAdd.taskCount" placeholder="请选择接收人"/>
					</div>
					<div class="col-sm-2">
						<a class="btn btn-primary" @click="task_endtime()"><i class="fa fa-plus"></i>&nbsp;添加</a>
					</div>
				</div>
			</div>
			<div v-show="taskEndtime" style="width: 100%">
				<div class="form-group textTable">
					<div><p>选择接收人</p></div>
					<div class="task_table row">
						<div >
							<input type="checkbox" value="1" v-model="xlevel"/>校园经理
						</div>
						<div >
							<input type="checkbox" value="2" v-model="onelevel" />一级人员
						</div>
						<div >
							<input type="checkbox" value="3" v-model="twolevel"/>二级人员
						</div>
						<div >
							<input type="checkbox" value="4" v-model="threelevel "/>三级人员
						</div>
						<div >
							<input type="checkbox"  value="5" v-model="buildingLong"/>栋长
						</div>
						<div >
							<input type="checkbox"  value="5" v-model="allUser" onclick="clickAll(this)" />全选
						</div>
						<div >
							<input type="text"  placeholder="单行输入" v-model="userData"  />
						</div>
					</div>
					<div class="task_table row">
						<div>
							<select
									style="height: 32px;" v-model="grouping">
								<option value=''>选择分组</option>
								<option v-for="itme in group" v-bind:value="itme.groupingCode">
									{{itme.groupingName}}</option>
							</select>
						</div>
						<div >
							<a class="btn btn-primary" @click="userReload"><i class="fa fa-plus"></i>&nbsp;搜索</a>
						</div>
					</div>
					<div>
						<table id="hnslUser"></table>
						<div id="hnslUserPager"></div>
					</div>
					<div style="width: 500px;">
						<p style="margin-left: 200px;margin-top: 10px;">
							<a  class="btn btn-primary" @click="endtime_hide()"><i class="fa fa-plus"></i>&nbsp;确定</a>
							<a  class="btn btn-primary" @click="endtime_hide()" style="margin-left: 20px;"><i class="fa fa-plus"></i>&nbsp;取消</a>
						</p>
					</div>
				</div>
			</div>

			<div class="form-group" style="margin-left: 0px;">
				<div class="col-sm-2 control-label">任务标题</div>
				<div class="col-sm-10">
					<div class="col-sm-10">
						<input type="text" class="form-control" v-model="hnslTaskAdd.taskTitle" placeholder="请添加任务标题"/>
					</div>
				</div>
			</div>

			<div class="form-group" style="margin-left: 0px;">
				<div class="col-sm-2 control-label">任务达标量</div>
				<div class="col-sm-10">
					<div class="col-sm-10">
						<input type="text" class="form-control" oninput="value=value.replace(/[^\d]/g,'')" v-model="hnslTaskAdd.taskAmount" placeholder="请添加任务达标量"/>
					</div>
				</div>
			</div>

			<div class="form-group"  style="margin-left: 0px;margin-left: 0px;">
				<div class="col-sm-2 control-label">任务内容</div>
				<div class="col-sm-10">
					<textarea  class="form-control" v-model="hnslTaskAdd.taskContent" cols="20" wrap="hard"
							   style="height: 100px;"  placeholder="请输入内容"></textarea>
				</div>
			</div>
			<div class="form-group"  style="margin-left: 0px;">
				<div class="col-sm-2 control-label">任务奖励</div>
				<div class="col-sm-10">
					<select class="form-control" style="height: 32px;" v-model="hnslTaskAdd.taskAward">
						<option value='' >请选择积分数量</option>
						<option value='0' >0分</option>
						<option value='5' >5分</option>
						<option value='10' >10分</option>
						<option value='20' >20分</option>
						<option value='30' >30分</option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label"></div>
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div>
		</form>
	</div>

	<div v-show="detailsView">
		<div class="task-x-nav">
			<span class="layui-breadcrumb" style="visibility: visible;">
        <a>任务管理</a><span lay-separator=""> / </span><a><cite>查看详情</cite></a>
      </span>
			<a class="layui-btn layui-btn-small sxBtn"  @click="replace()"  title="关闭">
				<i class="layui-icon">×</i></a>
			<a class="layui-btn layui-btn-small sxBtn"  @click="exportTask()"  title="导出">导出
			</a>
		</div>
		<div class="x-body">
			<div class="xblock">
				<p class="left_text">任务编号：{{hnslTaskDetails.taskCode}}</p>
				<span class="x-right" style="line-height:40px">奖励：{{hnslTaskDetails.taskAward}}积分&nbsp;&nbsp;&nbsp;&nbsp;任务种类：{{hnslTaskDetails.taskTitle}}&nbsp;&nbsp;&nbsp;&nbsp;截止时间：{{hnslTaskDetails.endtime}}</span>
			</div>
			<table class="layui-table" style="margin: 0; min-width: 1300px; margin-bottom: 50px;">
				<thead>
				<tr v-if="hnslTaskDetails.taskKind=='2' || hnslTaskDetails.taskKind=='3' || hnslTaskDetails.taskKind=='4'">
					<th>任务</th>
					<th>发布时间</th>
				</tr>
				<tr v-else>
					<th>任务</th>
					<th>接收人</th>
					<th>发布时间</th>
				</tr>
				</thead>
				<tbody>
				<tr  v-if="hnslTaskDetails.taskKind=='2' || hnslTaskDetails.taskKind=='3' || hnslTaskDetails.taskKind=='4'">
					<td style="width: 50%;">{{hnslTaskDetails.taskContent}}</td>
					<td style="width: 50%;">{{hnslTaskDetails.starttime}}</td>
				</tr>
				<tr v-else>
					<td style="width: 33%;">{{hnslTaskDetails.taskContent}}</td>
					<td style="width: 33%;">{{hnslTaskDetails.taskCount?hnslTaskDetails.taskCount:'无'}}</td>
					<td style="width: 33%;">{{hnslTaskDetails.starttime}}</td>
				</tr>
				</tbody>
			</table>

			<div class="xblock">
				<p class="left_text">人员列表</p>
			</div>
			<div  v-show="hnslTaskDetails.taskKind=='2' || hnslTaskDetails.taskKind=='3' || hnslTaskDetails.taskKind=='4'" class="layui-row">
				<form class="layui-form layui-col-md12 x-so">
					<div class="layui-input-inline">
						<span>条件：</span>
						<input type="text" name="username" autocomplete="off" placeholder="姓名" v-model="userName" class="layui-input">
					</div>
					<a class="layui-btn serBtn" @click="taskUserReload()"><i class="layui-icon"></i>搜索</a>
				</form>
			</div>
			<div v-show="hnslTaskDetails.taskKind=='2' || hnslTaskDetails.taskKind=='3' || hnslTaskDetails.taskKind=='4'" >
				<table id="hnslTaskUser"></table>
				<div id="hnslTaskUserPager"></div>
			</div>
			<div v-show="hnslTaskDetails.taskKind=='1'">
				<table id="hnslSchoolUser" ></table>
				<div id="hnslSchoolUserPager" ></div>
			</div>
			<div v-show="hnslTaskDetails.taskKind=='7'">
				<table id="hnslTaskRests" ></table>
				<div id="hnslTaskRestsPager" ></div>
			</div>
			<div v-show="hnslTaskDetails.taskKind=='6' || hnslTaskDetails.taskKind=='8'">
				<table id="hnslTaskImage" ></table>
				<div id="hnslTaskImagePager" ></div>
			</div>
			<div v-show="hnslTaskDetails.taskKind=='5'">
				<table id="hnslTaskUpload" ></table>
				<div id="hnslTaskUploadPager" ></div>
			</div>
		</div>
	</div>

	<div v-show="userDetailsView">
		<div class="task-x-nav">
			<span class="layui-breadcrumb" style="visibility: visible;">
        <a>任务管理</a><span lay-separator=""> / </span><a><cite>---查看详情</cite></a><a><cite>---信息采集详情</cite></a>
      </span>
			<a class="layui-btn layui-btn-small sxBtn"  @click="detailsReplace()"  title="关闭">
				<i class="layui-icon">×</i></a>
		</div>
		<div class="x-body">
			<div class="xblock">
				<p class="left_text">任务编号：{{hnslTaskDetails.taskCode}}</p>
				<span class="x-right" style="line-height:40px">
					<a  v-if="receiveAward=='2'"  @click="buidingDetail(hnslschoolUser.id)">完成</a>
					<a  v-if="receiveAward=='1'">已完成</a>
					<a  v-if="receiveAward=='3'">待领取</a>
					<a  v-if="receiveAward=='0'">进行中</a>
				</span>
			</div>
			<table id="schoolUserDetails"></table>
			<div id="schoolUserDetailsPager"></div>
		</div>
	</div>
</div>
<script src="../../js/modules/hnsl/hnsltask.js"></script>
<script src="../../js/components.js"></script>

</body>
</html>