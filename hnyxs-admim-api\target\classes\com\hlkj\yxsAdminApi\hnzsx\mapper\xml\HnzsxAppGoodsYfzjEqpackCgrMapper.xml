<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsYfzjEqpackCgrMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_yfzj_eqpack_cgr a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.createDate != null">
                AND a.CREATE_DATE LIKE CONCAT('%', #{param.createDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.UPDATE_DATE LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
            <if test="param.operator != null">
                AND a.OPERATOR LIKE CONCAT('%', #{param.operator}, '%')
            </if>
            <if test="param.equityId != null">
                AND a.EQUITY_ID LIKE CONCAT('%', #{param.equityId}, '%')
            </if>
            <if test="param.equityName != null">
                AND a.EQUITY_NAME LIKE CONCAT('%', #{param.equityName}, '%')
            </if>
            <if test="param.equityGrade != null">
                AND a.EQUITY_GRADE LIKE CONCAT('%', #{param.equityGrade}, '%')
            </if>
            <if test="param.equipmentTypeName != null">
                AND a.EQUIPMENT_TYPE_NAME LIKE CONCAT('%', #{param.equipmentTypeName}, '%')
            </if>
            <if test="param.saleName != null">
                AND a.SALE_NAME LIKE CONCAT('%', #{param.saleName}, '%')
            </if>
            <if test="param.eqAction != null">
                AND a.EQ_ACTION LIKE CONCAT('%', #{param.eqAction}, '%')
            </if>
            <if test="param.acceptanceSteps != null">
                AND a.ACCEPTANCE_STEPS LIKE CONCAT('%', #{param.acceptanceSteps}, '%')
            </if>
            <if test="param.exhibitStatus != null">
                AND a.EXHIBIT_STATUS LIKE CONCAT('%', #{param.exhibitStatus}, '%')
            </if>
            <if test="param.equipmentType != null">
                AND a.EQUIPMENT_TYPE LIKE CONCAT('%', #{param.equipmentType}, '%')
            </if>
            <if test="param.contractTypeCode != null">
                AND a.CONTRACT_TYPE_CODE LIKE CONCAT('%', #{param.contractTypeCode}, '%')
            </if>
            <if test="param.contractTypeName != null">
                AND a.CONTRACT_TYPE_NAME LIKE CONCAT('%', #{param.contractTypeName}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsYfzjEqpackCgr">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsYfzjEqpackCgr">
        <include refid="selectSql"></include>
    </select>

</mapper>
