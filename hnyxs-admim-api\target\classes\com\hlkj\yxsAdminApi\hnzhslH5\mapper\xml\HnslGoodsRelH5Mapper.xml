<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslGoodsRelH5Mapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsRel" id="hnslGoodsRelMap">
        <result property="id" column="ID"/>
        <result property="relType" column="REL_TYPE"/>
        <result property="status" column="STATUS"/>
        <result property="goodsNumberRel" column="GOODS_NUMBER_REL"/>
        <result property="goodsNumberRelMsg" column="GOODS_NUMBER_REL_MSG"/>
        <result property="goodsNumber" column="GOODS_NUMBER"/>
        <result property="goodsNumberRelName" column="GOODS_NUMBER_REL_NAME"/>
        <result property="goodsSchoolDiscern" column="GOODS_SCHOOL_DISCERN"/>
        <result property="prePrice" column="PRE_PRICE"/>
        <result property="deliverPrice" column="DELIVER_PRICE"/>
    </resultMap>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_goods_rel a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsNumber != null">
                AND a.GOODS_NUMBER LIKE CONCAT('%', #{param.goodsNumber}, '%')
            </if>
            <if test="param.goodsNumberRel != null">
                AND a.GOODS_NUMBER_REL LIKE CONCAT('%', #{param.goodsNumberRel}, '%')
            </if>
            <if test="param.relType != null">
                AND a.REL_TYPE LIKE CONCAT('%', #{param.relType}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS LIKE CONCAT('%', #{param.status}, '%')
            </if>
            <if test="param.goodsNumberRelMsg != null">
                AND a.GOODS_NUMBER_REL_MSG LIKE CONCAT('%', #{param.goodsNumberRelMsg}, '%')
            </if>
            <if test="param.goodsSchoolDiscern != null">
                AND a.GOODS_SCHOOL_DISCERN = #{param.goodsSchoolDiscern}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsRel">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsRel">
        <include refid="selectSql"></include>
    </select>

    <select id="selectGoodsRelList" resultMap="hnslGoodsRelMap" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsRel">
        select ID,REL_TYPE,GOODS_NUMBER_REL,GOODS_NUMBER_REL_MSG,GOODS_NUMBER,GOODS_SCHOOL_DISCERN,GOODS_NUMBER_REL_NAME from HNSL_GOODS_REL
        where STATUS = '1'
        and GOODS_NUMBER = #{goodsNumber,jdbcType=VARCHAR}
        <if test="goodsNumberRel != null and goodsNumberRel != ''">
            and GOODS_NUMBER_REL = #{goodsNumberRel,jdbcType=VARCHAR}
        </if>
        <if test="relType != null and relType != ''">
            and REL_TYPE = #{relType,jdbcType=VARCHAR}
        </if>
        <if test="goodsNumberRelMsg != null and goodsNumberRelMsg != ''">
            and GOODS_NUMBER_REL_MSG = #{goodsNumberRelMsg,jdbcType=VARCHAR}
        </if>
        <if test="goodsSchoolDiscern != null and goodsSchoolDiscern != 0">
            and GOODS_SCHOOL_DISCERN = #{goodsSchoolDiscern}
        </if>
    </select>

    <delete id="deleteGoodsRel"  parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsRel">
        delete from HNSL_GOODS_REL where GOODS_NUMBER = #{goodsNumber}
        <if test="relType != null">
            AND REL_TYPE = #{relType}
        </if>
    </delete>

    <update id="updateByGoodsNumber" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsRel">
        update HNSL_GOODS_REL
        <set>
            <if test="relType != null">REL_TYPE = #{relType}, </if>
            <if test="goodsNumberRelMsg != null">GOODS_NUMBER_REL_MSG = #{goodsNumberRelMsg}, </if>
            <if test="status != null">STATUS = #{status}, </if>
            <if test="goodsNumberRel != null">GOODS_NUMBER_REL = #{goodsNumberRel}, </if>
        </set>
        where GOODS_NUMBER = #{goodsNumber}
        <if test="relType != null">
            AND REL_TYPE = #{relType}
        </if>
        <if test="goodsNumberRel != null">
            AND GOODS_NUMBER_REL = #{goodsNumberRel}
        </if>
    </update>

</mapper>
