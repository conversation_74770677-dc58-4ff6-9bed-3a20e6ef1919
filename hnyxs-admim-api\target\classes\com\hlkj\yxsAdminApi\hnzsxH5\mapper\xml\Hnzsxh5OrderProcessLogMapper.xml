<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5OrderProcessLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5OrderProcessLog">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="scene_inst_id" property="sceneInstId" />
        <result column="process_node" property="processNode" />
        <result column="status" property="status" />
        <result column="request_params" property="requestParams" />
        <result column="response_result" property="responseResult" />
        <result column="error_msg" property="errorMsg" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_staff_id" property="operatorStaffId" />
        <result column="operator_name" property="operatorName" />
        <result column="class_name" property="className" />
        <result column="method_name" property="methodName" />
        <result column="execution_time" property="executionTime" />
        <result column="create_time" property="createTime" />
        <result column="cust_order_id" property="custOrderId" />
        <result column="interface_request_params" property="interfaceRequestParams" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, scene_inst_id, process_node, status, request_params, response_result, error_msg, cust_order_id, interface_request_params, operator_id, operator_staff_id, operator_name, class_name, method_name, execution_time, create_time
    </sql>

    <!-- 查询条件（避免左侧通配符导致索引失效） -->
    <sql id="Query_Where_Clause">
        <where>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <!-- 有索引的字段只使用右侧通配符（order_no/scene_inst_id/process_node） -->
            <if test="param.orderNo != null and param.orderNo != ''">
                AND a.order_no LIKE CONCAT(#{param.orderNo}, '%')  <!-- 利用 idx_order_no 索引 -->
            </if>
            <if test="param.sceneInstId != null and param.sceneInstId != ''">
                AND a.scene_inst_id LIKE CONCAT(#{param.sceneInstId}, '%')  <!-- 利用 idx_scene_inst_id 索引 -->
            </if>
            <if test="param.processNode != null and param.processNode != ''">
                AND a.process_node LIKE CONCAT(#{param.processNode}, '%')  <!-- 利用 idx_process_node 索引 -->
            </if>
            <if test="param.custOrderId != null and param.custOrderId != ''">
                AND a.cust_order_id LIKE CONCAT(#{param.custOrderId}, '%')
            </if>
            <if test="param.status != null">
                AND a.status = #{param.status}  <!-- 利用 idx_status 索引 -->
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                AND a.create_time &gt;= STR_TO_DATE(#{param.startTime}, '%Y-%m-%d %H:%i:%s')  <!-- 利用 idx_create_time 索引 -->
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                AND a.create_time &lt;= STR_TO_DATE(#{param.endTime}, '%Y-%m-%d %H:%i:%s')  <!-- 利用 idx_create_time 索引 -->
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hnzsxh5_order_process_log a
        <include refid="Query_Where_Clause"/>
        ORDER BY a.create_time DESC
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hnzsxh5_order_process_log a
        <include refid="Query_Where_Clause"/>
        ORDER BY a.create_time DESC
    </select>
    
    <!-- 使用动态表名进行分页查询 -->
    <select id="selectPageRelByTableName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName} a
        <include refid="Query_Where_Clause"/>
        ORDER BY a.create_time DESC
    </select>

    <!-- 使用动态表名查询全部 -->
    <select id="selectListRelByTableName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName} a
        <include refid="Query_Where_Clause"/>
        ORDER BY a.create_time DESC
    </select>
    
    <!-- 使用动态表名查询符合条件的记录ID列表 -->
    <select id="selectIdsByConditionWithTableName" resultType="java.lang.Long">
        SELECT id FROM ${tableName}
        WHERE status = #{status}
        AND process_node IN
        <foreach collection="processNodes" item="processNode" open="(" separator="," close=")">
            #{processNode}
        </foreach>
    </select>
    
    <!-- 使用动态表名根据ID列表批量更新状态 -->
    <update id="updateStatusByIdsWithTableName">
        UPDATE ${tableName} 
        SET status = #{newStatus}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    
    <!-- 使用动态表名查询符合流程节点条件的日志记录对象 -->
    <select id="selectIdResponseAndStatusByProcessNodesWithTableName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName}
        WHERE process_node IN ("订单提交生单","保存审批单","收费确认","0元收费确认")
        AND response_result IS NOT NULL
    </select>
</mapper> 