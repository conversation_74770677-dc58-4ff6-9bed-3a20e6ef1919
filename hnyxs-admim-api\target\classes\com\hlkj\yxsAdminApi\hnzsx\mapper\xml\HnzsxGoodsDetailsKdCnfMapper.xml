<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxGoodsDetailsKdCnfMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_details_kd_cnf a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.rate != null">
                AND a.RATE LIKE CONCAT('%', #{param.rate}, '%')
            </if>
            <if test="param.fqNumber != null">
                AND a.FQ_NUMBER LIKE CONCAT('%', #{param.fqNumber}, '%')
            </if>
            <if test="param.salesId != null">
                AND a.SALES_ID LIKE CONCAT('%', #{param.salesId}, '%')
            </if>
            <if test="param.salesName != null">
                AND a.SALES_NAME LIKE CONCAT('%', #{param.salesName}, '%')
            </if>
            <if test="param.money != null">
                AND a.MONEY = #{param.money}
            </if>
            <if test="param.goodsDetailsId != null">
                AND a.GOODS_DETAILS_ID = #{param.goodsDetailsId}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsDetailsKdCnf">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsDetailsKdCnf">
        <include refid="selectSql"></include>
    </select>

</mapper>
