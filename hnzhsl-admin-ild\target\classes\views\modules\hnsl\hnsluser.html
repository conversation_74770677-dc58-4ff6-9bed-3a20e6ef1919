<!DOCTYPE html>
<html>
<head>
    <title>2</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
            name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet"
          href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">

    <link rel="stylesheet"
          href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css"
          rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script
            src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script
            src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->
    <script src="../../js/common.js"></script>
    <style type="text/css">

        .templateShow-Info {
            float: left;
            width: 100%;
            font-size: 20px;
            padding: 10px 25px 0;
        }

        .templateShow-Info p {
            font-size: 20px;
            float: left;
            text-align: center;
            margin: 10px 0 10px;
            margin: 10px 0 10px;
        }

        .templateShow-Info p:nth-child(2) {
            color: #999 !important;
            font-size: 16px;
            line-height: 28px;
        }
    </style>
</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showIndex" id="isShowList">

        <div class="row">
            <div class="form-group col-md-2">
                <label>身份</label> <select class="form-control"
                                            style="height: 32px;" v-model="hnslUser.statusSf">
                <option value=''>全部</option>
                <option v-for="itme in role" v-bind:value="itme.roleId">
                    {{itme.roleName}}
                </option>
            </select>
            </div>

            <div class="form-group col-md-2">
                <label>状态:</label> <select class="form-control"
                                             style="height: 32px;" v-model="hnslUser.status">
                <option value=''>全部</option>
                <option v-for="itme in status" v-bind:value="itme.statusId">
                    {{itme.statusName}}
                </option>
            </select>
            </div>

            <div class="form-group col-md-2">
                <label>所属城市:</label> <select class="form-control" @change="onChange()"
                                                 style="height: 32px;" v-model="hnslUser.cityCode">
                <option value=''>全部</option>
                <option v-for="itme in city" v-bind:value="itme.cityCode">
                    {{itme.cityName}}
                </option>
            </select>
            </div>

            <div class="form-group col-md-2" style="height: 40px">
                <label>学校:</label> <input type="text" class="form-control"
                                            placeholder="学校" v-model="schoolName"/>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>身份证|号码|姓名:</label> <input type="text" class="form-control"
                                                        placeholder="身份证|号码|姓名" v-model="condition"/>
            </div>

            <div class="form-group col-md-2" style="height: 32px;">
                <label>创建日期:</label>
                <div class="input-group col-ms-2 ">
                    <input class="form-control pull-left dateRange date-picker "
                           id="dateTimeRange" @keyup.enter="query" value="" type="text"
                           placeholder="日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
						</span> <input name="beginTime" id="beginTime" type="hidden">
                    <input name="endTime" id="endTime" type="hidden">
                </div>
            </div>

        </div>
        <div class="row">
            <div class="form-group col-md-2">
                <label>所属小组:</label>
                <select class="form-control" style="height: 32px;" v-model="hnslUser.grouping">
                    <option value=''>全部</option>
                    <option v-for="itme in group" v-bind:value="itme.teamCode">
                        {{itme.teamName}}
                    </option>
                </select>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>上级号码:</label>
                <input type="text" class="form-control"
                       placeholder="请输入上级号码" v-model="hnslUser.statusSuperior"/>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>合伙人ID:</label>
                <input type="text" class="form-control"
                       placeholder="请输入合伙人ID" v-model="hnslUser.userId"/>
            </div>
        </div>

        <div class="grid-btn" style="margin-left: 17Px;">
            <a v-if="hasPermission('hnsluser:query')" class="btn btn-primary" @click="query">&nbsp;查询</a>
            <a v-if="hasPermission('hnsluser:update')" class="btn btn-primary" @click="update">&nbsp;修改</a>
            <a class="btn btn-primary" @click="templateShowI">&nbsp;批量导入</a>
            <a class="btn btn-primary" @click="exportUser">&nbsp;导出</a>
            <a class="btn btn-primary" @click="exportDayRecord">&nbsp;埋点记录导出</a>
            <a v-if="hasPermission('hnsluser:save')" class="btn btn-primary" @click="adminAdd">&nbsp;admin新增</a>
            <a v-if="hasPermission('hnsluser:adminUpdate')" class="btn btn-primary"
               @click="adminUpdate">&nbsp;admin修改</a>
            <a v-if="hasPermission('hnsluser:adminDetele')" class="btn btn-primary"
               @click="adminDetele">&nbsp;admin删除</a>
        </div>

        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>
    <!--模板下载 -->
    <div v-show="!templateShow" id="templateShow"
         class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <div class="form-horizontal" style="padding-top: 0px; width: 100%;">
            <form id="uploadImg" enctype="multipart/form-data">
                <div class="templateShow-Info">
                    <p>下载模板：</p>
                    <p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
                </div>
                <div style="margin-left: 125px;">
                    <a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
                </div>
                <div class="templateShow-Info">
                    <p>上传文件：</p>
                    <p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
                </div>
                <div style="margin-left: 125px;">
                    <a v-if="hasPermission('hnsduser:importUser')"
                       class="btn btn-primary" @click="importUser">&nbsp;开始导入</a> <input
                        style="display: none;" name="uploadFile" id="uploadFile"
                        type="file" @change="uploadFile"/>
                </div>
                <div style="width: 100%; text-align: center;">
                    <input type="button" class="btn btn-warning" @click="reload"
                           value="返回"/>
                </div>
            </form>
        </div>
    </div>
    <!-- 其他人员修改的详细信息 -->
    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading" style="font-size: 23px">{{title}}</div>
        <form class="form-horizontal">
            <!-- 用户信息 -->
            <table class="textTable">
                <!--  <span style="font-size: 18px;">&nbsp;&nbsp;&nbsp;账户信息</span>  -->
                <a style="margin-left: 10px" @click="noDesensitization(HnslUserInfo.id)"
                   class="btn btn-primary">免脱敏</a>
                <tr>
                    <td class="leftTd info-text"><label>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.userName"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userName}}--></td>
                    <td class="leftTd info-text" style="width:10%;text-align:center;"><label label>性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别:</label>
                    </td>
                    <td style="width:50%">
                        <select class="form-control"
                                style="height: 32px;" v-model="gender">
                            <option value='0'>未知</option>
                            <option value='1'>男</option>
                            <option value='2'>女</option>
                        </select>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userGender}}--></td>
                </tr>

                <tr>
                    <td class="leftTd info-text"><label>地&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;址:</label></td>
                    <td style="width: 31%;"><input type="text" v-model="HnslUserInfo.userSite"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userSite}}--></td>
                    <td class="leftTd info-text"><label>身&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;份:</label></td>
                    <td>
                        <select class="form-control"
                                style="height: 32px;" v-model="sf">
                            <option value='0'>无</option>
                            <option value='1'>校园经理</option>
                            <option value='2'>一级合伙人</option>
                            <option value='3'>二级合伙人</option>
                            <option value='4'>三级合伙人</option>
                            <option value='6'>地市管理员</option>
                            <option value='5'>省级管理员</option>
                        </select>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.statusSf}}--></td>
                </tr>

                <tr>
                    <td class="leftTd info-text"><label>身份证号:</label></td>
                    <td style="width: 31%;"><input type="text" v-model="HnslUserInfo.userSfz"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userSfz}}--></td>
                    <td class="leftTd info-text"><label>手机号码:</label></td>
                    <td><input type="text" v-model="HnslUserInfo.userPhone" :disabled="disabled"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userPhone}}--></td>
                </tr>

                <tr>
                    <td class="leftTd info-text"><label>银行卡号:</label></td>
                    <td style="width: 31%;">
                        <!--<input type="text" v-model="HnslUserInfo.bankNumber" readonly="readonly"  />-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.bankNumber}}
                    </td>
                    <td class="leftTd info-text"><label>销售编码:</label></td>
                    <td><input type="text" v-model="HnslUserInfo.salesCode" @blur="queryNumbers"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.salesCode}}--></td>
                </tr>

                <tr>
                    <td class="leftTd info-text"><label>揽机工号:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.numbers"/>
                    </td>
                    <td class="leftTd info-text"><label>QQ号码:</label></td>
                    <td style="width: 31%;"><input type="text" v-model="HnslUserInfo.flock"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.numbers}}--></td>
                </tr>
                <tr v-if="levelBool">
                    <td class="leftTd info-text"><label>上级联系号码修改:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.statusSuperior"/>
                    </td>
                </tr>
                <tr>
                    <td class="leftTd info-text"><label>来源渠道:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.channelType">
                            <option value='1'>中小学</option>
                            <option value='2'>高校（校园经理/管理员/学子支撑）</option>
                            <option value='3'>学子公司（学子合伙人）</option>
                            <option value='4'>校园店（门店代理商）</option>
                            <option value='5'>泛渠道（合作直销）</option>
                        </select>
                    </td>
                </tr>

                <!--<tr>
                    <td class="leftTd info-text"><label>地市:</label></td>
                    <td>
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.cityCode">
                            <option value='0'>无</option>
                            <option value='731'>长沙</option>
                            <option value='732'>湘潭</option>
                            <option value='733'>株洲</option>
                            <option value='734'>衡阳</option>
                            <option value='735'>郴州</option>
                            <option value='736'>常德</option>
                            <option value='737'>益阳</option>
                            <option value='738'>娄底</option>
                            <option value='739'>邵阳</option>
                            <option value='730'>岳阳</option>
                            <option value='743'>湘西</option>
                            <option value='744'>张家界</option>
                            <option value='745'>怀化</option>
                            <option value='746'>永州</option>
                        </select>
                    </td>
                </tr>-->
            </table>

            <!-- 楼栋信息 -->
            <table class="textTable">
                <!-- 	        <span style="font-size: 18px;">&nbsp;&nbsp;&nbsp;楼栋信息</span>
-->
                <div class="panel-heading" style="font-size: 23px">楼栋信息</div>
                <tr>
                    <td class="leftTd"><label>所属学校:</label></td>
                    <td style="width: 31%;">
                        <!--<select class="form-control"
                                style="height: 32px;" v-model="code">
                            <option value=''>全部</option>
                            <option v-for="itme in school" v-bind:value="itme.schoolCode">
                                {{itme.schoolName}}</option>
                        </select>-->
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.schoolName}}-->
                        <input type="text" v-model="HnslUserInfo.schoolName" disabled/>
                        <a v-show="bianji1" class="btn btn-primary" @click="school_show()"
                           style="float: left; margin-left: 4px; ">编辑</a>
                        <a v-show="bianji2" class="btn btn-primary" @click="school_close()"
                           style="float: left; margin-left: 4px;">确定</a>
                        <a v-show="bianji2" class="btn btn-warning" @click="school_back()"
                           style="float: left; margin-left: 4px;">取消</a>
                    </td>
                    <td class="leftTd"><label>管理楼栋(QQ群号):</label></td>
                    <td><!-- <input type="text" v-model="HnslUserInfo.buildingName" readonly="readonly"  />-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.buildingName}}
                    </td>
                </tr>
            </table>

            <div v-show="schoolBelongShow">
                <!-- 所属团队树 -->
                <table class="textTable">
                    <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;</span>
                    <div class="tree_pag">
                        <h5>用户对应学校</h5>
                        <div class="tree_content">
                            <!--左边树状图-->
                            <div class="left_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in teamTreeRight"
                                                @click="addCity(teamTree.schoolCode,teamTree.schoolName)">
                                                <a>{{teamTree.schoolName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!--右边树状图-->
                            <div class="right_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in teamTreeLeft"
                                                @click="delCity(index)"><a>{{teamTree.schoolName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </table>
            </div>

            <!-- 团队信息 -->
            <table class="textTable">
                <!-- 	        <span style="font-size: 18px;">&nbsp;&nbsp;&nbsp;楼栋信息</span>
-->
                <div class="panel-heading" style="font-size: 23px">团队信息</div>
                <tr>
                    <td class="leftTd"><label>所属团队:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.grouping"/>
                        <!--						<a  v-show="gBianji1" class="btn btn-primary" @click="group_show()" style="float: left; margin-left: 4px; ">编辑</a>-->
                        <!--							<a  v-show="gBianji2" class="btn btn-primary" @click="group_close()" style="float: left; margin-left: 4px;">确定</a>-->
                        <!--							<a  v-show="gBianji2" class="btn btn-warning" @click="group_back()" style="float: left; margin-left: 4px;">取消</a>-->
                    </td>
                    <td></td>
                    <td></td>
                </tr>
            </table>

            <div v-show="groupingShow">
                <!-- 所属团队树 -->
                <table class="textTable">
                    <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;</span>
                    <div class="tree_pag">
                        <h5>用户对应分组</h5>
                        <div class="tree_content">
                            <!--左边树状图-->
                            <div class="left_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in group"
                                                @click="addGroup(teamTree.groupingCode,teamTree.groupingName)">
                                                <a>{{teamTree.groupingName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!--右边树状图-->
                            <div class="right_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in groupTreeLeft"
                                                @click="delGroup(index)"><a>{{teamTree.groupingName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </table>
            </div>

            <!-- 积分绩效-->
            <table class="textTable">
                <div class="panel-heading" style="font-size: 23px">积分绩效</div>
                <tr>
                    <td class="leftTd"><label>总积分:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.integral" readonly="readonly"/>
                        <!--&nbsp;&nbsp;{{HnslUserInfo.integral}}--></td>
                    <td class="leftTd"><label></label></td>
                    <td></td>
                </tr>
                <tr>
                    <td class="leftTd"><label>积分变动详情:</label></td>
                    <td style="width: 400px;"><select>
                        <option v-for="itme in HnslUserInfo.integralList">
                            {{itme.createdDate}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{itme.integralRemark}}&nbsp;&nbsp;&nbsp;+{{itme.integralOperation}}
                        </option>
                    </select></td>
                </tr>
                <tr>
                    <td class="leftTd"><label>上月绩效：</label></td>
                    <td style="width: 400px;">
                        &nbsp;&nbsp;{{HnslUserInfo.lastPerformance}}
                    </td>
                    <td class="leftTd"><label>本月绩效：</label></td>
                    <td>&nbsp;&nbsp;{{HnslUserInfo.performance}}</td>
                </tr>

            </table>


            <div class="form-group">
                <div class="col-sm-2 control-label"></div>
                <input type="button" v-show="updateButton"
                       style="margin-left: 95%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
                       class="btn btn-warning" @click="saveOrUpdate" value="修改"/>
                <input type="button" v-show="sureShow"
                       style="margin-left: 95%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
                       class="btn btn-warning" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button"
                                   style="margin-left: 95%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
                                   class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
    <!-- 管理员操作新增修改的详细信息 -->
    <div v-show="!adminShowList" class="panel panel-default">
        <div class="panel-heading" style="font-size: 23px">{{title}}</div>
        <form class="form-horizontal">
            <!-- 用户信息 -->
            <table class="textTable">
                <!--  <span style="font-size: 18px;">&nbsp;&nbsp;&nbsp;账户信息</span>  -->
                <a style="margin-left: 10px" @click="noDesensitization(HnslUserInfo.id)" class="btn btn-primary"
                   v-show="updateButton">免脱敏</a>
                <tr>
                    <td class="leftTd info-text"><label>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.userName"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userName}}--></td>
                    <td class="leftTd info-text" style="width:10%;text-align:center;"><label label>性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别:</label>
                    </td>
                    <td style="width:50%">
                        <select class="form-control"
                                style="height: 32px;" v-model="gender">
                            <option value='0'>未知</option>
                            <option value='1'>男</option>
                            <option value='2'>女</option>
                        </select>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userGender}}--></td>
                </tr>

                <tr>
                    <td class="leftTd info-text"><label>地&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;址:</label></td>
                    <td style="width: 31%;"><input type="text" v-model="HnslUserInfo.userSite"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userSite}}--></td>
                    <td class="leftTd info-text"><label>身&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;份:</label></td>
                    <td>
                        <select class="form-control" style="height: 32px;" v-model="sf">
                            <option value='0'>无</option>
                            <option value='1'>校园经理</option>
                            <option value='2'>一级合伙人</option>
                            <option value='3'>二级合伙人</option>
                            <option value='4'>三级合伙人</option>
                            <option value='6'>地市管理员</option>
                            <option value='5'>省级管理员</option>
                        </select>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.statusSf}}--></td>
                </tr>

                <tr>
                    <td class="leftTd info-text"><label>身份证号:</label></td>
                    <td style="width: 31%;"><input type="text" v-model="HnslUserInfo.userSfz"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userSfz}}--></td>
                    <td class="leftTd info-text"><label>手机号码:</label></td>
                    <td><input type="text" v-model="HnslUserInfo.userPhone"
                               onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9]/g,'');}).call(this)"
                               onblur="this.v();"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.userPhone}}--></td>
                </tr>

                <tr>
                    <td class="leftTd info-text"><label>银行卡号:</label></td>
                    <td style="width: 31%;">
                        <!--<input type="text" v-model="HnslUserInfo.bankNumber" readonly="readonly"  />-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.bankNumber}}
                    </td>
                    <td class="leftTd info-text"><label>销售编码:</label></td>
                    <td><input type="text" v-model="HnslUserInfo.salesCode" @blur="queryNumbers"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.salesCode}}--></td>
                </tr>

                <tr>
                    <td class="leftTd info-text"><label>揽机工号:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.numbers"/>
                    </td>
                    <td class="leftTd info-text"><label>QQ号码:</label></td>
                    <td style="width: 31%;"><input type="text" v-model="HnslUserInfo.flock"/>
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.numbers}}--></td>
                </tr>

                <tr>
                    <td class="leftTd info-text"><label>地市:</label></td>
                    <td>
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.cityCode">
                            <option value='0'>无</option>
                            <option value='731'>长沙</option>
                            <option value='732'>湘潭</option>
                            <option value='733'>株洲</option>
                            <option value='734'>衡阳</option>
                            <option value='735'>郴州</option>
                            <option value='736'>常德</option>
                            <option value='737'>益阳</option>
                            <option value='738'>娄底</option>
                            <option value='739'>邵阳</option>
                            <option value='730'>岳阳</option>
                            <option value='743'>湘西</option>
                            <option value='744'>张家界</option>
                            <option value='745'>怀化</option>
                            <option value='746'>永州</option>
                            <option value='700'>全省</option>
                        </select>
                    </td>
                    <td class="leftTd info-text"><label>上级手机号码:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.statusSuperior"/>
                    </td>
                </tr>
                <tr>
                    <td class="leftTd info-text"><label>省级限制:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.positionLimitation">
                            <option value='0'>关闭</option>
                            <option value='1'>开启</option>
                        </select>
                    </td>
                    <td class="leftTd info-text"><label>登录活体:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control" style="height: 32px;" v-model="HnslUserInfo.livingLimitation">
                            <option value='0'>关闭</option>
                            <option value='1'>开启</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="leftTd info-text"><label>下单拦截:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.cardLimitationSwitch">
                            <option value='0'>关闭</option>
                            <option value='1'>腾讯活体识别</option>
                            <option value='2'>集团人脸评分</option>
                            <option value='3'>自动活体截图</option>
                            <option value='4'>本地视频录制</option>
                        </select>
                    </td>
                    <td class="leftTd info-text"><label>翼站展示:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.courierStationSwitch">
                            <option value='0'>关闭</option>
                            <option value='1'>开启</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="leftTd info-text"><label>BPS激活开关:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.activateBpsSwitch">
                            <option value='0'>关闭</option>
                            <option value='1'>开启</option>
                        </select>
                    </td>
                    <td class="leftTd info-text"><label>最新登录时间:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.loginDate"/>
                    </td>
                </tr>
                <tr>
                    <td class="leftTd info-text"><label>派单身份:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.sendOrdersType">
                            <option value='1'>分派员</option>
                            <option value='2'>接收员</option>
                        </select>
                    </td>
                    <td class="leftTd info-text"><label>来源渠道:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.channelType">
                            <option value='1'>中小学</option>
                            <option value='2'>高校（校园经理/管理员/学子支撑）</option>
                            <option value='3'>学子公司（学子合伙人）</option>
                            <option value='4'>校园店（门店代理商）</option>
                            <option value='5'>泛渠道（合作直销）</option>
                        </select>
                    </td>
                </tr>

                <tr v-if="hasPermission('hnsdsysuser:admin')">
                    <td class="leftTd info-text"><label>扫楼渠道:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.hnslChannel">
                            <option value='1'>全渠道</option>
                            <option value='2'>校园渠道</option>
                            <option value='3'>电渠互联网卡渠道</option>
                            <option value='4'>其他</option>
                        </select>
                    </td>
                </tr>
                <tr v-if="showPassCheck">
                    <td class="leftTd info-text"><label>跳过资格校验:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.passCheck">
                            <option value='2'>否</option>
                            <option value='1'>是</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="leftTd info-text"><label>临时工号:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="HnslUserInfo.temporaryJob">
                            <option value='2'>否</option>
                            <option value='1'>是</option>
                        </select>
                    </td>
                </tr>
                <tr v-if="hasPermission('hnsdsysuser:dkxd')">
                    <td class="leftTd info-text"><label>宽带业务:</label></td>
                    <td style="width: 31%;">
                        <select class="form-control" style="height: 32px;" v-model="HnslUserInfo.dkxdSwitch">
                            <option value='1'>开启</option>
                            <option value='2'>关闭</option>
                        </select>
                    </td>
                </tr>
            </table>


            <!-- 楼栋信息 -->
            <table class="textTable">
                <!-- 	        <span style="font-size: 18px;">&nbsp;&nbsp;&nbsp;楼栋信息</span>
-->
                <div class="panel-heading" style="font-size: 23px">楼栋信息</div>
                <tr>
                    <td class="leftTd"><label>所属学校:</label></td>
                    <td style="width: 31%;">
                        <!--<select class="form-control"
                                style="height: 32px;" v-model="code">
                            <option value=''>全部</option>
                            <option v-for="itme in school" v-bind:value="itme.schoolCode">
                                {{itme.schoolName}}</option>
                        </select>-->
                        <!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.schoolName}}-->
                        <input type="text" v-model="HnslUserInfo.schoolName" disabled/>
                        <a v-show="bianji1" class="btn btn-primary" @click="school_show()"
                           style="float: left; margin-left: 4px; ">编辑</a>
                        <a v-show="bianji2" class="btn btn-primary" @click="school_close()"
                           style="float: left; margin-left: 4px;">确定</a>
                        <a v-show="bianji2" class="btn btn-warning" @click="school_back()"
                           style="float: left; margin-left: 4px;">取消</a>
                    </td>
                    <td class="leftTd"><label>管理楼栋(QQ群号):</label></td>
                    <td><!-- <input type="text" v-model="HnslUserInfo.buildingName" readonly="readonly"  />-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{HnslUserInfo.buildingName}}
                    </td>
                </tr>
            </table>

            <div v-show="schoolBelongShow">
                <!-- 所属团队树 -->
                <table class="textTable">
                    <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;</span>
                    <div class="tree_pag">
                        <div>
                            <input type="text" v-model="searchQuery" placeholder="输入学校名称进行筛选"/>
                        </div>
                        <h5>用户对应学校</h5>
                        <div class="tree_content">
                            <!--左边树状图-->
                            <div class="left_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in filteredTeamTreeRight"
                                                @click="addCity(teamTree.schoolCode,teamTree.schoolName)">
                                                <a>{{teamTree.schoolName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!--右边树状图-->
                            <div class="right_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in teamTreeLeft"
                                                @click="delCity(index)"><a>{{teamTree.schoolName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </table>
            </div>

            <!-- 分组信息 -->
            <table class="textTable">
                <!-- 	        <span style="font-size: 18px;">&nbsp;&nbsp;&nbsp;楼栋信息</span>
-->
                <div class="panel-heading" style="font-size: 23px">分组信息</div>
                <tr>
                    <td class="leftTd"><label>所属分组:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.grouping"/>
                        <!--							<a  v-show="gBianji1" class="btn btn-primary" @click="group_show()" style="float: left; margin-left: 4px; ">编辑</a>-->
                        <!--							<a  v-show="gBianji2" class="btn btn-primary" @click="group_close()" style="float: left; margin-left: 4px;">确定</a>-->
                        <!--							<a  v-show="gBianji2" class="btn btn-warning" @click="group_back()" style="float: left; margin-left: 4px;">取消</a>-->
                    </td>
                    <td></td>
                    <td></td>
                </tr>
            </table>

            <div v-show="groupingShow">
                <!-- 所属团队树 -->
                <table class="textTable">
                    <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;</span>
                    <div class="tree_pag">
                        <h5>用户对应分组</h5>
                        <div class="tree_content">
                            <!--左边树状图-->
                            <div class="left_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in group"
                                                @click="addGroup(teamTree.groupingCode,teamTree.groupingName)">
                                                <a>{{teamTree.groupingName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!--右边树状图-->
                            <div class="right_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in groupTreeLeft"
                                                @click="delGroup(index)"><a>{{teamTree.groupingName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </table>
            </div>

            <!-- 积分绩效-->
            <table class="textTable">
                <div class="panel-heading" style="font-size: 23px">积分绩效</div>
                <tr>
                    <td class="leftTd"><label>总积分:</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="HnslUserInfo.integral" readonly="readonly"/>
                        <!--&nbsp;&nbsp;{{HnslUserInfo.integral}}--></td>
                    <td class="leftTd"><label></label></td>
                    <td></td>
                </tr>
                <tr>
                    <td class="leftTd"><label>积分变动详情:</label></td>
                    <td style="width: 400px;"><select>
                        <option v-for="itme in HnslUserInfo.integralList">
                            {{itme.createdDate}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{itme.integralRemark}}&nbsp;&nbsp;&nbsp;+{{itme.integralOperation}}
                        </option>
                    </select></td>
                </tr>
                <tr>
                    <td class="leftTd"><label>上月绩效：</label></td>
                    <td style="width: 400px;">
                        &nbsp;&nbsp;{{HnslUserInfo.lastPerformance}}
                    </td>
                    <td class="leftTd"><label>本月绩效：</label></td>
                    <td>&nbsp;&nbsp;{{HnslUserInfo.performance}}</td>
                </tr>

            </table>


            <div class="form-group">
                <div class="col-sm-2 control-label"></div>
                <input type="button" v-show="updateButton"
                       style="margin-left: 95%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
                       class="btn btn-warning" @click="updateQianZhi" value="修改"/>
                <input type="button" v-show="sureShow"
                       style="margin-left: 95%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
                       class="btn btn-warning" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button"
                                   style="margin-left: 5%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
                                   class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>

    <div class="modal" id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" style="padding: 70px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true">×
                    </button>
                    <h4 class="modal-title" id="myModalLabel1" style="text-align: center">数据下载审批人选择</h4>
                    <div style="display: flex">
                        <input placeholder="请输入审批人姓名或手机号" v-model="searchText"
                               @input="onInput"/>
                    </div>

                    <table class="table table-bordered" style="width: 100%; text-align: center;margin-top: 10px">
                        <tbody id="myTable">
                        <tr>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                    <div style="width: 100%;text-align: center;">
                        <a href="#" id="prevPage" @click="prevPage">上一页</a>
                        <span id="currentPage">1</span>
                        <a href="#" id="nextPage" @click="nextPage">下一页</a>
                    </div>

                    <div style="text-align: right;margin-top: 20px;">
                        <div class="col-sm-2 control-label"></div>
                        <input type="button" class="btn btn-warning" @click="submitApprover" value="确定"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" id="myModal2" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" style="padding: 70px">
            <div class="modal-content">
                <div class="modal-header">
                    <div style="display: flex">
                        合伙人姓名：<input v-model="hnslUserInfoId.userName" disabled/>
                    </div>
                    <div style="display: flex;margin-top: 10px">
                        合伙人手机号：<input v-model="hnslUserInfoId.userPhone" disabled/>
                    </div>
                    <div style="display: flex;margin-top: 10px">
                        身份证：<input v-model="hnslUserInfoId.userSfz" disabled/>
                    </div>
                    <div style="display: flex;margin-top: 10px">
                        上级手机号：<input v-model="hnslUserInfoId.statusSuperior" disabled/>
                    </div>
                    <div style="text-align: center;margin-top: 20px;">
                        <div class="col-sm-2 control-label"></div>
                        <input type="button" class="btn btn-warning" @click="closeDe" value="确定"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<script src="../../js/modules/hnsl/hnsluser.js"></script>
<script src="../components.js"></script>
</body>
</html>