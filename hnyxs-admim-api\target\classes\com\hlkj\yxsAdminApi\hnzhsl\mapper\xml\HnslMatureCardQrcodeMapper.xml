<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslMatureCardQrcodeMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_mature_card_qrcode a
        <where>
            <if test="param.id != null and param.id != ''">
                a.id = #{param.id}
            </if>
            <if test="param.schoolName != null and param.schoolName != ''">
                AND a.school_name LIKE CONCAT('%', #{param.schoolName}, '%')
            </if>
            <if test="param.schoolCode != null and param.schoolCode != ''">
                AND a.school_code = #{param.schoolCode}
            </if>
            <if test="param.cityCode != null and param.cityCode != ''">
                AND a.city_code = #{param.cityCode}
            </if>
            <if test="param.offlineQrcode != null and param.offlineQrcode != ''">
                AND a.offline_qrcode = #{param.offlineQrcode}
            </if>
            <if test="param.offlineQrcodeOut != null and param.offlineQrcodeOut != ''">
                AND a.offline_qrcode_out = #{param.offlineQrcodeOut}
            </if>
            <if test="param.offlineCrossCityUse != null and param.offlineCrossCityUse != ''">
                AND a.offline_cross_city_use = #{param.offlineCrossCityUse}
            </if>
            <if test="param.onlineQrcode != null and param.onlineQrcode != ''">
                AND a.online_qrcode = #{param.onlineQrcode}
            </if>
            <if test="param.onlineQrcodeOut != null and param.onlineQrcodeOut != ''">
                AND a.online_qrcode_out = #{param.onlineQrcodeOut}
            </if>
            <if test="param.onlineCrossCityUse != null and param.onlineCrossCityUse != ''">
                AND a.online_cross_city_use = #{param.onlineCrossCityUse}
            </if>
            <if test="param.onlineActiveQrcode != null and param.onlineActiveQrcode != ''">
                AND a.online_active_qrcode = #{param.onlineActiveQrcode}
            </if>
            <if test="param.onlineActiveQrcodeOut != null and param.onlineActiveQrcodeOut != ''">
                AND a.online_active_qrcode_out = #{param.onlineActiveQrcodeOut}
            </if>
            <if test="param.onlineActiveCrossCityUse != null and param.onlineActiveCrossCityUse != ''">
                AND a.online_active_cross_city_use = #{param.onlineActiveCrossCityUse}
            </if>
            <if test="param.selfActiveQrcode != null and param.selfActiveQrcode != ''">
                AND a.self_active_qrcode = #{param.selfActiveQrcode}
            </if>
            <if test="param.selfActiveQrcodeOut != null and param.selfActiveQrcodeOut != ''">
                AND a.self_active_qrcode_out = #{param.selfActiveQrcodeOut}
            </if>
            <if test="param.selfActiveCrossCityUse != null and param.selfActiveCrossCityUse != ''">
                AND a.self_active_cross_city_use = #{param.selfActiveCrossCityUse}
            </if>
             <if test="param.offlineFaceReservation != null and param.offlineFaceReservation != ''">
                AND a.offline_face_reservation = #{param.offlineFaceReservation}
            </if>
            <if test="param.createUser != null and param.createUser != ''">
                AND a.create_user = #{param.createUser}
            </if>
            <if test="param.createTime != null and param.createTime != ''">
                AND a.create_time = #{param.createTime}
            </if>
            <if test="param.updateUser != null and param.updateUser != ''">
                AND a.update_user = #{param.updateUser}
            </if>
            <if test="param.updateTime != null and param.updateTime != ''">
                AND a.update_time = #{param.updateTime}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslMatureCardQrcode">
        <include refid="selectSql"></include>
    </select>


    <select id="queryMatureCardList" parameterType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslMatureCardQrcode">
        SELECT t.* FROM `hnsl_mature_card_qrcode` t
        left join hnsl_school t1 on t.school_code = t1.SCHOOL_CODE
        <where>
            <if test="schoolName != null and schoolName !='' ">
                t.school_name = #{schoolName}
            </if>
            <if test="schoolCode != null and schoolCode !='' ">
                AND t.school_code = #{schoolCode}
            </if>
            <if test="cityCode != null and cityCode != '' ">
                AND t.city_code = #{cityCode}
            </if>
        </where>
    </select>

</mapper>
