<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta
	content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
	name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet"
	href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet"
	href="../../plugins/ztree/css/metroStyle/metroStyle.css">
	<link rel="stylesheet" href="../../css/bootstrap-datetimepicker.css">
	<link rel="stylesheet" href="../../css/main.css">
<link rel="stylesheet" href="../../css/building.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css"
	rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script
	src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.min.js"></script>
<script src="../../js/common.js"></script>
<style type="text/css">
.templateShow-Info {
	float: left;
	width: 100%;
	font-size: 20px;
	padding: 10px 25px 0;
}

.templateShow-Info p {
	font-size: 20px;
	float: left;
	text-align: center;
	margin: 10px 0 10px;
	margin: 10px 0 10px;
}

.templateShow-Info p:nth-child(2) {
	color: #999 !important;
	font-size: 16px;
	line-height: 28px;
}
.jifen-bt p {
	font-size: 20px;
	float: left;
	width: 19%;
	text-align: center;
	margin: 10px 0 10px;
}
.jifen-Info p {
	width: 25%;
	float: left;
	text-align: center;
}
.ui-th-div:nth-child(n+2){
text-align: center;
}
</style>
</head>
<body>
	<div id="rrapp" v-cloak>
		<div v-show="showList" id="isShowList">
				<div class="grid-btn">
					<a v-if="hasPermission('hnslbuilding:save')"
						class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增楼栋</a>
						<a class="btn btn-primary" @click="templateShowI(1)">&nbsp;批量导入</a>
						<a class="btn btn-primary" @click="cityReport">&nbsp;地市报表</a>
						<a class="btn btn-primary" @click="templateShowI(2)">&nbsp;学生导入</a>
						<a class="btn btn-primary" @click="templateShowBuilding()">&nbsp;楼栋信息报表</a>
						<a class="btn btn-primary" @click="templateShowGather()">&nbsp;采集信息报表</a>
				</div>
			<div class="row">
				
				<div class="form-group col-md-2">
					<select v-if='statusSf==6' id="prov" name="prov" class="form-control"
						style="height: 32px; margin-left: -15px;width: 122%;" v-model="cityCode"
						@change="onChangeCity()" disabled="disabled">
						<option value="">全部</option>
						<option v-for="item in city" :value="item.cityCode"
							:key="item.cityCode">{{item.cityName}}</option>
					</select>
					<select v-else='statusSf==5' id="prov" name="prov" class="form-control"
						style="height: 32px; margin-left: -15px;width: 122%;" v-model="cityCode"
						@change="onChangeCity()">
						<option value="">全部</option>
						<option v-for="item in city" :value="item.cityCode"
							:key="item.cityCode">{{item.cityName}}</option>
					</select>
				</div>
				
				<div class="form-group col-md-2">
					<select id="prov" name="prov" class="form-control"
						style="height: 32px; margin-left: -1px;width: 125%;" v-model="select1"
						@change="onChange()">
						<option v-if="listProv.length==0" value="">暂无学校</option>
						<option v-else="listProv.length==0" v-for="item in listProv" :value="item.schoolCode"
							:key="item.schoolCode">{{item.schoolName}}</option>
					</select>
				</div>
				
				<div>
					<span style="font-size: 13px; height: 32px;margin-left:3%">登记人数 &nbsp;:
						&nbsp;{{schoolRegisterExisting}}</span>
				</div>
				<div>
					<span style="font-size: 13px; height: 32px;margin-left:3%">本网用户 &nbsp;:
						&nbsp;{{schoolNetworkExisting}}</span>
				</div>
			</div>

			<table id="jqGrid"></table>
			<div id="jqGridPager"></div>
		</div>

		<!--模板下载 -->
		<div v-show="!templateShow" id="templateShow"
			class="panel panel-default">
			<div class="panel-heading">{{title}}</div>
			<div class="form-horizontal" style="padding-top: 0px; width: 100%;">
				<form id="uploadImg" enctype="multipart/form-data">
					<div class="templateShow-Info">
						<p>下载模板：</p>
						<p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
					</div>
					<div style="margin-left: 125px;">
						<a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
					</div>
					<div class="templateShow-Info">
						<p>上传文件：</p>
						<p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
					</div>
					<div style="margin-left: 125px;">
						<a v-if="hasPermission('hnsduser:importUser')"
						class="btn btn-primary" @click="importBuilding">&nbsp;开始导入</a> <input
						style="display: none;" name="uploadFile" id="uploadFile"
						type="file" @change="uploadFile" />
							
					</div>
					<div style="width: 100%; text-align: center;">
						<input type="button" class="btn btn-warning" @click="htmlReolad"
							value="返回" />
					</div>
				</form>
			</div>
		</div>
		
		<!-- 添加楼栋 -->
		<div v-show="!showAddBuilding">
		<div class="layui-layer-title" style="cursor: move;">添加楼栋</div>
		<div id="" class="layui-layer-content">
			<div class="x-body">
				<form class="layui-form" enctype="multipart/form-data">
					<div class="layui-form-item max-wid">
						<label class="layui-form-label">学&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;校：&nbsp;&nbsp;</label>
						<div class="layui-form-mid layui-word-aux">{{selectName}}</div>
					</div>
					<div class="layui-form-item ax-wid">
						<label class="layui-form-label">楼栋名称：&nbsp;&nbsp;</label>
						<div class="layui-input-inline">
							<input type="text" v-model="hnslBuildingAdd.buildingName"  placeholder="请输入楼栋名称" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item ax-wid">
						<label class="layui-form-label">楼栋层数：&nbsp;&nbsp;</label>
						<div class="layui-input-inline">
							<input type="text"  v-model="hnslBuildingAdd.buildingTier" placeholder="请输入楼栋层数" class="layui-input" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
						</div>
					</div>
					<div class="layui-form-item ax-wid">
						<label class="layui-form-label">每层房间：&nbsp;&nbsp;</label>
						<div class="layui-input-inline">
							<input type="text" v-model="hnslBuildingAdd.buildingRoom"  placeholder="请输入每层房间数" class="layui-input" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" >
						</div>
					</div>
					<div class="layui-form-item ax-wid">
						<label class="layui-form-label">房间人数：&nbsp;&nbsp;</label>
						<div class="layui-input-inline">
							<input type="text"  v-model="hnslBuildingAdd.buildingNumber"  placeholder="请输入房间人数" class="layui-input" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" >
						</div>
					</div>
					<div class="layui-form-item ax-wid">
						<label class="layui-form-label">楼&nbsp;&nbsp;&nbsp;栋&nbsp;长：&nbsp;&nbsp;</label>
						<div class="layui-input-inline">
							<input type="text" v-model="hnslBuildingAdd.userId"  placeholder="楼栋长手机号码" class="layui-input">
						</div>
					</div>

					<div class="layui-form-item max_kuan" style="margin-top: 7%">
						<label  class="layui-form-label"></label>
						<a class="layui-btn" @click="submitForm()" style="margin-left: 50px;" >确定</a>
						<a class="layui-btn hui_bg" @click="cancelForm()">取消</a>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	    <!-- 房间列表 -->
		<div v-show="!showRoom">
			<div class="row">
				<span style="font-size: 19px; height: 32px"> 楼栋名字:
					&nbsp;&nbsp;{{hnslBuilding.buildingName}}</span>
				<div>
					<span style="font-size: 15px; margin-left =5%; height: 32px">
						入住人数 &nbsp;&nbsp;&nbsp;: &nbsp;{{hnslBuilding.buildingRegisterExisting}}</span>
				</div>
				<span style="font-size: 15px; height: 32px">本网用户 &nbsp;&nbsp;&nbsp;:
					&nbsp;{{hnslBuilding.networkPhoneExisting}}</span>
			</div>
			
			<div class="panel-heading"
					style="clear: both; background-color: #ddd; width: 100%;margin-top: 1%;">房间信息</div>
			<div class="jifen-bt">
					<p>房间号</p>
					<p>登记人数</p>
					<p>本网手机用户</p>
					<p>本网宽带用户</p>
					<p>操作</p>
			</div>
			<div class="jifen-Info" v-for="itme in roomList"
					v-bind:value="itme.id">
					<p>123</p>
					<p>123</p>
					<p>1421</p>
					<p>123</p>
				</div>
			
			<div class="form-group col-md-2">
				<input type="button" class="btn btn-warning"
					style="margin-left: 296%; margin-top: 3%; padding: 4% 17%;"
					@click="htmlReolad" value="返 回">
			</div>
	</div>
	<!--修改入学年级弹窗-->
		<div class="modal"  id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog" style="width: 400px;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
								aria-hidden="true">×
						</button>
						<h4 class="modal-title" id="myModalLabel">
							修改楼栋入学年级
						</h4>
					</div>
					<div class="modal-body" style="display: flex;align-items: center;">
						<label style="margin-right: 10px;margin-bottom: 0;">选择年份:</label>
						<div class="input-group col-ms-2 ">
							<!--<input type="text" class="form-control form-filter yearpicker" id="dateTimeRange"-->
								   <!--placeholder="日期">-->
							<select id="sel_role" name="role" class="form-control" title="请选择" data-width="150px" v-model="gradeQuery">
								<option value="">请选择年份</option>
								<option v-for="item in gradeArray" :value="item"
										:key="item">{{item}}</option>
							</select>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default"
								data-dismiss="modal">关闭
						</button>
						<button type="button" class="btn btn-primary" @click="updateResult">
							修改
						</button>
					</div>
				</div><!-- /.modal-content -->
			</div><!-- /.modal-dialog -->
		</div><!-- /.modal -->

	<script src="../../js/modules/hnsl/hnslbuilding.js"></script>
	<script src="../../js/components.js"></script>

</body>
</html>