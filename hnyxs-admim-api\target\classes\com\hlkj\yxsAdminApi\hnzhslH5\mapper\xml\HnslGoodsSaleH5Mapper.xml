<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslGoodsSaleH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_goods_sale a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsSaleId != null">
                AND a.GOODS_SALE_ID LIKE CONCAT('%', #{param.goodsSaleId}, '%')
            </if>
            <if test="param.goodsSaleName != null">
                AND a.GOODS_SALE_NAME LIKE CONCAT('%', #{param.goodsSaleName}, '%')
            </if>
            <if test="param.campusBroadbandSwitch != null">
                AND a.CAMPUS_BROADBAND_SWITCH = #{param.campusBroadbandSwitch}
            </if>
            <if test="param.broadbandRate != null">
                AND a.BROADBAND_RATE LIKE CONCAT('%', #{param.broadbandRate}, '%')
            </if>
            <if test="param.campusTrafficSwitch != null">
                AND a.CAMPUS_TRAFFIC_SWITCH = #{param.campusTrafficSwitch}
            </if>
            <if test="param.goodsSaleIntegral != null">
                AND a.GOODS_SALE_INTEGRAL LIKE CONCAT('%', #{param.goodsSaleIntegral}, '%')
            </if>
            <if test="param.integralConfiguration != null">
                AND a.INTEGRAL_CONFIGURATION LIKE CONCAT('%', #{param.integralConfiguration}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.goodsSaleType != null">
                AND a.GOODS_SALE_TYPE LIKE CONCAT('%', #{param.goodsSaleType}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsSale">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsSale">
        <include refid="selectSql"></include>
    </select>

</mapper>
