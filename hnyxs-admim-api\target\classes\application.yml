# 端口
server:
  port: 8081
logging:
  config: classpath:logback-spring.xml
# 多环境配置
spring:
  profiles:
    active: dev

  # 禁用热部署
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false

  # JMX性能优化
  jmx:
    enabled: false

  # 优化启动性能
  main:
    lazy-initialization: false
    banner-mode: off

  # 连接池配置
  datasource:
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      remove-abandoned: true
      remove-abandoned-timeout: 1800
      #pool-prepared-statements: false
      #max-pool-prepared-statement-per-connection-size: 20
      filters: wall
      validation-query: SELECT 1
      aop-patterns: com.hlkj.yxsAdminApi.*.*.service.*
      # 开发环境禁用Druid监控以减少CPU消耗
      stat-view-servlet:
        enabled: false
      web-stat-filter:
        enabled: false

  # json时间格式设置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss

  # 设置上传文件大小
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

  # 邮件服务器配置
  mail:
    host: smtp.qq.com
    username:
    password:
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465

# Mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath*:com/hlkj/yxsAdminApi/**/*Mapper.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
  global-config:
    :banner: false
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0

# 框架配置
config:
  open-office-home: D:/OpenOffice4/
  swagger-base-package: com.hlkj.yxsAdminApi
  swagger-title: EleAdmin API文档
  swagger-version: 1.0
  swaggerHost: localhost:8081
  token-key: ULgNsWJ8rPjRtnjzX/Gv2RGS80Ksnm/ZaLpvIL+NrBg=
  #用户sessiostoke保存的标识
  sessiongToke: hnysx-admin-Toke
  #区域限制次数
  duanxin_xiangzhi_nums: 500
  #一天限制次数
  duanxin_xiangzhi_data_nums: 20
  #限制发送key
  one_minute_limit: ${personal.sessiongToke}_ONE_MINUTE_LIMIT
  #限制发送时间(秒)
  one_minute_limit_time: 60
  #ip限制发送key
  ipArress_one_day_limit: ${personal.sessiongToke}_IPARRESS_ONE_DAY_LIMIT
  #手机号码限制发送key
  moblie_one_day_limit: ${personal.sessiongToke}_MOBLIE_ONE_DAY_LIMIT
  #最终的短信验证码编码
  zuizhong_duanxin_nums: ${personal.sessiongToke}_ZUIZHONG_DUANXIN_NUMS
  #是否发送接着上面的短信信息（true:是，false：否）
  isduanxin: false
  #验证码的长度
  duanxin_length: 6
  #验证码保存时长(秒)
  duanxin_time: 300
  #短信验证码验证错误
  duanxin_error_nums: ${personal.sessiongToke}_DUANXIN_ERROR_NUMS
  #短信验证码输入错误次数
  duanxin_error_xianzhi_nums: 5
  #是否测试
  acctiond: test
  #图片访问地址
  hnzsxImgPath: https://lst.hn.189.cn/hnzsxserve/download/
  #省集约上传商品图片桶名称
  hnzsx_admin_goods: hnzsxadmingoods

aws:
  s3:
    # 正式环境
    accessKey: M5LGR55DFMU035DJO2JF
    secretKey: yx6pQO8Kgn8upOaIGKDzYs4MmxZes9rbyPDOGuk8
    url: http://**************:8080
    # 测试环境
#    accessKey: N4U20538LY53TVKRFWU1
#    secretKey: XyM10Hhy1tEAgoOKrqp00ArwAJwhUHooY7TQGBa9
#    url: http://***************:8080

slauth:
  #解密私钥,业务系统需要替换此值
  privateKey: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIqjucGnm36o/qEHTsbshroGmp8sbuGZWFbzfzBvMaJLR656HhvVD4vAlHjbdcGLBNclEHwT3zvh0h1N7hYr1CDGyEvWfgODZphk01hMkp+CxqvpbjYVhHaRSa583yU4HoEC5IODn5sQ84OmMaQIzBzStYCsb4TEh84HH7NFieELAgMBAAECgYABOY8TkeqCxPVy8MCWa1KJZDOLgnX8UkGAiygtgl365BVfCj8n2csIXgwxwSaaLiM9z1sE7ZwESGt1zNF0qrB2EldLe7//aBn5Uafe3CZw/97ozY4uhcT2/AGxuzjz+IPgQBub52HCaQC1LEMcXAN4hP6VCDZFxadlUW/iUViM0QJBAL5EhNoXnS0YBhhNCBaG4LR/cKDDY2TE4YYpJl2P8/ZlGl+Oq9wledrD3816Pg6/q8PjTcak5Ad4wjoqfCOoqCMCQQC6iSkfHdG71Mvw6uTC9LATvBnq8KWdFCPeK7N9LtSkY7qm70KoEW8iPNOcda/CafChkTE4VloHQ6xfN6Ufuj35AkASK9MQTR8KtnemjHzQQfNl6aHlCKQOdKshN/7dAqmHB6LcK3EIZl/b38cZBaq1l+Kco7HC3e6VNBhAm9wdP7dHAkAflzkSnv81G03KqE1DwyLKxNFy23yAxLdqWeZU7ghvKsCiouScCA/VMY348mlouH5bLnayftVSY6Ceo5Lgc68pAkB0f7zPqo2w/ov4zZW3X5dnS45tpPqybQvepLmlUmmghCbhf3dfaXxM9hAxDCZCqeRNdJ44B0dSx3g9lL6zVdyy
  #获取令牌接口，业务系统需要替换此值
  methodGetAccessToken: http://**************:30048/api/openapi/hunanOauth/oauth/token
  appid: 2bd1aded58f6b38ed943ecfa1c09e60b
  appkey: 74a7732dbff67f84a9cfb9b60a502c8f
  #获取code接口,业务系统需要替换此值
  methodGetCode: http://**************:32207/index.html?client_id=TYRZ_ZHSL&client_secret=TYRZ_ZHSL&redirect_uri=http://**************:30002/hnyxsadminapp/zhsl/auth
  clientId: TYRZ_ZHSL
  #应用密钥,业务系统需要替换此值
  clientSecret: TYRZ_ZHSL
  #系统回调地址,业务系统需要替换此值
  redirectUri: http://**************:30002/hnyxsadminapp/zhsl/auth
  #类型 固定值
  grantType: authorization_code
  #验活接口地址
  survival: http://**************:30048/api/openapi/uac/code/survival

sjyauth:
  #解密私钥,业务系统需要替换此值
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMrCIgrx8/v2/B0LH29adNH4auZSeNiSHbk2y+SgX8whiS6/oDxF3bdnxv5h6HHfrYhwdMtWfLaYvQNIa//6TD8EyuTkBT4ODrOXQVegDn/LS63ubXD7kHap9dD0oO681I8FqlLr3JueR4HghaYQXiM44j7FLID1sWlSqRv9WSlZAgMBAAECgYABxYPm9rDIi01KgTpRlbJbC/b+QWeoyaWYSC9XGPy7gs3x/B6/dINDW83u6IEWsoI6XZC6V6Vi+XuqLrvdDvm+G+PyTJuAyNmz7/BtZYRprTCyhqwMr79Df6LSo8cI/VwDqGiwYwPnRJ7jfFJEMq/qb34XFYRxE5DzcJdodIBW+QJBAPTM6GV4ECOWuIRVoAhhYUc8oo+6gl/79S2lPd0SO7kv3AU5xEs37nkB7tsGxbxpRExker0v6e4elsXE3t1zxr0CQQDUCNR7XH8Zw84hChFH1Z3166dVTSldYmxFAcp8BXoVjKYYgEvMQJqIFGxOKCKXHaxyAZGjNZ/knWzlevtFcVTNAkBhzDLTxsF71kzk4WhLsMuyL/EpxnMrcXcjwy4R0aT2eL5HgCqBeljVEwafjfcVLGDSvBmk1y13qdTGMb5vbj9pAkBdN3SSt1t5bEfW5qIqPpOvZeau7bldwMNwoxTt6Up+5tKuutiX9174rGHhCmwKm5UQmff5FL4yt7MEQEjvgp+RAkEAttpZn5b86wlKR1SIZ6KEyralN3ehADeWjA8y51EK9tRqn7dA0kCD/x1/Oit4okl2+nm0tEDl0CEiRq2i9bN4FQ==
  #获取令牌接口，业务系统需要替换此值
  methodGetAccessToken: http://**************:30048/api/openapi/hunanOauth/oauth/token
  appid: 2bd1aded58f6b38ed943ecfa1c09e60b
  appkey: 74a7732dbff67f84a9cfb9b60a502c8f
  #获取code接口,业务系统需要替换此值
  #methodGetCode: https://web.oauth.tyrzzx.eda.it.hnx.ctc.com:15099/index.html?client_id=TYRZ_JYSD&client_secret=TYRZ_JYSD&redirect_uri=http://**************:30002/hnyxsAdmin/jysd/auth
  methodGetCode: http://**************:32207/index.html?client_id=TYRZ_JYSD&client_secret=TYRZ_JYSD&redirect_uri=http://**************:30002/hnyxsadminapp/jysd/auth
  clientId: TYRZ_JYSD
  #应用密钥,业务系统需要替换此值
  clientSecret: TYRZ_JYSD
  #系统回调地址,业务系统需要替换此值
  redirectUri: http://**************:30002/hnyxsadminapp/jysd/auth
  #类型 固定值
  grantType: authorization_code
  #验活接口地址
  survival: http://**************:30048/api/openapi/uac/code/survival



 