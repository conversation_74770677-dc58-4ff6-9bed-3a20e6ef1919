{"ast": null, "code": "import EditForm from '../components/edit-form.vue';\nimport { getUser } from '@/api/system/user';\nexport default {\n  name: 'ListBasicEdit',\n  components: {\n    EditForm\n  },\n\n  data() {\n    return {\n      loading: true,\n      user: undefined\n    };\n  },\n\n  created() {\n    this.query();\n  },\n\n  methods: {\n    query() {\n      const {\n        query\n      } = this.$route;\n\n      if (query.id) {\n        getUser(Number(query.id)).then(data => {\n          this.loading = false;\n          this.user = data;\n        }).catch(e => {\n          this.$message.error(e.message);\n        });\n      }\n    }\n\n  }\n};", "map": {"version": 3, "mappings": "AAWA;AACA;AAEA;EACAA,qBADA;EAEAC;IAAAC;EAAA,CAFA;;EAGAC;IACA;MACAC,aADA;MAEAC;IAFA;EAIA,CARA;;EASAC;IACA;EACA,CAXA;;EAYAC;IACAC;MACA;QAAAA;MAAA;;MACA;QACAC,0BACAC,IADA,CACAP;UACA;UACA;QACA,CAJA,EAKAQ,KALA,CAKAC;UACA;QACA,CAPA;MAQA;IACA;;EAbA;AAZA", "names": ["name", "components", "EditForm", "data", "loading", "user", "created", "methods", "query", "getUser", "then", "catch", "e"], "sourceRoot": "src/views/hnzhslH5/goods/edit", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ele-body\">\r\n    <el-card shadow=\"never\">\r\n      <div v-loading=\"loading\">\r\n        <edit-form :data=\"user\" />\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import EditForm from '../components/edit-form.vue';\r\n  import { getUser } from '@/api/system/user';\r\n\r\n  export default {\r\n    name: 'ListBasicEdit',\r\n    components: { EditForm },\r\n    data() {\r\n      return {\r\n        loading: true,\r\n        user: undefined\r\n      };\r\n    },\r\n    created() {\r\n      this.query();\r\n    },\r\n    methods: {\r\n      query() {\r\n        const { query } = this.$route;\r\n        if (query.id) {\r\n          getUser(Number(query.id))\r\n            .then((data) => {\r\n              this.loading = false;\r\n              this.user = data;\r\n            })\r\n            .catch((e) => {\r\n              this.$message.error(e.message);\r\n            });\r\n        }\r\n      }\r\n    }\r\n  };\r\n</script>\r\n"]}, "metadata": {}, "sourceType": "module"}