<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslUserSchoolH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_h5_user_school a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS LIKE CONCAT('%', #{param.status}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserSchool">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserSchool">
        <include refid="selectSql"></include>
    </select>

    <select id="queryObjectBy" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserSchool">
        select us.*,s.SCHOOL_NAME from hnsl_h5_user_school
        us left join hnsl_h5_school s on us.SCHOOL_CODE=s.SCHOOL_CODE
        WHERE us.status=1
        <if test="schoolCode!=null and schoolCode!=''">
            AND us.SCHOOL_CODE = #{schoolCode}
        </if>
        <if test="userPhone!='' and userPhone!=null">
            AND us.USER_PHONE = #{userPhone}
        </if>
    </select>

</mapper>
