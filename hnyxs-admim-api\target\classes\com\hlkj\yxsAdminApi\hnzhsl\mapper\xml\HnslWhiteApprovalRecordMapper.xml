<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslWhiteApprovalRecordMapper">

    <sql id="selectSql">
        SELECT t.* from hnsl_white_approve_record t
        <where>
            <if test="param.batchCode != null and param.batchCode !='' ">
                t.BATCH_CODE = #{param.batchCode}
            </if>
            <if test="param.fileName != null and param.fileName !='' ">
                AND t.FILE_NAME like concat('%', #{param.fileName}, '%')
            </if>
            <if test="param.submitApprovalTime != null and param.submitApprovalTime !=''">
                AND t.SUBMIT_APPROVAL_TIME = #{param.submitApprovalTime}
            </if>
            <if test="param.queryStatus == 6">
                AND t.CITY_CODE = #{param.cityCode} AND t.PRESENTER = #{param.userName}
            </if>
        </where>
        order by t.ID desc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApprovalRecord">
        <include refid="selectSql"></include>
    </select>
	
<!--	<select id="queryListMap" resultType="java.util.HashMap">-->
<!--	SELECT tt.*-->
<!--		FROM (-->
<!--		select * from hnsl_white_approve_record-->
<!--        <choose>-->
<!--            <when test="sidx != null and sidx.trim() != ''">-->
<!--                order by ${sidx} ${order}-->
<!--            </when>-->
<!--			<otherwise>-->
<!--                order by ID desc-->
<!--			</otherwise>-->
<!--        </choose>-->
<!--		) tt-->
<!--		<choose>-->
<!--			<when test="page != null and limit != null">-->
<!--				LIMIT #{page},#{limit}-->
<!--			</when>-->
<!--			<otherwise>-->

<!--			</otherwise>-->
<!--		</choose>-->
<!--		-->
<!--	</select>-->
<!--	-->
<!-- 	<select id="queryTotal" resultType="int">-->
<!--		select count(*) from hnsl_white_approve_record t-->
<!--	</select>-->

<!--	<select id="queryRecord" resultType="com.hl.modules.hnsl.entity.HnslWhiteApprovalRecordEntity">-->
<!--		select t.* from hnsl_white_approve_record t where t.apply_id = #{id}-->
<!--	</select>-->

<!--	<insert id="save" parameterType="com.hl.modules.hnsl.entity.HnslWhiteApprovalRecordEntity">-->

<!--		insert into hnsl_white_approve_record-->
<!--		(-->
<!--			APPROVE_ID,-->
<!--			APPLY_ID,-->
<!--			BATCH_CODE,-->
<!--			FILE_NAME,-->
<!--			FILE_PATH,-->
<!--			BRANCH_POLICY_FILES,-->
<!--			BRANCH_POLICY_FILE_PATH,-->
<!--			CITY_CODE,-->
<!--			WHITE_PHONE_NUMBER,-->
<!--		 	PRESENTER,-->
<!--			APPROVE_USER,-->
<!--			ACTIVE_EVENT,-->
<!--			PRESENTER_MOBILE,-->
<!--			SUBMIT_APPROVAL_TIME,-->
<!--			REJECT_REASON,-->
<!--			REMARK,-->
<!--			CREATE_Time,-->
<!--			STATUS-->
<!--		)-->
<!--		values-->
<!--		(-->
<!--		 	#{applyId},-->
<!--		 	#{approveId},-->
<!--			#{batchCode},-->
<!--			#{fileName},-->
<!--			#{filePath},-->
<!--			#{branchPolicyFiles},-->
<!--			#{branchPolicyFilePath},-->
<!--			#{cityCode},-->
<!--			#{whitePhoneNumber},-->
<!--		 	#{presenter},-->
<!--			#{approveUser},-->
<!--			#{activeEvent},-->
<!--		 	#{presenterMobile},-->
<!--			#{submitApprovalTime},-->
<!--		 	#{rejectReason},-->
<!--			#{remark},-->
<!--			#{createTime},-->
<!--			#{status}-->
<!--		)-->
<!--	</insert>-->

<!--	<update id="update" parameterType="com.hl.modules.hnsl.entity.HnslWhiteApprovalRecordEntity">-->
<!--		update hnsl_white_approve_record-->
<!--		<set>-->
<!--			<if test="status != null">STATUS = #{status}, </if>-->
<!--			<if test="approveUser != null">APPROVE_USER = #{approveUser}, </if>-->
<!--			<if test="rejectReason != null">REJECT_REASON = #{rejectReason}, </if>-->
<!--		</set>-->
<!--		where id = #{id}-->
<!--	</update>-->
</mapper>