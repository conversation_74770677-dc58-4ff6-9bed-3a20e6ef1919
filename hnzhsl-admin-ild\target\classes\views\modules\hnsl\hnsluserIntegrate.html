<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta
	content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
	name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet"
	href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet"
	href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css"
	rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script
	src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script
	src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
<style type="text/css">
.user-Info {
	float: left;
	width: 50%;
	font-size: 20px;
}

.user-Info p {
	/* 	width: 35%; */
	float: left;
	margin: 10px 0 10px;
	padding-left: 20px;
}

.jifen-bt p {
	font-size: 20px;
	float: left;
	width: 25%;
	text-align: center;
	margin: 10px 0 10px;
}

.user-Info-p1 {
	width: 20%;
}

.user-Info p:nth-child(2) {
	color: #999 !important;
}

.jifen-Info p {
	width: 25%;
	float: left;
	text-align: center;
}

.templateShow-Info {
	float: left;
	width: 100%;
	font-size: 20px;
	padding: 10px 25px 0;
}

.templateShow-Info p {
	font-size: 20px;
	float: left;
	text-align: center;
	margin: 10px 0 10px;
	margin: 10px 0 10px;
}

.templateShow-Info p:nth-child(2) {
	color: #999 !important;
	font-size: 16px;
	line-height: 28px;
}
</style>
</head>
<body>
	<div id="rrapp" v-cloak>
		<div v-show="showList" id="isShowList">
			<div class="row">
				<div class="form-group col-md-2">
					<label>身份:</label> <select class="form-control"
						style="height: 32px;" v-model="hnslUser.statusSf">
						<option value=''>全部</option>
						<option v-for="itme in role" v-bind:value="itme.roleId">
							{{itme.roleName}}</option>
					</select>
				</div>

				<div class="form-group col-md-2" style="height: 32px;">
					<label>创建日期:</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							   id="dateTimeRange" @keyup.enter="query" value="" type="text"
							   placeholder="日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
						</span> <input name="beginTime" id="beginTime" type="hidden"> <input
							name="endTime" id="endTime" type="hidden">
					</div>
				</div>

				<div class="form-group col-md-2" style="height: 40px">
					<label>学校:</label> <input type="text" class="form-control"
						placeholder="学校" v-model="hnslUser.schoolName" />
				</div>

                <div class="form-group col-md-2" style="height: 40px">
					<label>姓名:</label> <input type="text" class="form-control"
						placeholder="姓名" v-model="hnslUser.userName" />
				</div>
				<div class="form-group col-md-2" style="height: 40px">
					<label>手机号码:</label> <input type="text" class="form-control"
						placeholder="手机号码" v-model="hnslUser.userPhone" />
				</div>

				<div class="form-group col-md-2">
					<label>所属城市:</label> <select class="form-control"
												 style="height: 32px;" v-model="hnslUser.cityCode">
					<option value=''>全部</option>
					<option v-for="itme in city" v-bind:value="itme.cityCode">
						{{itme.cityName}}</option>
				</select>
				</div>
			</div>
			<div class="grid-btn" style="margin-left: 19px;">
				<a v-if="hasPermission('hnsduser:query')" class="btn btn-primary"
					@click="query">&nbsp;查询</a> 
					<a class="btn btn-primary" @click="templateShowI">&nbsp;批量导入</a>
				<a v-if="hasPermission('hnsduser:exportUser')"
					class="btn btn-primary" @click="outUserJiFei(1)">&nbsp;批量导出</a>
				<a v-if="hasPermission('hnsluser:exportUser')"
				   class="btn btn-primary" @click="outJiFeiDeails(2)">&nbsp;详情导出</a>
			</div>

			<table id="jqGrid"></table>
			<div id="jqGridPager"></div>
		</div>

		<!--模板下载 -->
		<div v-show="!templateShow" id="templateShow"
			class="panel panel-default">
			<div class="panel-heading">{{title}}</div>
			<div class="form-horizontal" style="padding-top: 0px; width: 100%;">
				<form id="uploadImg" enctype="multipart/form-data">
					<div class="templateShow-Info">
						<p>下载模板：</p>
						<p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
					</div>
					<div style="margin-left: 125px;">
						<a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
					</div>
					<div class="templateShow-Info">
						<p>上传文件：</p> 
						<p>仅支持:.xlsx，.xls;文件大小:≤4M</p>
					</div>
					<div style="margin-left: 125px;">
						<a v-if="hasPermission('hnsduser:importUser')"
							class="btn btn-primary" @click="importUserJiFen">&nbsp;开始导入</a> <input
							style="display: none;" name="uploadFile" id="uploadFile"
							type="file" @change="uploadFile" />
					</div>
					<div style="width: 100%; text-align: center;">
						<input type="button" class="btn btn-warning" @click="previousPage"
							value="返回" />
					</div>
				</form>
			</div>
		</div>
<!-- 		查看积分详情 -->
		<div v-show="!showList" class="panel panel-default">
			<div class="panel-heading">{{title}}</div>
			<div class="form-horizontal" style="padding-top: 0px; width: 100%;">
				<!--<div class="user-Info">
					<p>管理者姓名:</p>
					<p>{{hnslUser.userName}}</p>
				</div>
				<div class="user-Info">
					<p>管理者身份证号：</p>
					<p>{{hnslUser.userSfz}}</p>
				</div>-->
				<div class="user-Info">
					<p>本周订单数：</p>
					<p>{{weekNum}}</p>
				</div>
				<div class="user-Info">
					<p>本&nbsp; 月&nbsp; 订&nbsp;单&nbsp; 数：</p>
					<p>{{monthNum}}</p>
				</div>

				<div class="user-Info">
					<p>查询日期:</p>
					<p class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker"
							   id="dateTimeDetailRange" value="" type="text"
							   placeholder="日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
						</span> <input name="beginTime1" id="beginTime1" type="hidden"> <input
							name="endTime1" id="endTime1" type="hidden">
					</p>
				</div>
				<div class="user-Info">
					<p>年度订单数：</p>
					<p>{{yearNum}}</p>
				</div>
				<table id="jfDetailsGrid"></table>
				<div id="jfDetailsPager"></div>
				</br> </br>
				<!--	<div class="panel-heading"
                        style="clear: both; background-color: #ddd; width: 100%;">积分变动详情</div>
                    <div class="jifen-bt">
                        <p>积分时间</p>
                        <p>积分来源</p>
                        <p>积分数量</p>
                        <p>操&nbsp;作&nbsp;人</p>
                    </div>

                    <div class="jifen-Info" v-for="itme in integralDetailsList"
                        v-bind:value="itme.id">
                        <p>{{itme.createdDate}}</p>
                        <p>{{itme.integralRemark}}</p>
                        <p>{{itme.integralOperation}}</p>
                        <p>{{itme.createdUser}}</p>
                    </div>-->
				<div style="width: 100%; text-align: center;">
					<input type="button" class="btn btn-warning" @click="previousPage"
						value="返回" />
				</div>
			</div>
		</div>

		<!-- 数据下载审批人选择 -->
		<div class="modal" id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog" style="padding: 70px">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
								aria-hidden="true">×
						</button>
						<h4 class="modal-title" id="myModalLabel1" style="text-align: center">数据下载审批人选择</h4>
						<div style="display: flex">
							<input placeholder="请输入审批人姓名或手机号" v-model="searchText"
								   @input="onInput"/>
						</div>

						<table class="table table-bordered" style="width: 100%; text-align: center;margin-top: 10px">
							<tbody id="myTable">
							<tr>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td></td>
								<td></td>
							</tr>
							</tbody>
						</table>
						<div style="width: 100%;text-align: center;">
							<a href="#" id="prevPage" @click="prevPage">上一页</a>
							<span id="currentPage">1</span>
							<a href="#" id="nextPage" @click="nextPage">下一页</a>
						</div>

						<div style="text-align: right;margin-top: 20px;">
							<div class="col-sm-2 control-label"></div>
							<input type="button" class="btn btn-warning" @click="submitApprover" value="确定"/>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script src="../../js/modules/hnsl/hnsluserIntegrate.js"></script>
	<script src="../../js/components.js"></script>
</body>
</html>