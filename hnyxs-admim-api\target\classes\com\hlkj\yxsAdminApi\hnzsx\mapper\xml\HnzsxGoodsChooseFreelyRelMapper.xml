<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxGoodsChooseFreelyRelMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_choose_freely_rel a
        <where>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.goodsDetailsId != null">
                AND a.goods_details_id = #{param.goodsDetailsId}
            </if>
            <if test="param.chooseFreelyId != null">
                AND a.choose_freely_id = #{param.chooseFreelyId}
            </if>
            <if test="param.saleId != null">
                AND a.sale_id = #{param.saleId}
            </if>
            <if test="param.createdDate != null">
                AND a.created_date LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsChooseFreelyRel">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsChooseFreelyRel">
        <include refid="selectSql"></include>
    </select>

</mapper>
