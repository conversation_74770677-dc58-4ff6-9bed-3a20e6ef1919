<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslMessageNotificationMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_message_notification a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.readStatus != null">
                AND a.READ_STATUS = #{param.readStatus}
            </if>
            <if test="param.notificationCode != null">
                AND a.NOTIFICATION_CODE LIKE CONCAT('%', #{param.notificationCode}, '%')
            </if>
            <if test="param.notificationContent != null">
                AND a.NOTIFICATION_CONTENT LIKE CONCAT('%', #{param.notificationContent}, '%')
            </if>
            <if test="param.notificationContentDetails != null">
                AND a.NOTIFICATION_CONTENT_DETAILS LIKE CONCAT('%', #{param.notificationContentDetails}, '%')
            </if>
            <if test="param.orderId != null">
                AND a.ORDER_ID LIKE CONCAT('%', #{param.orderId}, '%')
            </if>
            <if test="param.customerName != null">
                AND a.CUSTOMER_NAME LIKE CONCAT('%', #{param.customerName}, '%')
            </if>
            <if test="param.customerPhone != null">
                AND a.CUSTOMER_PHONE LIKE CONCAT('%', #{param.customerPhone}, '%')
            </if>
            <if test="param.notificationUser != null">
                AND a.NOTIFICATION_USER LIKE CONCAT('%', #{param.notificationUser}, '%')
            </if>
            <if test="param.notificationType != null">
                AND a.NOTIFICATION_TYPE = #{param.notificationType}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslMessageNotification">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslMessageNotification">
        <include refid="selectSql"></include>
    </select>

</mapper>
