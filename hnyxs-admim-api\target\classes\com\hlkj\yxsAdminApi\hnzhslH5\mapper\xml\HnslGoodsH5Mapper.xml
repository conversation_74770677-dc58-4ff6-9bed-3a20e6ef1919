<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslGoodsH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_h5_goods a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsNumber != null and param.goodsNumber != ''">
                AND a.GOODS_NUMBER LIKE CONCAT('%', #{param.goodsNumber}, '%')
            </if>
            <if test="param.goodsMianNumber != null and param.goodsMianNumber != ''">
                AND a.GOODS_MIAN_NUMBER LIKE CONCAT('%', #{param.goodsMianNumber}, '%')
            </if>
            <if test="param.goodsName != null and param.goodsName != ''">
                AND a.GOODS_NAME LIKE CONCAT('%', #{param.goodsName}, '%')
            </if>
            <if test="param.goodsType != null">
                AND a.GOODS_TYPE LIKE CONCAT('%', #{param.goodsType}, '%')
            </if>
            <if test="param.goodsChildtypeNumber != null">
                AND a.GOODS_CHILDTYPE_NUMBER LIKE CONCAT('%', #{param.goodsChildtypeNumber}, '%')
            </if>
            <if test="param.goodsPrice != null">
                AND a.GOODS_PRICE = #{param.goodsPrice}
            </if>
            <if test="param.goodsImg != null">
                AND a.GOODS_IMG LIKE CONCAT('%', #{param.goodsImg}, '%')
            </if>
            <if test="param.goodsDetailsImg != null">
                AND a.GOODS_DETAILS_IMG LIKE CONCAT('%', #{param.goodsDetailsImg}, '%')
            </if>
            <if test="param.prestore != null">
                AND a.PRESTORE LIKE CONCAT('%', #{param.prestore}, '%')
            </if>
            <if test="param.minPrice != null">
                AND a.MIN_PRICE LIKE CONCAT('%', #{param.minPrice}, '%')
            </if>
            <if test="param.productionPrice != null">
                AND a.PRODUCTION_PRICE LIKE CONCAT('%', #{param.productionPrice}, '%')
            </if>
            <if test="param.installPrice != null">
                AND a.INSTALL_PRICE LIKE CONCAT('%', #{param.installPrice}, '%')
            </if>
            <if test="param.goodIntegral != null">
                AND a.GOOD_INTEGRAL LIKE CONCAT('%', #{param.goodIntegral}, '%')
            </if>
            <if test="param.bandwidth != null">
                AND a.BANDWIDTH LIKE CONCAT('%', #{param.bandwidth}, '%')
            </if>
            <if test="param.bandwidthType != null">
                AND a.BANDWIDTH_TYPE LIKE CONCAT('%', #{param.bandwidthType}, '%')
            </if>
            <if test="param.cardType != null">
                AND a.CARD_TYPE LIKE CONCAT('%', #{param.cardType}, '%')
            </if>
            <if test="param.transactionNum != null">
                AND a.TRANSACTION_NUM LIKE CONCAT('%', #{param.transactionNum}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS LIKE CONCAT('%', #{param.status}, '%')
            </if>
            <if test="param.orderNumber != null">
                AND a.ORDER_NUMBER LIKE CONCAT('%', #{param.orderNumber}, '%')
            </if>
            <if test="param.remark != null">
                AND a.REMARK LIKE CONCAT('%', #{param.remark}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.cardGoodsName != null">
                AND a.CARD_GOODS_NAME LIKE CONCAT('%', #{param.cardGoodsName}, '%')
            </if>
            <if test="param.cardGoodsNumber != null">
                AND a.CARD_GOODS_NUMBER LIKE CONCAT('%', #{param.cardGoodsNumber}, '%')
            </if>
            <if test="param.bandwidthGoodsName != null">
                AND a.BANDWIDTH_GOODS_NAME LIKE CONCAT('%', #{param.bandwidthGoodsName}, '%')
            </if>
            <if test="param.bandwidthGoodsNumber != null">
                AND a.BANDWIDTH_GOODS_NUMBER LIKE CONCAT('%', #{param.bandwidthGoodsNumber}, '%')
            </if>
            <if test="param.itvNumber != null">
                AND a.ITV_NUMBER LIKE CONCAT('%', #{param.itvNumber}, '%')
            </if>
            <if test="param.saflType != null">
                AND a.SAFL_TYPE = #{param.saflType}
            </if>
            <if test="param.goodsDetalt != null">
                AND a.GOODS_DETALT LIKE CONCAT('%', #{param.goodsDetalt}, '%')
            </if>
            <if test="param.minAge != null">
                AND a.MIN_AGE = #{param.minAge}
            </if>
            <if test="param.maxAge != null">
                AND a.MAX_AGE = #{param.maxAge}
            </if>
            <if test="param.astrictGoods != null">
                AND a.ASTRICT_GOODS LIKE CONCAT('%', #{param.astrictGoods}, '%')
            </if>
            <if test="param.goodsExplain != null">
                AND a.GOODS_EXPLAIN LIKE CONCAT('%', #{param.goodsExplain}, '%')
            </if>
            <if test="param.goodsTypeShow != null">
                AND a.GOODS_TYPE_SHOW = #{param.goodsTypeShow}
            </if>
            <if test="param.goodsHtmlUrl != null">
                AND a.GOODS_HTML_URL LIKE CONCAT('%', #{param.goodsHtmlUrl}, '%')
            </if>
            <if test="param.goodsPackageType != null">
                AND a.GOODS_PACKAGE_TYPE = #{param.goodsPackageType}
            </if>
            <if test="param.cpsList != null">
                AND a.CPS_LIST LIKE CONCAT('%', #{param.cpsList}, '%')
            </if>
            <if test="param.certificateSwitch != null">
                AND a.CERTIFICATE_SWITCH = #{param.certificateSwitch}
            </if>
            <if test="param.goodsServiceUrl != null">
                AND a.GOODS_SERVICE_URL LIKE CONCAT('%', #{param.goodsServiceUrl}, '%')
            </if>
            <if test="param.beginTime != null and param.beginTime != '' and param.endTime != null and param.endTime != ''">
                AND a.CREATED_DATE BETWEEN #{param.beginTime} and #{param.endTime}
            </if>
        </where>
        order by a.id desc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Goods">
        <!--        <include refid="selectSql"></include>-->
        SELECT tt.*
        FROM (
        select * from hnsl_h5_goods g1 WHERE 1=1
        <if test="param.goodsNumber!=null and param.goodsNumber!=''">
            AND g1.GOODS_NUMBER = #{param.goodsNumber}
        </if>
        <if test="param.goodsName!=null and param.goodsName!=''">
            AND g1.GOODS_NAME like concat(concat('%',#{param.goodsName}),'%')
        </if>
        <if test="param.goodsType!=null and param.goodsType!=''">
            AND g1.SAFL_TYPE = #{param.goodsType}
        </if>
        <if test="param.bandwidth!=null and param.bandwidth!=''">
            and g1.bandwidth is not null
        </if>
        <if test="param.goodsMianNumber!=null and param.goodsMianNumber!=''">
            AND g1.GOODS_MIAN_NUMBER = #{param.goodsMianNumber}
        </if>
        <if test="param.goodsChildtypeNumber!=null and param.goodsChildtypeNumber!=''">
            AND g1.GOODS_CHILDTYPE_NUMBER = #{param.goodsChildtypeNumber}
        </if>
        <if test="param.status!=null and param.status!=''">
            AND g1.status = #{param.status}
        </if>
        <if test="param.hnslType !=null and param.hnslType!='' and param.hnslType!=1">
            and g1.GOODS_PACKAGE_TYPE = #{param.hnslType}
        </if>
        <if test="param.beginTime != null and param.beginTime.trim() != ''">
            and g1.CREATED_DATE between
            #{param.beginTime} and
            #{param.endTime}
        </if>
        <if test="(param.schoolName!=null and param.schoolName!='') or
 				 (param.cityCode!=null and param.cityCode!='')">
            and exists(select 1 from hnsl_h5_goods_belong t
            left join hnsl_h5_school s1 on t.school_code=s1.school_code
            where t.status=1 and g1.goods_number=t.goods_number
            <if test="param.schoolName != null and param.schoolName != ''">
                and s1.school_name like concat(concat('%',#{param.schoolName}),'%')
            </if>
            <if test="param.cityCode != null and param.cityCode != ''">
                AND s1.school_city = #{param.cityCode}
            </if>
            group by t.goods_number)
        </if>
        order by g1.id desc) tt
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Goods">
        <include refid="selectSql"></include>
    </select>

    <delete id="deleteBatch">
        delete from hnsl_h5_goods where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryListSchoolBy" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Goods">
        select a.* from hnsl_h5_school a
        <if test="userPhone !=null and userPhone !=''">
            LEFT JOIN hnsl_h5_user_school b ON a.SCHOOL_CODE=b.SCHOOL_CODE
        </if>
        where a.STATUS=1
        <if test="userPhone !=null and userPhone !=''">
            AND b.USER_PHONE =#{userPhone}
        </if>
        <if test="cityCode !=null and cityCode !=''">
            AND a.SCHOOL_CITY =#{cityCode}
        </if>
        <if test="hnslChannel ==2 ">
            AND a.SCHOOL_GRADE_TYPE in (1,2,5)
        </if>
        <if test="hnslChannel ==3 or hnslChannel ==4 ">
            AND a.SCHOOL_GRADE_TYPE=#{hnslChannel}
        </if>
    </select>
</mapper>
