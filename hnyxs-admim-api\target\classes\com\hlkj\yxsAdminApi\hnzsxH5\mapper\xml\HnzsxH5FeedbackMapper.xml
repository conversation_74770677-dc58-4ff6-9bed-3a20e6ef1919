<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.HnzsxH5FeedbackMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hlkj.yxsAdminApi.hnzsxH5.entity.HnzsxH5FeedbackEntity">
        <id column="id" property="id" />
        <result column="feedback_type" property="feedbackType" />
        <result column="content" property="content" />
        <result column="contact_phone" property="contactPhone" />
        <result column="image_urls" property="imageUrls" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_phone" property="userPhone" />
        <result column="reply_content" property="replyContent" />
        <result column="reply_time" property="replyTime" />
        <result column="reply_user_id" property="replyUserId" />
        <result column="reply_user_name" property="replyUserName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, feedback_type, content, contact_phone, image_urls, create_time, update_time, status, 
        user_id, user_name, user_phone, reply_content, reply_time, reply_user_id, reply_user_name
    </sql>
    
</mapper> 