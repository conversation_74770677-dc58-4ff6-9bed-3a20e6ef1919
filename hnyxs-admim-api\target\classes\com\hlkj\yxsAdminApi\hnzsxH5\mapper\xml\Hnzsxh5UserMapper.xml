<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5UserMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.ID, a.STAFF_CODE, a.USER_ACCEPT_ID,
        a.USER_NUMBER, a.USER_NAME, a.USER_PHONE,
        a.CITY, a.CITYCODE, a.STATUS, a.CREATED_DATE,
        a.UPDATED_DATE, a.STAFF_ID, a.COMPANY_NAME,
        a.REGION_NAME, a.CHANNEL_ATTR, a.LAN_NAME, a.ORG_ID,
        a.ORG_LEVEL, a.ORG_NAME, a.ORG_SUBTYPE, a.REGION_ID,
        a.SALE_BOX_CODE, a.SALES_ORG_CODE
        FROM hnzsxh5_user a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.staffCode != null">
                AND a.STAFF_CODE LIKE CONCAT('%', #{param.staffCode}, '%')
            </if>
            <if test="param.userAcceptId != null">
                AND a.USER_ACCEPT_ID LIKE CONCAT('%', #{param.userAcceptId}, '%')
            </if>
            <if test="param.userNumber != null">
                AND a.USER_NUMBER LIKE CONCAT('%', #{param.userNumber}, '%')
            </if>
            <if test="param.userName != null">
                AND a.USER_NAME LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.city != null">
                AND a.CITY LIKE CONCAT('%', #{param.city}, '%')
            </if>
            <if test="param.citycode != null">
                AND a.CITYCODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS LIKE CONCAT('%', #{param.status}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.staffId != null">
                AND a.STAFF_ID LIKE CONCAT('%', #{param.staffId}, '%')
            </if>
            <if test="param.companyName != null">
                AND a.COMPANY_NAME LIKE CONCAT('%', #{param.companyName}, '%')
            </if>
            <if test="param.regionName != null">
                AND a.REGION_NAME LIKE CONCAT('%', #{param.regionName}, '%')
            </if>
            <if test="param.channelAttr != null">
                AND a.CHANNEL_ATTR LIKE CONCAT('%', #{param.channelAttr}, '%')
            </if>
            <if test="param.lanName != null">
                AND a.LAN_NAME LIKE CONCAT('%', #{param.lanName}, '%')
            </if>
            <if test="param.orgId != null">
                AND a.ORG_ID LIKE CONCAT('%', #{param.orgId}, '%')
            </if>
            <if test="param.orgLevel != null">
                AND a.ORG_LEVEL LIKE CONCAT('%', #{param.orgLevel}, '%')
            </if>
            <if test="param.orgName != null">
                AND a.ORG_NAME LIKE CONCAT('%', #{param.orgName}, '%')
            </if>
            <if test="param.orgSubtype != null">
                AND a.ORG_SUBTYPE LIKE CONCAT('%', #{param.orgSubtype}, '%')
            </if>
            <if test="param.regionId != null">
                AND a.REGION_ID LIKE CONCAT('%', #{param.regionId}, '%')
            </if>
            <if test="param.saleBoxCode != null">
                AND a.SALE_BOX_CODE LIKE CONCAT('%', #{param.saleBoxCode}, '%')
            </if>
            <if test="param.salesOrgCode != null">
                AND a.SALES_ORG_CODE LIKE CONCAT('%', #{param.salesOrgCode}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5User">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5User">
        <include refid="selectSql"></include>
    </select>


</mapper>
