# 订单统计汇总功能测试数据示例

## 接口测试示例

### 1. 按模块类型汇总统计接口测试

**请求URL**: `POST /api/hnzsxH5/hnzsxh5-order-info/getOrderCountByModuleType`

**请求参数**:
```json
{
  "date": "2025-01-01",
  "endDate": "2025-01-31"
}
```

**预期返回数据**:
```json
[
  {
    "moduleTypeName": "智能终端",
    "moduleTypeCode": "C_ZN",
    "totalCount": 242,
    "cityCount": 5,
    "percentage": 78.1
  },
  {
    "moduleTypeName": "云端",
    "moduleTypeCode": "C_YD",
    "totalCount": 36,
    "cityCount": 2,
    "percentage": 11.6
  },
  {
    "moduleTypeName": "未分类",
    "moduleTypeCode": "UNCATEGORIZED",
    "totalCount": 23,
    "cityCount": 1,
    "percentage": 7.4
  },
  {
    "moduleTypeName": "融合",
    "moduleTypeCode": "C_RH",
    "totalCount": 16,
    "cityCount": 1,
    "percentage": 5.2
  }
]
```

### 2. 按地市汇总统计接口测试

**请求URL**: `POST /api/hnzsxH5/hnzsxh5-order-info/getOrderCountByCity`

**请求参数**:
```json
{
  "date": "2025-01-01",
  "endDate": "2025-01-31"
}
```

**预期返回数据**:
```json
[
  {
    "cityCode": "731",
    "cityName": "长沙",
    "totalCount": 2478,
    "moduleCount": 8,
    "percentage": 45.2
  },
  {
    "cityCode": "730",
    "cityName": "岳阳",
    "totalCount": 1842,
    "moduleCount": 6,
    "percentage": 33.6
  },
  {
    "cityCode": "733",
    "cityName": "株洲",
    "totalCount": 837,
    "moduleCount": 5,
    "percentage": 15.3
  },
  {
    "cityCode": "732",
    "cityName": "湘潭",
    "totalCount": 875,
    "moduleCount": 4,
    "percentage": 16.0
  }
]
```

## 前端页面展示效果

### 页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│                        订单统计页面                              │
├─────────────────────────────────────────────────────────────────┤
│  日期选择: [2025-01-01] 至 [2025-01-31]                         │
├─────────────────────────────────────────────────────────────────┤
│                    订单总数：5,480 单                            │
├─────────────────────────────────────────────────────────────────┤
│                   各地市订单统计图表                             │
├─────────────────────────────────────────────────────────────────┤
│                  按模块类型汇总发展总量统计                       │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │序号│模块类型名称│模块类型编码│订单总量│涉及地市数│占比(%)│ │
│ │ 1  │智能终端    │C_ZN       │  242   │    5    │ 78.1  │ │
│ │ 2  │云端       │C_YD       │   36   │    2    │ 11.6  │ │
│ │ 3  │未分类     │UNCATEGORIZED│  23   │    1    │  7.4  │ │
│ │ 4  │融合       │C_RH       │   16   │    1    │  5.2  │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                   按地市汇总发展总量统计                          │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │序号│地市编码│地市名称│订单总量│涉及模块数│占比(%)│         │
│ │ 1  │731    │长沙    │ 2478   │    8    │ 45.2  │         │
│ │ 2  │730    │岳阳    │ 1842   │    6    │ 33.6  │         │
│ │ 3  │733    │株洲    │  837   │    5    │ 15.3  │         │
│ │ 4  │732    │湘潭    │  875   │    4    │ 16.0  │         │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│              各地市按模块分类订单数量统计表                       │
└─────────────────────────────────────────────────────────────────┘
```

### 表格特点
1. **独立布局**: 两个汇总表格分别占用独立行，布局清晰
2. **全宽显示**: 表格占满整行宽度，充分利用页面空间
3. **自适应列宽**: 使用`min-width`属性，列宽自动调整
4. **高亮显示**: 订单总量用蓝色高亮，占比用绿色显示
5. **智能排序**: 按订单总量降序排列
6. **响应式设计**: 支持不同屏幕尺寸
7. **优化行高**: 表格行高适中，数据展示更清晰
8. **美观样式**: 表头背景色、悬停效果等提升用户体验

## 业务价值

### 模块类型汇总统计
- 快速识别主要业务模块（如智能终端占78.1%）
- 了解各模块类型的地市覆盖情况
- 为产品策略调整提供数据支持

### 地市汇总统计  
- 识别重点发展地市（如长沙占45.2%）
- 了解各地市的业务多样性（模块数量）
- 为区域发展策略提供依据

### 综合分析
- 结合两个维度进行交叉分析
- 发现业务发展的热点区域和热点产品
- 为资源配置和市场推广提供决策支持
