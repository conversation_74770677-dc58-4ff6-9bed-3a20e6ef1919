<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslGoodsDraftMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_goods_draft a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsNumber != null">
                AND a.GOODS_NUMBER LIKE CONCAT('%', #{param.goodsNumber}, '%')
            </if>
            <if test="param.goodsMianNumber != null">
                AND a.GOODS_MIAN_NUMBER LIKE CONCAT('%', #{param.goodsMianNumber}, '%')
            </if>
            <if test="param.goodsName != null">
                AND a.GOODS_NAME LIKE CONCAT('%', #{param.goodsName}, '%')
            </if>
            <if test="param.goodsType != null">
                AND a.GOODS_TYPE LIKE CONCAT('%', #{param.goodsType}, '%')
            </if>
            <if test="param.goodsChildtypeNumber != null">
                AND a.GOODS_CHILDTYPE_NUMBER LIKE CONCAT('%', #{param.goodsChildtypeNumber}, '%')
            </if>
            <if test="param.goodsPrice != null">
                AND a.GOODS_PRICE = #{param.goodsPrice}
            </if>
            <if test="param.goodsImg != null">
                AND a.GOODS_IMG LIKE CONCAT('%', #{param.goodsImg}, '%')
            </if>
            <if test="param.goodsDetailsImg != null">
                AND a.GOODS_DETAILS_IMG LIKE CONCAT('%', #{param.goodsDetailsImg}, '%')
            </if>
            <if test="param.prestore != null">
                AND a.PRESTORE LIKE CONCAT('%', #{param.prestore}, '%')
            </if>
            <if test="param.minPrice != null">
                AND a.MIN_PRICE LIKE CONCAT('%', #{param.minPrice}, '%')
            </if>
            <if test="param.productionPrice != null">
                AND a.PRODUCTION_PRICE LIKE CONCAT('%', #{param.productionPrice}, '%')
            </if>
            <if test="param.installPrice != null">
                AND a.INSTALL_PRICE LIKE CONCAT('%', #{param.installPrice}, '%')
            </if>
            <if test="param.goodIntegral != null">
                AND a.GOOD_INTEGRAL LIKE CONCAT('%', #{param.goodIntegral}, '%')
            </if>
            <if test="param.bandwidth != null">
                AND a.BANDWIDTH LIKE CONCAT('%', #{param.bandwidth}, '%')
            </if>
            <if test="param.bandwidthType != null">
                AND a.BANDWIDTH_TYPE LIKE CONCAT('%', #{param.bandwidthType}, '%')
            </if>
            <if test="param.cardType != null">
                AND a.CARD_TYPE LIKE CONCAT('%', #{param.cardType}, '%')
            </if>
            <if test="param.transactionNum != null">
                AND a.TRANSACTION_NUM LIKE CONCAT('%', #{param.transactionNum}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS LIKE CONCAT('%', #{param.status}, '%')
            </if>
            <if test="param.orderNumber != null">
                AND a.ORDER_NUMBER LIKE CONCAT('%', #{param.orderNumber}, '%')
            </if>
            <if test="param.remark != null">
                AND a.REMARK LIKE CONCAT('%', #{param.remark}, '%')
            </if>
            <if test="param.cardGoodsName != null">
                AND a.CARD_GOODS_NAME LIKE CONCAT('%', #{param.cardGoodsName}, '%')
            </if>
            <if test="param.cardGoodsNumber != null">
                AND a.CARD_GOODS_NUMBER LIKE CONCAT('%', #{param.cardGoodsNumber}, '%')
            </if>
            <if test="param.bandwidthGoodsName != null">
                AND a.BANDWIDTH_GOODS_NAME LIKE CONCAT('%', #{param.bandwidthGoodsName}, '%')
            </if>
            <if test="param.bandwidthGoodsNumber != null">
                AND a.BANDWIDTH_GOODS_NUMBER LIKE CONCAT('%', #{param.bandwidthGoodsNumber}, '%')
            </if>
            <if test="param.itvNumber != null">
                AND a.ITV_NUMBER LIKE CONCAT('%', #{param.itvNumber}, '%')
            </if>
            <if test="param.saflType != null">
                AND a.SAFL_TYPE = #{param.saflType}
            </if>
            <if test="param.goodsDetalt != null">
                AND a.GOODS_DETALT LIKE CONCAT('%', #{param.goodsDetalt}, '%')
            </if>
            <if test="param.minAge != null">
                AND a.MIN_AGE = #{param.minAge}
            </if>
            <if test="param.maxAge != null">
                AND a.MAX_AGE = #{param.maxAge}
            </if>
            <if test="param.astrictGoods != null">
                AND a.ASTRICT_GOODS LIKE CONCAT('%', #{param.astrictGoods}, '%')
            </if>
            <if test="param.goodsExplain != null">
                AND a.GOODS_EXPLAIN LIKE CONCAT('%', #{param.goodsExplain}, '%')
            </if>
            <if test="param.goodsTypeShow != null">
                AND a.GOODS_TYPE_SHOW = #{param.goodsTypeShow}
            </if>
            <if test="param.goodsHtmlUrl != null">
                AND a.GOODS_HTML_URL LIKE CONCAT('%', #{param.goodsHtmlUrl}, '%')
            </if>
            <if test="param.goodsPackageType != null">
                AND a.GOODS_PACKAGE_TYPE = #{param.goodsPackageType}
            </if>
            <if test="param.cpsList != null">
                AND a.CPS_LIST LIKE CONCAT('%', #{param.cpsList}, '%')
            </if>
            <if test="param.certificateSwitch != null">
                AND a.CERTIFICATE_SWITCH = #{param.certificateSwitch}
            </if>
            <if test="param.goodsServiceUrl != null">
                AND a.GOODS_SERVICE_URL LIKE CONCAT('%', #{param.goodsServiceUrl}, '%')
            </if>
            <if test="param.goodsShareImg != null">
                AND a.GOODS_SHARE_IMG LIKE CONCAT('%', #{param.goodsShareImg}, '%')
            </if>
            <if test="param.goodsShareSwitch != null">
                AND a.GOODS_SHARE_SWITCH LIKE CONCAT('%', #{param.goodsShareSwitch}, '%')
            </if>
            <if test="param.configurationName != null">
                AND a.CONFIGURATION_NAME LIKE CONCAT('%', #{param.configurationName}, '%')
            </if>
            <if test="param.configurationNumber != null">
                AND a.CONFIGURATION_NUMBER LIKE CONCAT('%', #{param.configurationNumber}, '%')
            </if>
            <if test="param.submitType != null">
                AND a.SUBMIT_TYPE LIKE CONCAT('%', #{param.submitType}, '%')
            </if>
            <if test="param.submitDate != null">
                AND a.SUBMIT_DATE LIKE CONCAT('%', #{param.submitDate}, '%')
            </if>
            <if test="param.approvalDate != null">
                AND a.APPROVAL_DATE LIKE CONCAT('%', #{param.approvalDate}, '%')
            </if>
            <if test="param.approvalStatus != null">
                AND a.APPROVAL_STATUS LIKE CONCAT('%', #{param.approvalStatus}, '%')
            </if>
            <if test="param.approvalUser != null">
                AND a.APPROVAL_USER LIKE CONCAT('%', #{param.approvalUser}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsDraft">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsDraft">
        <include refid="selectSql"></include>
    </select>

</mapper>
