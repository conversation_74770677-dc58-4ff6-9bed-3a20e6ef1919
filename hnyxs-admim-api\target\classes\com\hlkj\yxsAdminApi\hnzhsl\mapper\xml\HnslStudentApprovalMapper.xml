<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslStudentApprovalMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_student_approval a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.auditDate != null">
                AND a.AUDIT_DATE LIKE CONCAT('%', #{param.auditDate}, '%')
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.schoolName != null">
                AND a.SCHOOL_NAME LIKE CONCAT('%', #{param.schoolName}, '%')
            </if>
            <if test="param.userName != null">
                AND a.USER_NAME LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.teamLevel != null">
                AND a.TEAM_LEVEL LIKE CONCAT('%', #{param.teamLevel}, '%')
            </if>
            <if test="param.userCard != null">
                AND a.USER_CARD LIKE CONCAT('%', #{param.userCard}, '%')
            </if>
            <if test="param.userAge != null">
                AND a.USER_AGE LIKE CONCAT('%', #{param.userAge}, '%')
            </if>
            <if test="param.userServiceTime != null">
                AND a.USER_SERVICE_TIME LIKE CONCAT('%', #{param.userServiceTime}, '%')
            </if>
            <if test="param.teamName != null">
                AND a.TEAM_NAME LIKE CONCAT('%', #{param.teamName}, '%')
            </if>
            <if test="param.userKpi != null">
                AND a.USER_KPI LIKE CONCAT('%', #{param.userKpi}, '%')
            </if>
            <if test="param.userDate != null">
                AND a.USER_DATE LIKE CONCAT('%', #{param.userDate}, '%')
            </if>
            <if test="param.businessNumber1 != null">
                AND a.BUSINESS_NUMBER1 = #{param.businessNumber1}
            </if>
            <if test="param.businessIntegration1 != null">
                AND a.BUSINESS_INTEGRATION1 = #{param.businessIntegration1}
            </if>
            <if test="param.businessNumber2 != null">
                AND a.BUSINESS_NUMBER2 = #{param.businessNumber2}
            </if>
            <if test="param.businessIntegration2 != null">
                AND a.BUSINESS_INTEGRATION2 = #{param.businessIntegration2}
            </if>
            <if test="param.userIntegration != null">
                AND a.USER_INTEGRATION = #{param.userIntegration}
            </if>
            <if test="param.teamIntegration != null">
                AND a.TEAM_INTEGRATION = #{param.teamIntegration}
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.money1 != null">
                AND a.MONEY1 = #{param.money1}
            </if>
            <if test="param.money2 != null">
                AND a.MONEY2 = #{param.money2}
            </if>
            <if test="param.money3 != null">
                AND a.MONEY3 = #{param.money3}
            </if>
            <if test="param.money4 != null">
                AND a.MONEY4 = #{param.money4}
            </if>
            <if test="param.money5 != null">
                AND a.MONEY5 = #{param.money5}
            </if>
            <if test="param.userBankName != null">
                AND a.USER_BANK_NAME LIKE CONCAT('%', #{param.userBankName}, '%')
            </if>
            <if test="param.userBankNumber != null">
                AND a.USER_BANK_NUMBER LIKE CONCAT('%', #{param.userBankNumber}, '%')
            </if>
            <if test="param.reportPicture != null">
                AND a.REPORT_PICTURE LIKE CONCAT('%', #{param.reportPicture}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.provincialUser != null">
                AND a.PROVINCIAL_USER LIKE CONCAT('%', #{param.provincialUser}, '%')
            </if>
            <if test="param.provincialDate != null">
                AND a.PROVINCIAL_DATE LIKE CONCAT('%', #{param.provincialDate}, '%')
            </if>
            <if test="param.businessIntegration3 != null">
                AND a.BUSINESS_INTEGRATION3 = #{param.businessIntegration3}
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.teamCode != null">
                AND a.TEAM_CODE LIKE CONCAT('%', #{param.teamCode}, '%')
            </if>
            <if test="param.teamCount != null">
                AND a.TEAM_COUNT LIKE CONCAT('%', #{param.teamCount}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslStudentApproval">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslStudentApproval">
        <include refid="selectSql"></include>
    </select>

</mapper>
