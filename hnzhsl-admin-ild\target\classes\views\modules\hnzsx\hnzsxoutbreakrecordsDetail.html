<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>

    <div class="row">
      <div class="form-group col-md-2">
					<label>本地网:</label> <select class="form-control"
						style="height: 32px;" v-model="hnzsxOutbreakRecords.cityCode">
						<option value=''>全部</option>
						<option v-for="itme in city" v-bind:value="itme.cityCode">
							{{itme.cityName}}</option>
					</select>
				</div>
				
				<div class="form-group col-md-2" style="height: 32px;">
					<label>签到日期:</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							id="dateTimeRange" @keyup.enter="query" value="" type="text"
							placeholder="日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
						</span> <input name="beginTime" id="beginTime" type="hidden"> <input
							name="endTime" id="endTime" type="hidden">
					</div>
				</div>
				
				<div class="form-group col-md-2" style="height: 32px;">
					<label>填报日期:</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							id="dateTimeRanges" @keyup.enter="query" value="" type="text"
							placeholder="日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
						</span> <input name="beginTimes" id="beginTimes" type="hidden"> <input
							name="endTimes" id="endTimes" type="hidden">
					</div>
				</div>
    </div>


	<div v-show="showList">
		<div class="grid-btn" style="margin-left: 1.5%">
			<a v-if="hasPermission('hnzsxoutbreakrecords:query')" class="btn btn-primary" @click="query">&nbsp;查询</a>
		   	<a v-if="hasPermission('hnzsxoutbreakrecords:exportHnzsxoutbreakrecords')" class="btn btn-primary" @click="exportHnzsxoutbreakrecords">&nbsp;导出</a>
		   
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
    
    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
											<div class="form-group">
			   	<div class="col-sm-2 control-label">选项3（1、是2、否）今日营业人员（含客户）是否发现确诊或疑似病例？</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.option3" placeholder="选项3（1、是2、否）今日营业人员（含客户）是否发现确诊或疑似病例？"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">是否签到（1、已签到2、未签到）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.isSignIn" placeholder="是否签到（1、已签到2、未签到）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">门店id</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.shopId" placeholder="门店id"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">选项4（1、是2、否）防疫方面门店遇到的困难以及需要省市公司支撑的 事情？</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.option4" placeholder="选项4（1、是2、否）防疫方面门店遇到的困难以及需要省市公司支撑的 事情？"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">选项2（1、是2、否）是否按照要求每日对厅店进行消毒？</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.option2" placeholder="选项2（1、是2、否）是否按照要求每日对厅店进行消毒？"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">修改时间</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.updateDate" placeholder="修改时间"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">地市编码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.cityCode" placeholder="地市编码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">姓名</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.name" placeholder="姓名"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">确证疑似病例备注信息</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.remark" placeholder="确证疑似病例备注信息"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">上班人数</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.workPopulation" placeholder="上班人数"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">是否1-3级自营厅(1、是2、否)</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.isProprietaryHall" placeholder="是否1-3级自营厅(1、是2、否)"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">openid</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.openId" placeholder="openid"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">唯一标识</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.id" placeholder="唯一标识"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">是否提交疫情记录（1、已提交2、未提交）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.isSaveEpidemic" placeholder="是否提交疫情记录（1、已提交2、未提交）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">手机号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.phone" placeholder="手机号"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">状态（1、可用2、不可用）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.status" placeholder="状态（1、可用2、不可用）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">选项1（1、是2、否）今日营业人员佩戴口罩、消毒液、洗手液等措施是否 落实？</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnzsxOutbreakRecords.option1" placeholder="选项1（1、是2、否）今日营业人员佩戴口罩、消毒液、洗手液等措施是否 落实？"/>
			    </div>
			</div>
							<div class="form-group">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div>
		</form>
	</div>
</div>

<script src="../../js/modules/hnzsx/hnzsxoutbreakrecordsDetail.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>