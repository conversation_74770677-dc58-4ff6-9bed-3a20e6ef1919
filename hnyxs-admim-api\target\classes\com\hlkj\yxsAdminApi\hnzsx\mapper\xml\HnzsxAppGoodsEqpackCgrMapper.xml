<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsEqpackCgrMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_eqpack_cgr a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.equity != null">
                AND a.EQUITY = #{param.equity}
            </if>
            <if test="param.parentCid != null">
                AND a.PARENT_CID = #{param.parentCid}
            </if>
            <if test="param.catLevel != null">
                AND a.CAT_LEVEL = #{param.catLevel}
            </if>
            <if test="param.salesGoodsId != null">
                AND a.SALES_GOODS_ID LIKE CONCAT('%', #{param.salesGoodsId}, '%')
            </if>
            <if test="param.packageGradeId != null">
                AND a.PACKAGE_GRADE_ID LIKE CONCAT('%', #{param.packageGradeId}, '%')
            </if>
            <if test="param.createDate != null">
                AND a.CREATE_DATE LIKE CONCAT('%', #{param.createDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.UPDATE_DATE LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
            <if test="param.operator != null">
                AND a.OPERATOR LIKE CONCAT('%', #{param.operator}, '%')
            </if>
            <if test="param.sort != null">
                AND a.SORT = #{param.sort}
            </if>
            <if test="param.equipmentTypeName != null">
                AND a.EQUIPMENT_TYPE_NAME LIKE CONCAT('%', #{param.equipmentTypeName}, '%')
            </if>
            <if test="param.equityName != null">
                AND a.EQUITY_NAME LIKE CONCAT('%', #{param.equityName}, '%')
            </if>
            <if test="param.saleName != null">
                AND a.SALE_NAME LIKE CONCAT('%', #{param.saleName}, '%')
            </if>
            <if test="param.eqAction != null">
                AND a.EQ_ACTION LIKE CONCAT('%', #{param.eqAction}, '%')
            </if>
            <if test="param.acceptanceSteps != null">
                AND a.ACCEPTANCE_STEPS LIKE CONCAT('%', #{param.acceptanceSteps}, '%')
            </if>
            <if test="param.exhibitStatus != null">
                AND a.EXHIBIT_STATUS LIKE CONCAT('%', #{param.exhibitStatus}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsEqpackCgr">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsEqpackCgr">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询终端权益已选分期内容回显 -->
    <select id="queryZdqyfqShow" parameterType="Integer" resultType="map">
        select
            hgec.id as eqpackId,
            hgec.EQUITY as equity,
            hgec.EQUITY_NAME as equityName,
            hgec.SALES_GOODS_ID as salesGoodsId,
            htt.id as terminalId,
            htt.TERMINAL_NAME as terminalName,
            htt.TERMINAL_NUMBER as terminalNumber
        from
            hnzsx_goods_eqpack_cgr hgec
                inner join hnzsx_terminal_type htt on
                hgec.EQUIPMENT_TYPE_NAME = htt.TERMINAL_NAME and htt.TERMINAL_TYPE = 1
        where
            hgec.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

</mapper>
