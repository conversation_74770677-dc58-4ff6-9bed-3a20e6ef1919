<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5RuleConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5RuleConfig">
        <id column="ID" property="id" />
        <result column="RULE_CODE" property="ruleCode" />
        <result column="RULE_NAME" property="ruleName" />
        <result column="RULE_TYPE" property="ruleType" />
        <result column="RULE_CONTENT" property="ruleContent" />
        <result column="RULE_DESC" property="ruleDesc" />
        <result column="STATUS" property="status" />
        <result column="CREATED_BY" property="createdBy" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="UPDATED_BY" property="updatedBy" />
        <result column="UPDATED_DATE" property="updatedDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CONTENT, RULE_DESC, STATUS, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hnzsxh5_rule_config
        <where>
            <if test="param.id != null">
                AND ID = #{param.id}
            </if>
            <if test="param.ruleCode != null and param.ruleCode != ''">
                AND RULE_CODE LIKE CONCAT('%', #{param.ruleCode}, '%')
            </if>
            <if test="param.ruleName != null and param.ruleName != ''">
                AND RULE_NAME LIKE CONCAT('%', #{param.ruleName}, '%')
            </if>
            <if test="param.ruleType != null and param.ruleType != ''">
                AND RULE_TYPE = #{param.ruleType}
            </if>
            <if test="param.status != null">
                AND STATUS = #{param.status}
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                AND CREATED_DATE >= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                AND CREATED_DATE &lt;= #{param.createTimeEnd}
            </if>
        </where>
        ORDER BY CREATED_DATE DESC
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hnzsxh5_rule_config
        <where>
            <if test="param.id != null">
                AND ID = #{param.id}
            </if>
            <if test="param.ruleCode != null and param.ruleCode != ''">
                AND RULE_CODE LIKE CONCAT('%', #{param.ruleCode}, '%')
            </if>
            <if test="param.ruleName != null and param.ruleName != ''">
                AND RULE_NAME LIKE CONCAT('%', #{param.ruleName}, '%')
            </if>
            <if test="param.ruleType != null and param.ruleType != ''">
                AND RULE_TYPE = #{param.ruleType}
            </if>
            <if test="param.status != null">
                AND STATUS = #{param.status}
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                AND CREATED_DATE >= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                AND CREATED_DATE &lt;= #{param.createTimeEnd}
            </if>
        </where>
        ORDER BY CREATED_DATE DESC
    </select>

</mapper> 