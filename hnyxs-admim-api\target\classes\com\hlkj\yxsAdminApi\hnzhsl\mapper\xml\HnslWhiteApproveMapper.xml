<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslWhiteApproveMapper">

    <sql id="selectSql">
        SELECT t.* FROM hnsl_white_approve t
        <where>
            <if test="param.batchCode != null and param.batchCode !='' ">
                t.BATCH_CODE = #{param.batchCode}
            </if>
            <if test="param.fileName != null and param.fileName !='' ">
                AND t.FILE_NAME like concat('%', #{param.fileName}, '%')
            </if>
            <if test="param.approveDate != null and param.approveDate !=''">
                AND t.APPROVE_DATE = #{param.approveDate}
            </if>
            <if test="param.queryStatus == 6">
                AND t.CITY_CODE = #{param.cityCode} AND t.APPROVE_USER = #{param.userName}
            </if>
            AND t.DELETE_RECORD = 0
        </where>
        order by t.ID desc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApprove">
        <include refid="selectSql"></include>
    </select>

<!--	-->
<!--	<select id="queryListMap" resultType="java.util.HashMap">-->
<!--	SELECT tt.*-->
<!--		FROM (-->
<!--		select * from hnsl_white_approve-->
<!--        <choose>-->
<!--            <when test="sidx != null and sidx.trim() != ''">-->
<!--                order by ${sidx} ${order}-->
<!--            </when>-->
<!--			<otherwise>-->
<!--                order by ID desc-->
<!--			</otherwise>-->
<!--        </choose>-->
<!--		) tt-->
<!--		<choose>-->
<!--			<when test="page != null and limit != null">-->
<!--				LIMIT #{page},#{limit}-->
<!--			</when>-->
<!--			<otherwise>-->

<!--			</otherwise>-->
<!--		</choose>-->
<!--		-->
<!--	</select>-->
<!--	-->
<!-- 	<select id="queryTotal" resultType="int">-->
<!--		SELECT count(*) FROM hnsl_white_approve t-->
<!--		<where>-->
<!--			<if test="batchCode != null and batchCode !='' ">-->
<!--				t.BATCH_CODE = #{batchCode}-->
<!--			</if>-->
<!--			<if test="fileName != null and fileName !='' ">-->
<!--				AND t.FILE_NAME like concat('%', #{fileName}, '%')-->
<!--			</if>-->
<!--			<if test="approveDate != null and approveDate !=''">-->
<!--				AND t.APPROVE_DATE = #{approveDate}-->
<!--			</if>-->
<!--		</where>-->
<!--	</select>-->
	 
<!--	<insert id="save" parameterType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApprove">-->
<!--		-->
<!--		insert into hnsl_white_approve-->
<!--		(-->
<!--			APPLY_ID,-->
<!--			BATCH_CODE,-->
<!--			CITY_CODE,-->
<!--			FILE_NAME,-->
<!--			FILE_PATH,-->
<!--			APPROVE_FILE,-->
<!--			APPROVE_FILE_PATH,-->
<!--			WHITE_PHONE_NUMBER,-->
<!--			ACTIVE_EVENT,-->
<!--			APPROVE_PHONE,-->
<!--			APPROVE_USER,-->
<!--			SUBMIT_APPROVAL_TIME,-->
<!--			STATUS,-->
<!--			REMARK,-->
<!--		 	CREATE_Time,-->
<!--			DELETE_RECORD-->
<!--		)-->
<!--		values-->
<!--		(-->
<!--		 	#{applyId},-->
<!--			#{batchCode},-->
<!--		 	#{cityCode},-->
<!--			#{fileName},-->
<!--			#{filePath},-->
<!--			#{approveFile},-->
<!--			#{approveFilePath},-->
<!--			#{whitePhoneNumber},-->
<!--			#{activeEvent},-->
<!--			#{approvePhone},-->
<!--			#{approveUser},-->
<!--			#{submitApprovalTime},-->
<!--			#{status},-->
<!--			#{remark},-->
<!--			#{createTime},-->
<!--			#{deleteRecord}-->
<!--		)-->
<!--	</insert>-->

<!--	<update id="update" parameterType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApprove">-->
<!--		update hnsl_white_approve-->
<!--		<set>-->
<!--			<if test="status != null">STATUS = #{status}, </if>-->
<!--			<if test="deleteRecord != null">DELETE_RECORD = #{deleteRecord}, </if>-->
<!--		</set>-->
<!--		where id = #{id}-->
<!--	</update>-->
<!--	-->
<!--	<select id="queryListTable" resultType="java.util.HashMap">-->
<!--		select t.*,t1.school_name,ifnull(t.s3,0)+ifnull(t.s4,0) as s5 from (-->
<!--		select t.batch_code,t.school_code,max(t.city_code) as city_code,max(t.school_channel) as school_channel-->
<!--		,max(t.created_date) as created_date,count(*) as zs,-->
<!--		sum((case t.number_status when 1 then 1 else 0 end)) as s1,-->
<!--		sum((case t.number_status when 2 then 1 else 0 end)) as s2,-->
<!--		sum((case t.number_status when 3 then 1 else 0 end)) as s3,-->
<!--		sum((case t.number_status when 4 then 1 else 0 end)) as s4-->
<!--		from hnsl_white_list t-->
<!--		<where>-->
<!--			<if test="status != null and status !='' ">-->
<!--				AND t.STATUS = #{status}-->
<!--			</if>-->
<!--			<if test="numberStatus != null and numberStatus !='' ">-->
<!--				AND t.NUMBER_STATUS = #{numberStatus}-->
<!--			</if>-->
<!--			<if test="userPhone != null and userPhone !=''">-->
<!--				AND t.USER_PHONE = #{userPhone}-->
<!--			</if>-->
<!--			<if test="cityCode != null and cityCode != '' ">-->
<!--				AND t.CITY_CODE = #{cityCode}-->
<!--			</if>-->
<!--			<if test="beginTime != null and beginTime.trim() != ''">-->
<!--				AND t.CREATED_DATE between-->
<!--				#{beginTime} and-->
<!--						#{endTime}-->
<!--			</if>-->
<!--			<if test="iccid != null and iccid != '' ">-->
<!--				AND t.ICCID = #{iccid}-->
<!--			</if>-->
<!--			<if test="batchCode != null and batchCode != '' ">-->
<!--				AND t.BATCH_CODE = #{batchCode}-->
<!--			</if>-->
<!--		</where>-->
<!--		group by t.batch_code,t.school_code) t-->
<!--		left join hnsl_school t1 on t.school_code=t1.school_code-->
<!--		<where>-->
<!--			<if test="schoolName != null and schoolName != '' ">-->
<!--				AND t1.SCHOOL_NAME like concat('%',#{schoolName},'%')-->
<!--			</if>-->
<!--		</where>-->
<!--	</select>-->
</mapper>