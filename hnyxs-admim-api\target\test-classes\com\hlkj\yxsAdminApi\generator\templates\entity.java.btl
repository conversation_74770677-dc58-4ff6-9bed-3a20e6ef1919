package ${package.Entity};

<% for(pkg in table.importPackages) { %>
import ${pkg};
<% } %>
<% if(swagger2) { %>
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
<% } %>
<% if(entityLombokModel) { %>
import lombok.Data;
import lombok.EqualsAndHashCode;
    <% if(chainModel) { %>
import lombok.experimental.Accessors;
    <% } %>
<% } %>

/**
 * ${table.comment!}
 *
 * <AUTHOR>
 * @since ${date(), 'yyyy-MM-dd HH:mm:ss'}
 */
<% if(entityLombokModel) { %>
@Data
    <% if(isNotEmpty(superEntityClass)) { %>
@EqualsAndHashCode(callSuper = true)
    <% } else { %>
@EqualsAndHashCode(callSuper = false)
    <% } %>
    <% if(chainModel) { %>
@Accessors(chain = true)
    <% } %>
<% } %>
<% if(swagger2) { %>
@ApiModel(value = "${entity}对象", description = "${table.comment!''}")
<% } %>
<% if(table.convert) { %>
@TableName("${table.name}")
<% } %>
<% if(isNotEmpty(superEntityClass)) { %>
public class ${entity} extends ${superEntityClass}<% if(activeRecord) { %><${entity}><% } %>{
<% } else if(activeRecord) { %>
public class ${entity} extends Model<${entity}> {
<% } else { %>
public class ${entity} implements Serializable {
<% } %>
<% if(entitySerialVersionUID) { %>
    private static final long serialVersionUID = 1L;
<% } %>
<% /** -----------BEGIN 字段循环遍历----------- **/ %>
<% for(field in table.fields) { %>
    <%
    var keyPropertyName;
    if(field.keyFlag) {
        keyPropertyName = field.propertyName;
    }
    %>

    <% if(isNotEmpty(field.comment)) { %>
        <% if(swagger2) { %>
    @ApiModelProperty(value = "${field.comment}")
        <% }else{ %>
    /**
     * ${field.comment}
     */
        <% } %>
    <% } %>
    <% /* 主键 */ %>
    <% if(field.keyFlag) { %>
        <% if(field.keyIdentityFlag) { %>
    @TableId(value = "${field.annotationColumnName}", type = IdType.AUTO)
        <% } else if(isNotEmpty(idType)) { %>
    @TableId(value = "${field.annotationColumnName}", type = IdType.${idType})
        <% } else if(field.convert) { %>
    @TableId("${field.annotationColumnName}")
        <% } %>
    <% /* 普通字段 */ %>
    <% } else if(isNotEmpty(field.fill)) { %>
        <% if(field.convert){ %>
    @TableField(value = "${field.annotationColumnName}", fill = FieldFill.${field.fill})
        <% }else{ %>
    @TableField(fill = FieldFill.${field.fill})
        <% } %>
    <% } else if(field.convert) { %>
    @TableField("${field.annotationColumnName}")
    <% } %>
    <% /* 乐观锁注解 */ %>
    <% if(versionFieldName!'' == field.name) { %>
    @Version
    <% } %>
    <% /* 逻辑删除注解 */ %>
    <% if(logicDeleteFieldName!'' == field.name) { %>
    @TableLogic
    <% } %>
    private ${field.propertyType} ${field.propertyName};
<% } %>
<% /** -----------END 字段循环遍历----------- **/ %>

<% if(!entityLombokModel) { %>
    <% for(field in table.fields) { %>
        <%
        var getprefix = '';
        if(field.propertyType == 'boolean') {
            getprefix = 'is';
        } else {
            getprefix = 'get';
        }
        %>
    public ${field.propertyType} ${getprefix}${field.capitalName}() {
        return ${field.propertyName};
    }

        <% if(chainModel) { %>
    public ${entity} set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
        <% } else { %>
    public void set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
        <% } %>
        this.${field.propertyName} = ${field.propertyName};
        <% if(chainModel){ %>
        return this;
        <% } %>
    }

    <% } %>
<% } %>
<% if(entityColumnConstant) { %>
    <% for(field in table.fields) { %>
    public static final String ${strutil.toUpperCase(field.name)} = "${field.name}";

    <% } %>
<% } %>
<% if(activeRecord) { %>
    @Override
    protected Serializable pkVal() {
        <% if(isNotEmpty(keyPropertyName)){ %>
        return this.${keyPropertyName};
        <% }else{ %>
        return null;
        <% } %>
    }

<% } %>
<% if(!entityLombokModel){ %>
    @Override
    public String toString() {
        return "${entity}{" +
        <% for(field in table.fields){ %>
            <% if(fieldLP.index==0){ %>
        "${field.propertyName}=" + ${field.propertyName} +
            <% }else{ %>
        ", ${field.propertyName}=" + ${field.propertyName} +
            <% } %>
        <% } %>
        "}";
    }
<% } %>
}
