<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsTagRefMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_tag_ref a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsTagId != null">
                AND a.GOODS_TAG_ID = #{param.goodsTagId}
            </if>
            <if test="param.goodsDetailsId != null">
                AND a.GOODS_DETAILS_ID = #{param.goodsDetailsId}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsTagRef">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsTagRef">
        <include refid="selectSql"></include>
    </select>

</mapper>
