<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppDedicatedCircuitInvoiceMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_dedicated_circuit_invoice a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.dedicatedCircuitType != null">
                AND a.DEDICATED_CIRCUIT_TYPE = #{param.dedicatedCircuitType}
            </if>
            <if test="param.dedicatedCircuitName != null">
                AND a.DEDICATED_CIRCUIT_NAME LIKE CONCAT('%', #{param.dedicatedCircuitName}, '%')
            </if>
            <if test="param.offerId != null">
                AND a.OFFER_ID LIKE CONCAT('%', #{param.offerId}, '%')
            </if>
            <if test="param.offerName != null">
                AND a.OFFER_NAME LIKE CONCAT('%', #{param.offerName}, '%')
            </if>
            <if test="param.packageLevel != null">
                AND a.PACKAGE_LEVEL = #{param.packageLevel}
            </if>
            <if test="param.duration != null">
                AND a.DURATION LIKE CONCAT('%', #{param.duration}, '%')
            </if>
            <if test="param.minDeDuction != null">
                AND a.MIN_DE_DUCTION LIKE CONCAT('%', #{param.minDeDuction}, '%')
            </if>
            <if test="param.preStorage != null">
                AND a.PRE_STORAGE LIKE CONCAT('%', #{param.preStorage}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.UPDATE_DATE LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitInvoice">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitInvoice">
        <include refid="selectSql"></include>
    </select>

</mapper>
