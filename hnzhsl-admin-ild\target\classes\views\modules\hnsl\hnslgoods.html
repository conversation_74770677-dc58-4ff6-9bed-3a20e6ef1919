<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<link rel="stylesheet" href="../../css/goods.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
	<style type="text/css">

		.templateShow-Info {
			float: left;
			width: 100%;
			font-size: 20px;
			padding: 10px 25px 0;
		}

		.templateShow-Info p {
			font-size: 20px;
			float: left;
			text-align: center;
			margin: 10px 0 10px;
			margin: 10px 0 10px;
		}

		.templateShow-Info p:nth-child(2) {
			color: #999 !important;
			font-size: 16px;
			line-height: 28px;
		}
	</style>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">
		<!-- 条件查询 -->
		<div class="row">
				<div class="form-group col-md-2" style="height:40px">
				  <label>商品名称</label>
				 <input type="text" class="form-control" placeholder="商品名称" v-model="seachGoods.goodsName"/>
				 </div>

			   <div class="form-group col-md-2" style="height:40px">
				  <label>商品编码</label>
				 <input type="text" class="form-control" placeholder="商品编码"  v-model="seachGoods.goodsNumber" />
			   </div>

				<div class="form-group col-md-2">
					<label>商品类型</label>
					<select class="form-control" style="height: 32px;" v-model="seachGoods.goodsType">
					  <option value='' >全部</option>
					  <option value='1' >号卡新装</option>
					  <option value='2' >一人一码</option>
                      <option value='3' >销售品加装</option>
						<option value='rh' >融合新装</option>
						<option value='4' >公众存费</option>
                    </select>
				</div>

				<div class="form-group col-md-2">
					<label>主套餐编码</label>
					<input type="text" style="height: 32px;" class="form-control" placeholder="主套餐编码" v-model="seachGoods.goodsMianNumber"/>
				</div>

				<div class="form-group col-md-2">
					<label>所属学校</label>
					<input type="text" style="height: 32px;" class="form-control" placeholder="输入学校名称" v-model="seachGoods.schoolName"/>
					<!--<select  id="prov" name="prov" class="form-control" style="height: 32px;" v-model="seachGoods.schoolCode" >
					  <option value='' >全部</option>
					  <option>学校1</option>
                     </select>-->
				</div>
				<div class="form-group col-md-2">
					<label>状态</label>
					<select class="form-control" style="height: 32px;" v-model="seachGoods.status">
					  <option value='' >全部</option>
					  <option value="1">在架</option>
					  <option value="0">下架</option>
					</select>
				</div>

				<div class="row2">
					<div class="form-group col-md-2" style="height: 32px;">
						<label>创建日期:</label>
						<div class="input-group col-ms-2 ">
							<input class="form-control pull-left dateRange date-picker "
								id="dateTimeRange" @keyup.enter="query" value="" type="text"
								placeholder="创建日期"> <span class="input-group-addon">
								<i class="fa fa-calendar bigger-110"></i>
							</span>
							 <input name="beginTime" id="beginTime" type="hidden" >
							 <input name="endTime" id="endTime" type="hidden">
 						</div>
				  </div>
					<div class="form-group col-md-2">
						<label>商品子类型</label>
						<select class="form-control" style="height: 32px;" v-model="seachGoods.goodsChildtypeNumber">
							<option value='' >全部</option>
							<option value='1' >促销包节点</option>
							<option value='2' >批量加装</option>
							<option value='3' >可选包节点</option>
							<option value='6' >毕业生宽带</option>
						</select>
					</div>
					<div class="form-group col-md-2"  style="height:43px">
						<label>所属地市:</label>
						<select class="form-control" style="height: 32px;" v-model="seachGoods.cityCode">
							<option value=''>全部</option>
							<option v-for="itme in city" v-bind:value="itme.cityCode">
								{{itme.cityName}}
							</option>
						</select>
					</div>
			    </div>
			<a v-if="hasPermission('hnsdgoods:query')" class="btn btn-primary"  @click="query" style="margin-left:1%">&nbsp;查询</a>
			<a v-if="hasPermission('hnsdgoods:save')" class="btn btn-primary"  @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
			<a v-if="hasPermission('hnsluser:adminUpdate')" class="btn btn-primary" @click="templateShow1('1')">&nbsp;熟卡积分导入</a>
			<a v-if="hasPermission('hnsluser:adminUpdate')" class="btn btn-primary" @click="templateShow1('2')">&nbsp;加装限制编码导入</a>
			<a v-if="hasPermission('hnsluser:adminUpdate')" class="btn btn-primary" @click="exportGoodsRecord()">&nbsp;商品分析导出</a>
			<!--<a v-if="hasPermission('hnsdgoods:update')" class="btn btn-primary" @click="update" ><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a>-->
	</div>

	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>

    <div v-show="showInfo" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">

        <table class="textTable">
	        <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;产品基本信息</span>
	        <tr>
				<td class="leftTd"><label><span>商品类型：</span></label></td>
				<td style="width: 30%;">
					<select class="form-control" v-model="hnslGoods.saflType" style="height: 32px;"
							@change="selectGoods()">
					  <option value='null'>请选择</option>
					  <option value='1' >号卡新装</option>
					  <option value='2' >一人一码</option>
					  <option value='3' >销售品加装</option>
                      <option value='9' >融合新装</option>
						<option value='4' >公众存费</option>
					</select>
				</td>
				 <td class="leftTd"><label><span>产品名称：</span></label></td>
				<td style="width: 46%">
					<input type="text" v-model="hnslGoods.goodsName"  />
				</td>
			</tr>
			<tr>
				<td class="leftTd" v-if="hasPermission('hnsluser:adminUpdate')"><label><span>套餐类型：</span></label></td>
				<td style="width: 30%;" v-if="hasPermission('hnsluser:adminUpdate')">
					<select class="form-control" autocomplete="off" v-model="hnslGoods.goodsPackageType" style="height: 32px;"
							@change="selectGoodsPackage()">
						<option v-for="itme in hnslChannelList" v-bind:value="itme.value">
							{{itme.name}}
						</option>
<!--						<option value='1' >校园渠道</option>-->
<!--						<option value='2' >电渠互联网卡渠道</option>-->
<!--						<option value='3' >其他渠道</option>-->
					</select>
				</td>
				<td class="leftTd" v-if="tcBool"><label><span>CPS：</span></label></td>
				<td style="width: 46%" v-if="tcBool">
					<select class="form-control" v-model="hnslGoods.cpsList" autocomplete="off" style="height: 32px;">
						<option value='' selected>请选择</option>
						<option v-for="itme in hnslgoodsCpsList" v-bind:value="itme.cpsNumber">
							{{itme.cpsName+'-'+itme.cpsNumber}}
						</option>
					</select>
				</td>
			</tr>
            <tr>
                <td class="leftTd"><label><span>产品编码：</span></label></td>
				<td>
						<input type="text" v-model="hnslGoods.goodsNumber" id="goodsNumber"/>
				</td>
				<td  class="leftTd"><label><span>主套餐销售品编码：</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.goodsMianNumber"   />
				</td>
            </tr>
            <tr>
				<td v-if="astrictBool" class="leftTd"><label><span>加装类型:</span></label></td>
				<td v-if="astrictBool" style="width: 30%;">
					<select class="form-control" v-model="hnslGoods.goodsType" style="height: 32px;">
						<option value=''>请选择</option>
						<option value='1' >权益销售品</option>
						<option value='2' >预存销售品</option>
						<option value='3' >续费销售品</option>
						<option value='4' >流量销售品</option>
						<option value='5' >宽带销售品</option>
						<option value='6' >语音销售品</option>
						<option value='7' >增值销售品</option>
					</select>
				</td>
                <td v-if="newNumberBool" class="leftTd"><label><span>预存款：</span></label></td>
				<td v-if="newNumberBool">
					<input type="text" v-model="hnslGoods.prestore"/>
				</td>
				<td class="leftTd"><label><span>商品积分:</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.goodsIntegral" placeholder="只支持数字"
						   onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();"/>
				</td>
			</tr>
			<tr v-if="newNumberBool">
			    <td class="leftTd"><label><span>制卡费：</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.productionPrice"  />
				</td>
			     <td class="leftTd"><label><span>保底消费：</span></label></td>
				 <td>
					<input type="text" v-model="hnslGoods.minPrice"  />
				 </td>
			</tr>
			<tr>
				<td class="leftTd"><label><span>带宽：</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.bandwidth" placeholder="只支持数字"
						   onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();"/>
				</td>
				<td v-if="newNumberBool" class="leftTd"><label><span>带宽销售品编码：</span></label></td>
				<td v-if="newNumberBool ">
					<input type="text" v-model="hnslGoods.minPrice9"  placeholder="输入带宽销售品编码"/>
					<!--<input type="text" v-model="hnslGoods.minPrice9"  placeholder="点击添加多个销售品编码" @click="number_show('minPrice9')"   />-->
				</td>
			</tr>
			<tr>
			    <td class="leftTd"><label><span>可选包销售品编码:</span></label></td>
				 <td>
					<input type="text" v-model="hnslGoods.minPrice1"    placeholder="点击添加多个销售品编码" @click="number_show('minPrice1')" />
				 </td>
				 <td class="leftTd"><label><span>宽带可选包编码:</span></label></td>
				 <td>
					<input type="text" v-model="hnslGoods.minPrice2"  placeholder="点击添加多个销售品编码" @click="number_show('minPrice2')" />
				 </td>
			</tr>
			<tr>
			     <td class="leftTd"><label><span>叠加包销售品编码:</span></label></td>
				 <td>
					<input type="text" v-model="hnslGoods.minPrice3"  placeholder="点击添加多个销售品编码" @click="number_show('minPrice3')"/>
				 </td>
				<td class="leftTd"><label><span>X元预存</span></label></td>
				<td>
					<input type="text" style="width:130px" v-model="hnslGoods.minPrice8" placeholder="预存编码" /> <input type="text" style="width:180px" v-model="hnslGoods.text8"  placeholder="预存说明" />
				</td>
				 <!--<td class="leftTd"><label><span>ITV销售品编码:</span></label></td>
				 <td>
					<input type="text" v-model="hnslGoods.minPrice4" placeholder=""/>
				 </td>-->
			</tr>
			<tr v-if="newNumberBool">
				<td v-if="newNumberBool" class="leftTd"><label><span>基础包销售品编码:</span></label></td>
				<td v-if="newNumberBool">
					<input type="text" v-model="hnslGoods.minPrice10"  placeholder="多个用逗号隔开"/>
				</td>
				<td class="leftTd"><label><span>300元预存</span></label></td>
				<td>
					<input type="text" style="width:130px" v-model="hnslGoods.minPrice7"  placeholder="预存编码" /> <input type="text" style="width:180px" v-model="hnslGoods.text7" placeholder="预存说明" />
				</td>
			</tr>
			<tr v-if="newNumberBool">
			    <td class="leftTd"><label><span>100元预存:</span></label></td>
				  <td>
					   <input type="text" style="width:130px" v-model="hnslGoods.minPrice5" placeholder="预存编码" /> <input type="text" style="width:180px" v-model="hnslGoods.text5" placeholder="预存说明" />
				 </td>
			    <td class="leftTd"><label><span>200元预存:</span></label></td>
				 <td>
					   <input type="text" style="width:130px" v-model="hnslGoods.minPrice6"  placeholder="预存编码" /> <input type="text" style="width:180px" v-model="hnslGoods.text6" placeholder="预存说明" />
				 </td>
			</tr>
			<tr v-if="astrictBool">
				<td v-if="astrictBool" class="leftTd"><label><span>限制销售品编码:</span></label></td>
				<td v-if="astrictBool">
					<input type="text" v-model="hnslGoods.astrictGoods" @click="number_show('astrictGoods')"  placeholder="点击添加多个销售品编码"  readonly="readonly"/>
				</td>
				<td v-if="astrictBool" class="leftTd"><label><span>预存包价格:</span></label></td>
				<td v-if="astrictBool">
					<input type="number" v-model="hnslGoods.goodsPrice" value="0"  placeholder="默认为0元" />
				</td>
			      <!-- <td class="leftTd"><label><span>X元预存</span></label></td>
				   <td>
					   <input type="text" style="width:130px" v-model="hnslGoods.minPrice8" placeholder="预存编码" /> <input type="text" style="width:180px" v-model="hnslGoods.text8"  placeholder="预存说明" />
				   </td>-->
			</tr>
			<tr>
			      <td class="leftTd"><label><span>所属学校:</span></label></td>
				   <td>
					<input type="text" v-model="hnslGoods.schoolName" />
					 <a  v-show="bianji1" class="btn btn-primary" @click="school_show()" style="float: left; margin-left: 4px; ">编辑</a>
					 <a  v-show="bianji2" class="btn btn-primary" @click="school_close()" style="float: left; margin-left: 4px;">确定</a>
					 <a  v-show="bianji2" class="btn btn-warning" @click="school_back()" style="float: left; margin-left: 4px;">取消</a>
				   </td>
				   <td class="leftTd"><label><span>可办理数量:</span></label></td>
				 <td>
					 <select class="form-control"  v-model="hnslGoods.transactionNum" style="height: 32px;" >
					    <option value='' >不限制</option>
					    <option value='1' >1</option>
					    <option value='2' >2</option>
					    <option value='3' >3</option>
					    <option value='4' >4</option>
					    <option value='5' >5</option>
					</select>
				 </td>
			</tr>
			<tr>
				<td class="leftTd"><label><span>最小年龄限制:</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.minage" placeholder="只支持正整数"
						   onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9]/g,'');}).call(this)" onblur="this.v();"/>
				</td>
				<td class="leftTd"><label><span>最大年龄限制:</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.maxage" placeholder="只支持正整数"
						   onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9]/g,'');}).call(this)" onblur="this.v();"/>
				</td>
			</tr>
			<tr>
				<td class="leftTd"><label><span>加装业务说明:</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.goodsExplain" placeholder="请输入需要展示加装套装业务说明" />
				</td>
				<td class="leftTd"><label><span>商品子类型:</span></label></td>
				<td>
					<select class="form-control" v-model="hnslGoods.goodsChildtypeNumber" style="height: 32px;">
						<option value='1' >促销包节点</option>
						<option value='2' >批量加装</option>
						<option value='3' >可选包节点</option>
						<option value='6' >毕业生宽带</option>
						<!--<option value='4' >可重复加装类型</option>
						<option value='5' >促销包类型</option>-->
					</select>
				</td>
			</tr>
			<tr>
				<td class="leftTd"><label><span>翼店展示:</span></label></td>
				<td>
					<select class="form-control" v-model="hnslGoods.goodsTypeShow" style="height: 32px;">
						<option value='0' >默认不展示</option>
						<option value='1' >正常展示</option>
					</select>
				</td>
				<td v-if="!astrictBool" class="leftTd"><label><span>业务类型:</span></label></td>
				<td v-if="!astrictBool" style="width: 30%;">
					<select class="form-control" v-model="hnslGoods.goodsType" style="height: 32px;">
						<option value=''>请选择</option>
						<option value='1' >新类型</option>
						<option value='2' >老类型</option>
					</select>
				</td>
			</tr>
			<tr>
				<td class="leftTd"><label><span>H5链接:</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.goodsHtmlUrl" placeholder="请输入跳转的地址URL"/>
				</td>
				<td class="leftTd"><label><span>BPS产品编码:</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.bpsGoodsNumber" placeholder="请输入BPS产品编码"/>
				</td>
			</tr>
			<tr v-show="internetShow">
				<td class="leftTd"><label><span>分享展示:</span></label></td>
				<td>
					<select class="form-control" v-model="hnslGoods.goodsShareSwitch" autocomplete="off" style="height: 32px;">
						<option value='2' selected="selected">否</option>
						<option value='1'>是</option>
					</select>
				</td>
				<td class="leftTd"><label><span>受理流程类型:</span></label></td>
				<td>
					<select class="form-control" v-model="hnslGoods.acceptanceProcessType" style="height: 32px;">
						<option value='1' >省内CRM3.0</option>
						<option value='2' >BPS集团白卡</option>
						<option value='3' >自主受理</option>
					</select>
				</td>
			</tr>
			<tr v-show="internetShow">
				<td class="leftTd"><label><span>副卡加装数量:</span></label></td>
				<td>
					<input type="text" v-model="hnslGoods.subcardQuantity" placeholder="请输入副卡加装数量"/>
				</td>
				<td class="leftTd"><label><span>展示上传证件照:</span></label></td>
				<td>
					<select class="form-control" v-model="hnslGoods.certificateSwitch" autocomplete="off" style="height: 32px;">
						<option value='0' selected="selected">否</option>
						<option value='1'>是</option>
					</select>
				</td>
			</tr>
			<tr v-show="internetShow">
				<td class="leftTd"><label><span>企业信息是否填写:</span></label></td>
				<td>
					<select class="form-control" v-model="hnslGoods.guild" style="height: 32px;">
						<option value='2' >不填写</option>
						<option value='1' >填写</option>
					</select>
				</td>
				<td class="leftTd" v-show="hnslGoods.certificateSwitch === '1' || hnslGoods.certificateSwitch === 1"><label><span>必传证件照数量:</span></label></td>
				<td v-show="hnslGoods.certificateSwitch === '1' || hnslGoods.certificateSwitch === 1">
					<select class="form-control" v-model="hnslGoods.workProveNum" autocomplete="off" style="height: 32px;">
						<option value='1' selected="selected">1</option>
						<option value='2' >2</option>
						<option value='3' >3</option>
					</select>
				</td>
			</tr>
      	</table>

      	<div v-show="schoolBelongShow">
      	<!-- 所属团队树 -->
      	<table class="textTable">
	        <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;</span>
	        <div class="tree_pag">
				<!--<h5>商品对应学校</h5>-->
				<div class="tree_content" style="height:50px;">
					<h5 style="height: 30px;float: left;width:250px;position: relative;overflow: auto;">商品对应学校</h5>
					<div style="height: 40px;float: right;width: 450px;position: relative;overflow: auto;">
						<input type="text" style="width:150px" v-model="schoolName"  placeholder="输入名称" />
						<a v-if="hasPermission('hnsdgoods:query')" class="btn btn-primary"  @click="querySchool" style="margin-left:1%">&nbsp;查询</a>
						<a v-if="hasPermission('hnsdgoods:query')" class="btn btn-primary"  @click="addAll" style="margin-left:1%">&nbsp;全部添加</a>
						<a v-if="hasPermission('hnsdgoods:query')" class="btn btn-primary"  @click="delectAll" style="margin-left:1%">&nbsp;全部取消</a>
					</div>
				</div>

			<div class="tree_content">
				<!--左边树状图-->
				<div class="left_tree" >
					<div class="treebox scrollXY">
						<div class="tree">
							<ul >
								<li class="main" v-for="(teamTree , index) in teamTreeRight"  @dblclick="addCity(teamTree.cityName,index)" v-show="teamTree.team.length!=0"> <a @click="openBuildingLeft(teamTree.cityCode)">{{teamTree.cityName}}</a>
									<ul :id="teamTree.cityCode+'left'" style="display:none">
										<li v-for="(itme , indexs ) in teamTree.team"><a @click="addTeam(index,indexs,itme.schoolCode,itme.schoolName)">{{itme.schoolName}}</a></li>
									</ul>
								</li>
							</ul>
						</div>
					</div>
				</div>

				<!--右边树状图-->
				<div class="right_tree">
					<div class="treebox scrollXY">
						<div class="tree">
							<ul>
								<li class="main" v-for="(teamTrees , index) in teamTreeLeft" @dblclick="delCity(index)"  v-show="teamTrees.team.length!=0"> <a @click="openBuildingRight(teamTrees.cityCode)">{{teamTrees.cityName}}</a>
									<ul :id="teamTrees.cityCode+'right'" style="display:none">
										<li v-for="(itme , indexs) in teamTrees.team"><a @click="delTeam(index,indexs)">{{itme.schoolName}}</a></li>
									</ul>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
	</div>
	    </table>
	    </div>
			<!-- 产品宣传 -->
	    <table class="textTable">
	         <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;产品宣传</span>
			<tr v-if="hbBool">
				<td class="leftTd" style="height:150px;"><label><span>分享海报:</span></label></td>
				<td>
					<img :src="url+imgUrl3" class="fileUp" alt="" id="hb" height="100" width="100" @click="delectImg(0)" />
					<input class="fileUp" type="file" @change="upload('3')" accept="image/*" id="file3" value="" />
				</td>
				<td>
					<img :src="url+imgUrl4" class="fileUp" alt="" id="hb1" height="100" width="100" @click="delectImg(1)" />
					<input class="fileUp" type="file" @change="upload('4')" accept="image/*" id="file4" value="" />
				</td>
				<td>
					<img :src="url+imgUrl5" class="fileUp" alt="" id="hb2" height="100" width="100" @click="delectImg(2)" />
					<input class="fileUp" type="file" @change="upload('5')" accept="image/*" id="file5" value="" />
				</td>
			</tr>
			<tr v-if="kfBool">
				<td class="leftTd" style="height:150px;"><label><span>专属客服二维码:</span></label></td>
				<td>
					<img :src="url+imgUrl6" class="fileUp" alt="" id="qr" height="100" width="100" @click="delectImg(3)" />
					<input class="fileUp" type="file" @change="upload('6')" accept="image/*" id="file6" value="" />
				</td>
			</tr>
	         <tr>
			    <td class="leftTd" style="height:150px;"><label><span>橱窗图:</span></label></td>
				 <td>
					<!--<a class="fileUp" style="width: 100px; height: 100px;">上传
					</a>-->
					 <img :src="url+imgUrl1" class="fileUp" alt="" id="zm" height="100" width="100" />
					 <input class="fileUp" type="file" @change="upload('1')" accept="image/*" id="file1" value="" />
				 </td>
			</tr>
			<tr>
			    <td class="leftTd" style="height:150px;"><label><span>套餐详情图:</span></label></td>
				 <td>
					<!--<a class="fileUp" style="width: 100px; height: 100px;">上传
					</a>-->
					 <img :src="url+imgUrl2" class="fileUp" alt="" id="dm" height="100" width="100" />
					 <input class="fileUp" type="file" @change="upload('2')" accept="image/*" id="file2" value="" />
				 </td>
			</tr>
			<tr>
				<td class="leftTd" style="height:150px;"><label><span>套餐分享图:</span></label></td>
				<td>
					<!--<a class="fileUp" style="width: 100px; height: 100px;">上传
					</a>-->
					<img :src="url+imgUrl7" class="fileUp" alt="" id="fx" height="100" width="100" />
					<input class="fileUp" type="file" @change="upload('7')" accept="image/*" id="file7" value="" />
				</td>
			</tr>
			<tr>
			    <td class="leftTd" style="height:150px;"><label><span>商品描述:</span></label></td>
				 <td>
					 <textarea style="height:150px;" v-model="hnslGoods.goodsDetalt"  ></textarea>
				 </td>
			</tr>
			<tr>
			    <td class="leftTd" style="height:150px;"><label><span>备注:</span></label></td>
				 <td>
					<textarea style="height:150px;" v-model="hnslGoods.remark" ></textarea>
				 </td>
			</tr>

			<tr>
			     <td class="leftTd"><label><span>排序:</span></label></td>
			     <td>
					<input type="text" value=""  v-model="hnslGoods.orderNumber" placeholder="数字越大排序越靠前" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();" />
				 </td>
			</tr>
	    </table>
			<div style="text-align: center; width: 1225px">
				<input type="button" class="btn btn-primary" v-show="sureShow" @click="saveOrUpdate" value="确定"/>
				<input type="button" class="btn btn-warning" @click="reload" value="返回" />
			</div>

		</form>

	</div>

	<!--2019-12-31 -->
	<div class="fadeBox" id="tcBox" v-show="numberShow">
		<div class="dao_pag package">
			<img src="https://lst.hn.189.cn/hnzhsl/uploads/picture/goods/close_1.png" class="close_1" @click="tc_hide('tcBox')" alt="" />
			<h5>销售品编码</h5>
			<div class="layui-form mt20 inpBox inp-flex-box">
				<div class="layui-form-item inp_pag">
					<label  class="layui-form-label">编码：</label>
					<div class="layui-input-inline" style="width: 150px;">
						<input type="text" id="number1" placeholder="请输入编码" class="layui-input-s">
					</div>
<!--					<input type="checkbox" id="vehicle1"  /> 校内流量-->
<!--					<input type="checkbox" id="domain1"  /> 双域快网-->
					<select class="form-control" id="vehicle1" @change="handleSelectChange()">
						<option value='0' >无</option>
						<option value='1' >校内流量</option>
						<option value='2' >双域快网</option>
						<option value='3' >预存包</option>
						<option value='4' v-show="minCheckShow">本地宽带包</option>
						<option value='5' v-show="minCheckShow">异地宽带包</option>
						<option value='6' v-show="minPrice2Show">光猫代管包</option>
						<option value='7' v-show="minPre1Package || minPre3Package">强充预存包</option>
					</select>
					<img src="https://lst.hn.189.cn/hnzhsl/uploads/picture/goods/add_img.png"class="add_img" @click="add_inp" />
					<div class="layui-input-inline-price" v-show="minPrice2Show">
						<input type="text" id="goodsRelPrice1" class="layui-input-s">
					</div>
					<label  class="layui-form-label-price" v-show="minPrice2Show">元</label>

					<div v-show="pre1Package" style="display: flex; align-items: center;">
						<label  class="layui-form-label-price" style="margin: 10px;font-weight: 400; line-height: 20px; text-align: right;">存</label>
						<div class="layui-input-inline-price" style="width: 100px">
							<input type="text" id="goodsRelPre1Package1" class="layui-input-s">
						</div>
						<label  class="layui-form-label-price" style="margin: 10px;font-weight: 400; line-height: 20px; text-align: right;">送</label>
						<div class="layui-input-inline-price" style="width: 100px">
							<input type="text" id="goodsRelPre3Package1" class="layui-input-s">
						</div>
					</div>

					<div v-show="pre2Package" style="display: flex; align-items: center;">
						<label  class="layui-form-label-price" style="margin: 10px;font-weight: 400; line-height: 20px; text-align: right;">存</label>
						<div class="layui-input-inline-price" style="width: 100px">
							<input type="text" id="goodsRelPre2Package1" class="layui-input-s">
						</div>
						<label  class="layui-form-label-price" style="margin: 10px;font-weight: 400; line-height: 20px; text-align: right;">送</label>
						<div class="layui-input-inline-price" style="width: 100px">
							<input type="text" id="goodsRelPre5Package1" class="layui-input-s">
						</div>
					</div>
				</div>
			</div>
			<div class="layui-form-item inpBtn">
				<button class="layui-btn" @click="bm" >确定</button>
				<button @click="tc_hide('tcBox')"  class="layui-btn">取消</button>
			</div>
		</div>
	</div>

	<!--模板下载-->
	<div v-show="!templateShow" id="templateShow"
		 class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<div class="form-horizontal" style="padding-top: 0px; width: 100%;">
			<form id="uploadImg" enctype="multipart/form-data">
				<div class="templateShow-Info">
					<p>下载模板：</p>
					<p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
				</div>
				<div style="margin-left: 125px;">
					<a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
				</div>
				<div class="templateShow-Info">
					<p>上传文件：</p>
					<p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
				</div>
				<div style="margin-left: 125px;">
					<a v-if="hasPermission('hnsduser:importUser')"
					   class="btn btn-primary" @click="importUser">&nbsp;开始导入</a> <input
						style="display: none;" name="uploadFile" id="uploadFile"
						type="file" @change="uploadFile" />
				</div>
				<div style="width: 100%; text-align: center;">
					<input type="button" class="btn btn-warning" @click="reload" value="返回" />
				</div>
			</form>
		</div>
	</div>

</div>
<script  src="../../js/modules/hnsl/hnslgoods.js" ></script>
<script src="../../js/components.js"></script>
</body>
</html>