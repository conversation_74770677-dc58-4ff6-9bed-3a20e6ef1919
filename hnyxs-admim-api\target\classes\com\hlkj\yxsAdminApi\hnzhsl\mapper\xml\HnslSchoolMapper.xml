<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslSchoolMapper">

    <resultMap type="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchool" id="hnslSchoolMap">
        <result property="id" column="ID"/>
        <result property="schoolRegister" column="SCHOOL_REGISTER"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="schoolNetwork" column="SCHOOL_NETWORK"/>
        <result property="updatedUser" column="UPDATED_USER"/>
        <result property="schoolName" column="SCHOOL_NAME"/>
        <result property="schoolRegisterExisting" column="SCHOOL_REGISTER_EXISTING"/>
        <result property="createdUser" column="CREATED_USER"/>
        <result property="houseNumber" column="HOUSE_NUMBER"/>
        <result property="schoolNetworkExisting" column="SCHOOL_NETWORK_EXISTING"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="userId" column="USER_ID"/>
        <result property="status" column="STATUS"/>
        <result property="schoolCode" column="SCHOOL_CODE"/>
        <collection property="hnslBuilding" column="SCHOOL_CODE" select="queryListBulidingBySchoolCode" />
    </resultMap>

    <resultMap type="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBuilding" id="hnslBuildingMap">
        <result property="id" column="ID"/>
        <result property="buildingNumber" column="BUILDING_NUMBER"/>
        <result property="buildingRegisterExisting" column="BUILDING_REGISTER_EXISTING"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="buildingName" column="BUILDING_NAME"/>
        <result property="buildingTier" column="BUILDING_TIER"/>
        <result property="buildingRoom" column="BUILDING_ROOM"/>
        <result property="updatedUser" column="UPDATED_USER"/>
        <result property="networkPhoneExisting" column="NETWORK_PHONE_EXISTING"/>
        <result property="createdUser" column="CREATED_USER"/>
        <result property="networkExisting" column="NETWORK_EXISTING"/>
        <result property="buildingId" column="BUILDING_ID"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="userId" column="USER_ID"/>
        <result property="status" column="STATUS"/>
        <result property="schoolId" column="SCHOOL_ID"/>
    </resultMap>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_school a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.schoolName != null">
                AND a.SCHOOL_NAME LIKE CONCAT('%', #{param.schoolName}, '%')
            </if>
            <if test="param.schoolRegister != null">
                AND a.SCHOOL_REGISTER = #{param.schoolRegister}
            </if>
            <if test="param.schoolRegisterExisting != null">
                AND a.SCHOOL_REGISTER_EXISTING = #{param.schoolRegisterExisting}
            </if>
            <if test="param.schoolNetwork != null">
                AND a.SCHOOL_NETWORK = #{param.schoolNetwork}
            </if>
            <if test="param.schoolNetworkExisting != null">
                AND a.SCHOOL_NETWORK_EXISTING = #{param.schoolNetworkExisting}
            </if>
            <if test="param.houseNumber != null">
                AND a.HOUSE_NUMBER = #{param.houseNumber}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.userId != null">
                AND a.USER_ID = #{param.userId}
            </if>
            <if test="param.schoolCity != null">
                AND a.SCHOOL_CITY LIKE CONCAT('%', #{param.schoolCity}, '%')
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.schoolImage != null">
                AND a.SCHOOL_IMAGE LIKE CONCAT('%', #{param.schoolImage}, '%')
            </if>
            <if test="param.schoolType != null">
                AND a.SCHOOL_TYPE = #{param.schoolType}
            </if>
            <if test="param.upSchool != null">
                AND a.UP_SCHOOL LIKE CONCAT('%', #{param.upSchool}, '%')
            </if>
            <if test="param.crmCode != null">
                AND a.CRM_CODE LIKE CONCAT('%', #{param.crmCode}, '%')
            </if>
            <if test="param.schoolFlowCode != null">
                AND a.SCHOOL_FLOW_CODE LIKE CONCAT('%', #{param.schoolFlowCode}, '%')
            </if>
            <if test="param.schoolVariety != null">
                AND a.SCHOOL_VARIETY = #{param.schoolVariety}
            </if>
            <if test="param.schoolQrcode != null">
                AND a.SCHOOL_QRCODE = #{param.schoolQrcode}
            </if>
            <if test="param.schoolSixId != null">
                AND a.SCHOOL_SIX_ID LIKE CONCAT('%', #{param.schoolSixId}, '%')
            </if>
            <if test="param.schoolGradeType != null">
                AND a.SCHOOL_GRADE_TYPE = #{param.schoolGradeType}
            </if>
            <if test="param.schoolServiceUrl != null">
                AND a.SCHOOL_SERVICE_URL LIKE CONCAT('%', #{param.schoolServiceUrl}, '%')
            </if>
            <if test="param.schoolModuleType != null">
                AND a.SCHOOL_MODULE_TYPE = #{param.schoolModuleType}
            </if>
            <if test="param.schoolImageUploadType != null">
                AND a.SCHOOL_IMAGE_UPLOAD_TYPE = #{param.schoolImageUploadType}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchool">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchool">
        <include refid="selectSql"></include>
    </select>

    <select id="queryTaskUserDetailsByTaskCode" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchoolUser">
        select b.*,
        (select BUILDING_NAME from HNSL_BUILDING where BUILDING_ID=b.BUILDING_ID ) as buildingName,
        (select ROOM_NAME from HNSL_ROOM where ROOM_NUMBER=b.ROOM_ID ) as roomName,
        (select c.USER_NAME from HNSL_BUILDING a left join HNSL_USER c on a.USER_ID=c.USER_PHONE  where a.BUILDING_ID=b.BUILDING_ID ) as mangenName
        from HNSL_TASK_PLAN a left join HNSL_SCHOOL_USER b  on a.client_number=b.client_number
        where a.plan_status=1 and b.status=1 and a.user_phone=#{phone} and a.task_code=#{taskCode}
    </select>

    <select id="querySchoolDownBuildingList" resultMap="hnslSchoolMap">
        select a.* from HNSL_SCHOOL a
        <if test="userPhone !=null and userPhone !=''">
            LEFT JOIN  HNSL_USER_SCHOOL b ON  a.SCHOOL_CODE=b.SCHOOL_CODE
            where b.USER_PHONE =#{userPhone} AND a.STATUS=1
        </if>
        <if test="cityCode !=null and cityCode !=''">
            where a.STATUS=1 AND a.SCHOOL_CITY =#{cityCode}
        </if>
    </select>

    <select id="queryListBulidingBySchoolCode" resultMap="hnslBuildingMap">
        select * from HNSL_BUILDING where 1=1 and SCHOOL_ID= #{schoolCode}
    </select>

</mapper>
