<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.HnzsxSysUrlConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hlkj.yxsAdminApi.hnzsxH5.entity.HnzsxSysUrlConfigEntity">
        <id column="id" property="id" />
        <result column="url_code" property="urlCode" />
        <result column="prefix_url" property="prefixUrl" />
        <result column="suffix_url" property="suffixUrl" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, url_code, prefix_url, suffix_url, description, status, create_time, update_time, remark
    </sql>
    
    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT 
        <include refid="Base_Column_List" />
        FROM hnzsxh5_sys_url_config
        <where>
            <if test="param.id != null">
                AND id = #{param.id}
            </if>
            <if test="param.urlCode != null and param.urlCode != ''">
                AND url_code LIKE CONCAT('%', #{param.urlCode}, '%')
            </if>
            <if test="param.prefixUrl != null and param.prefixUrl != ''">
                AND prefix_url LIKE CONCAT('%', #{param.prefixUrl}, '%')
            </if>
            <if test="param.suffixUrl != null and param.suffixUrl != ''">
                AND suffix_url LIKE CONCAT('%', #{param.suffixUrl}, '%')
            </if>
            <if test="param.description != null and param.description != ''">
                AND description LIKE CONCAT('%', #{param.description}, '%')
            </if>
            <if test="param.status != null">
                AND status = #{param.status}
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                AND create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                AND create_time &lt;= #{param.createTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultMap="BaseResultMap">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultMap="BaseResultMap">
        <include refid="selectSql"></include>
    </select>

</mapper> 