<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslTaskUploadPicturesMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_task_upload_pictures a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.uploadCode != null">
                AND a.UPLOAD_CODE LIKE CONCAT('%', #{param.uploadCode}, '%')
            </if>
            <if test="param.uploadType != null">
                AND a.UPLOAD_TYPE = #{param.uploadType}
            </if>
            <if test="param.uploadImage != null">
                AND a.UPLOAD_IMAGE LIKE CONCAT('%', #{param.uploadImage}, '%')
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.uploadStatus != null">
                AND a.UPLOAD_STATUS = #{param.uploadStatus}
            </if>
            <if test="param.uploadDate != null">
                AND a.UPLOAD_DATE LIKE CONCAT('%', #{param.uploadDate}, '%')
            </if>
            <if test="param.qqGroupNumber != null">
                AND a.QQ_GROUP_NUMBER LIKE CONCAT('%', #{param.qqGroupNumber}, '%')
            </if>
            <if test="param.qqGroupPeoples != null">
                AND a.QQ_GROUP_PEOPLES = #{param.qqGroupPeoples}
            </if>
            <if test="param.qqGroupName != null">
                AND a.QQ_GROUP_NAME LIKE CONCAT('%', #{param.qqGroupName}, '%')
            </if>
            <if test="param.thumbsUp != null">
                AND a.THUMBS_UP = #{param.thumbsUp}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskUploadPictures">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskUploadPictures">
        <include refid="selectSql"></include>
    </select>

</mapper>
