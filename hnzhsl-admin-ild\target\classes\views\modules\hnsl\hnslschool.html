<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script>

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>

	<div v-show="showList">
	   <div class="row">
	     <div class="form-group col-md-2">
					<label>学校名称:</label> <input type="text" class="form-control"
						placeholder="学校名称" v-model="hnslSchool.schoolName" />
		 </div>
	   <div class="form-group col-md-2">
		   <label>学校编码:</label> <input type="text" class="form-control"
									   placeholder="学校编码" v-model="hnslSchool.schoolCode" />
	   </div>
		 <div class="form-group col-md-2">
					<label>状态:</label> <select class="form-control"
						style="height: 32px;" v-model="hnslSchool.status">
						<option value=''>全部</option>
						<option value='1'>启用</option>
						<option value='0'>禁用</option>

					</select>
		 </div>

		 <div class="form-group col-md-2">
					<label>类型:</label> <select class="form-control"
						style="height: 32px;" v-model="hnslSchool.schoolType">
						<option value=''>全部</option>
						<option value='1'>主学校</option>
						<option value='2'>分校区</option>
					</select>
		 </div>

		   <div class="form-group col-md-2">

			   <label>地市:</label>
		   <select id="prov" name="prov" class="form-control"
				   style="height: 32px; margin-left: -15px;width: 122%;" v-model="cityCode">
			   <option value="">全部</option>
			   <option v-for="item in city" :value="item.cityCode"
					   :key="item.cityCode">{{item.cityName}}</option>
		   </select>

		   </div>

	     <div class="form-group col-md-2" style="height: 32px;">
		   <label>导出日期:</label>
		   <div class="input-group col-ms-2 ">
			   <input class="form-control pull-left dateRange date-picker "
					  id="dateTimeRange" @keyup.enter="query" value="" type="text"
					  placeholder="日期"> <span class="input-group-addon">
						<i class="fa fa-calendar bigger-110"></i>
					</span> <input name="beginTime" id="beginTime" type="hidden"> <input
				   name="endTime" id="endTime" type="hidden">
		   </div>
	     </div>

	   </div>
		<div class="grid-btn" style="height: 40px;margin-left: 1%;">
			<a v-if="hasPermission('hnslschool:query')" class="btn btn-primary" @click="query">&nbsp;查询</a>
			<a v-if="hasPermission('hnslschool:save')" class="btn btn-primary" @click="add">&nbsp;新增</a>
			<a v-if="hasPermission('hnslschool:update')" class="btn btn-primary" @click="update">&nbsp;修改</a>
			<!--v-if="hasPermission('hnslschool:export')"-->
			<a v-if="hasPermission('hnslschool:export')" class="btn btn-primary" @click="exportMonthData">&nbsp;导出</a>
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>

    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
		    <div class="form-group" style="width: 161%;">
			   	<div class="col-sm-2 control-label">学校名称</div>
			   	<div class="col-sm-10" style="width: 28%">
			      <input type="text" class="form-control" v-model="hnslSchool.schoolName" placeholder="学校名称"/>
			    </div>
			</div>
			<div class="form-group" style="width: 161%;">
			   	<div class="col-sm-2 control-label">学校编码</div>
			   	<div class="col-sm-10" style="width: 28%">
			      <input type="text" class="form-control" v-model="hnslSchool.schoolCode" placeholder="学校编码"/>
			    </div>
			</div>
            <div class="form-group" style="width: 161%;" v-if="hnslSchool.schoolGradeType == 1 || hnslSchool.schoolGradeType == 2 || hnslSchool.schoolGradeType ==5 ">
                <div class="col-sm-2 control-label">营协六级ID</div>
                <div class="col-sm-10" style="width: 28%">
                    <input type="text" class="form-control" v-model="hnslSchool.schoolSixId" placeholder="营协六级ID"/>
                </div>
            </div>
<!--			 <div class="form-group" style="width: 161%;">-->
<!--					<div class="col-sm-2 control-label">学校类型:</div> -->
<!--				    <div class="col-sm-10" style="width: 28%">-->
<!--					<select class="form-control"-->
<!--						style="height: 32px;" v-model="hnslSchool.schoolType">-->
<!--						<option value=''>请选择</option>-->
<!--					    <option value='1'>主学校</option>-->
<!--						<option value='2'>分校区</option>-->
<!--					</select>-->
<!--					</div>-->
<!--			</div>-->
			<div class="form-group" style="width: 161%;">
				<div class="col-sm-2 control-label">学校渠道类型:</div>
				<div class="col-sm-10" style="width: 28%">
					<select class="form-control" @change="gradeTypeSelect()"
							style="height: 32px;" v-model="hnslSchool.schoolGradeType">
						<option value=''>请选择</option>
						<option v-for="itme in hnslChannelList" v-bind:value="itme.value">
							{{itme.name}}
						</option>
<!--						<option value='1'>校园渠道-高校</option>-->
<!--						<option value='2'>校园渠道-中小学</option>-->
<!--						<option value='3'>电渠互联网卡渠道</option>-->
<!--						<option value='4'>其他</option>-->
<!--						<option value='5'>校园渠道-中职</option>-->
					</select>
				</div>
			</div>
			<div class="form-group" style="width: 161%;">
				<div class="col-sm-2 control-label">学校地市:</div>
				<div class="col-sm-10" style="width: 28%">
				<select class="form-control"
					style="height: 32px;" v-model="hnslSchool.schoolCity">
					<option value=''>请选择</option>
					<option v-for="itme in city" v-bind:value="itme.cityCode">
						{{itme.cityName}}</option>
				</select>
				</div>
			</div>
			<div v-if="hnslSchool.schoolGradeType!=3">
				<div class="form-group" style="width: 161%;">
					<div class="col-sm-2 control-label">楼栋数</div>
					<div class="col-sm-10" style="width: 28%">
						<input type="text" class="form-control" v-model="hnslSchool.houseNumber" placeholder="非必填"/>
					</div>
				</div>
				<div class="form-group" style="width: 161%;">
					<div class="col-sm-2 control-label">CRM编码</div>
					<div class="col-sm-10" style="width: 28%">
						<input type="text" class="form-control" v-model="hnslSchool.crmCode" placeholder="非必填"/>
					</div>
				</div>
				<div class="form-group" style="width: 161%;">
					<div class="col-sm-2 control-label">校内流量编码</div>
					<div class="col-sm-10" style="width: 28%">
						<input type="text" class="form-control" v-model="hnslSchool.schoolFlowCode" placeholder="非必填"/>
					</div>
				</div>
				<!--			<div class="form-group" style="width: 161%;">-->
				<!--			   	<div class="col-sm-2 control-label">校园经理</div>-->
				<!--			   	<div class="col-sm-10" style="width: 28%">-->
				<!--			      <input type="text" class="form-control" placeholder="暂时没有" id="manager" />-->
				<!--			    </div>-->
				<!--			</div>-->
				<div class="form-group" style="width: 161%;margin-top:-2%">
					<div class="col-sm-2 control-label">本网用户总人数</div>
					<div class="col-sm-10" style="width: 28%">
						<input type="text" class="form-control" v-model="hnslSchool.schoolNetwork" placeholder="非必填"/>
					</div>
				</div>
				<div class="form-group" style="width: 161%;margin-top:-2%">
					<div class="col-sm-2 control-label">现有登记人数</div>
					<div class="col-sm-10" style="width: 28%">
						<input type="text" class="form-control" v-model="hnslSchool.schoolRegisterExisting" placeholder="非必填"/>
					</div>
				</div>

				<div class="form-group" style="width: 161%;margin-top:-2%">
					<div class="col-sm-2 control-label">本网用户现有人数</div>
					<div class="col-sm-10" style="width: 28%">
						<input type="text" class="form-control" v-model="hnslSchool.schoolNetworkExisting" placeholder="非必填"/>
					</div>
				</div>
<!--				<div class="form-group" style="width: 161%;">-->
<!--					<div class="col-sm-2 control-label">熟卡二维码开启:</div>-->
<!--					<div class="col-sm-10" style="width: 28%">-->
<!--						<select class="form-control"-->
<!--								style="height: 32px;" v-model="hnslSchool.schoolQrcode">-->
<!--							<option value='0'>关闭</option>-->
<!--							<option value='1'>开启</option>-->
<!--						</select>-->
<!--					</div>-->
<!--				</div>-->
				<div class="form-group" style="width: 161%;margin-top:-2%">
					<div class="col-sm-2 control-label">学校群组号</div>
					<div class="col-sm-10" style="width: 28%">
						<input type="text" class="form-control" v-model="hnslSchool.schoolGroupNumber" placeholder="非必填"/>
					</div>
				</div>
				<div class="form-group" style="width: 161%;margin-top:-2%">
					<div class="col-sm-2 control-label">签约模板号</div>
					<div class="col-sm-10" style="width: 28%">
						<input type="text" class="form-control" v-model="hnslSchool.schoolTemplate" placeholder="非必填"/>
					</div>
				</div>
				<div class="form-group" style="width: 161%;margin-top:-2%">
					<div class="col-sm-2 control-label">PAKID</div>
					<div class="col-sm-10" style="width: 28%">
						<input type="text" class="form-control" v-model="hnslSchool.schoolPakid" placeholder="非必填"/>
					</div>
				</div>
				<div class="form-group" style="width: 161%;margin-top:-2%">
					<div class="col-sm-2 control-label">一级合伙人店奖比例</div>
					<div class="col-sm-10" style="width: 28%">
						<select class="form-control"
								style="height: 32px;" v-model="hnslSchool.oneUserScale">
							<option v-for="itme in userScaleList" v-model="hnslSchool.oneUserScale" v-bind:value="itme">
								{{itme}}%</option>
						</select>
					</div>
				</div>
				<div class="form-group" style="width: 161%;margin-top:-2%">
					<div class="col-sm-2 control-label">二级合伙人店奖比例</div>
					<div class="col-sm-10" style="width: 28%">
						<select class="form-control"
								style="height: 32px;" v-model="hnslSchool.twoUserScale">
							<option v-for="itme in userScaleList" v-model="hnslSchool.twoUserScale" v-bind:value="itme">
								{{itme}}%</option>
						</select>
					</div>
				</div>
			</div>
			<div class="form-group" style="width: 161%;">
				<div class="col-sm-2 control-label">展示模块:</div>
				<div class="col-sm-10" style="width: 28%">
					<select class="form-control"
							style="height: 32px;" v-model="hnslSchool.schoolModuleType">
						<option value='1'>全部模块</option>
						<option value='2'>部分模块</option>
					</select>
				</div>
			</div>
			<div class="form-group" style="width: 161%;">
				<div class="col-sm-2 control-label">图片上传类型:</div>
				<div class="col-sm-10" style="width: 28%">
					<select class="form-control"
							style="height: 32px;" v-model="hnslSchool.schoolImageUploadType">
						<option value='1'>拍照</option>
						<option value='2'>拍照或相册</option>
					</select>
				</div>
			</div>
			<div class="form-group" style="width: 161%;margin-top:-2%" v-if="hnslSchool.schoolGradeType==3">
				<div class="col-sm-2 control-label">bps渠道ID</div>
				<div class="col-sm-10" style="width: 28%">
					<input type="text" class="form-control" v-model="hnslSchool.channelId" placeholder="非必填"/>
				</div>
			</div>
			<div class="form-group" style="width: 161%;margin-top:-2%" v-if="hnslSchool.schoolGradeType==3">
				<div class="col-sm-2 control-label">号池渠道ID</div>
				<div class="col-sm-10" style="width: 28%">
					<input type="text" class="form-control" v-model="hnslSchool.cardpoolChannelId" placeholder="非必填"/>
				</div>
			</div>
				<!--			<div class="form-group" style="width: 161%;">-->
<!--				<div class="col-sm-2 control-label">生卡预约开关:</div>-->
<!--				<div class="col-sm-10" style="width: 28%">-->
<!--					<select class="form-control"-->
<!--							style="height: 32px;" v-model="hnslSchool.schoolReservationSwitch">-->
<!--						<option value='1'>打开</option>-->
<!--						<option value='2'>关闭</option>-->
<!--					</select>-->
<!--				</div>-->
<!--			</div>-->
<!--			<div class="form-group" style="width: 161%;" v-if="imgShow" >-->
<!--				<div class="col-sm-2 control-label">客服二维码:</div>-->
<!--				<div class="col-sm-10" style="width: 28%">-->
<!--					<img :src="url+imgUrl" class="fileUp" id="kf" alt="" height="100" width="100" @click="delectImg()" />-->
<!--					<input class="fileUp" type="file" @change="upload()" accept="image/*" id="file" value="" />-->
<!--				</div>-->
<!--			</div>-->
			<div class="form-group" style="width: 161%;margin-top:-2%">
				<div class="col-sm-2 control-label"></div>

				<input type="button" v-show="determine" class="btn btn-primary" style="margin-left: 3%" @click="format" value="确定"/>
				&nbsp;&nbsp;
				<input type="button" style="margin-left: 3%" class="btn btn-warning" @click="back" value="返回"/>
			</div>
		</form>
	</div>
</div>

<script src="../../js/modules/hnsl/hnslschool.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>