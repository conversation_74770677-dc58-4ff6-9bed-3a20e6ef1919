<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslBuildingH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_building a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.buildingName != null">
                AND a.BUILDING_NAME LIKE CONCAT('%', #{param.buildingName}, '%')
            </if>
            <if test="param.buildingTier != null">
                AND a.BUILDING_TIER = #{param.buildingTier}
            </if>
            <if test="param.buildingRegisterExisting != null">
                AND a.BUILDING_REGISTER_EXISTING = #{param.buildingRegisterExisting}
            </if>
            <if test="param.networkPhoneExisting != null">
                AND a.NETWORK_PHONE_EXISTING = #{param.networkPhoneExisting}
            </if>
            <if test="param.networkExisting != null">
                AND a.NETWORK_EXISTING = #{param.networkExisting}
            </if>
            <if test="param.schoolId != null">
                AND a.SCHOOL_ID LIKE CONCAT('%', #{param.schoolId}, '%')
            </if>
            <if test="param.userId != null">
                AND a.USER_ID LIKE CONCAT('%', #{param.userId}, '%')
            </if>
            <if test="param.buildingRoom != null">
                AND a.BUILDING_ROOM = #{param.buildingRoom}
            </if>
            <if test="param.buildingNumber != null">
                AND a.BUILDING_NUMBER = #{param.buildingNumber}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.buildingId != null">
                AND a.BUILDING_ID LIKE CONCAT('%', #{param.buildingId}, '%')
            </if>
            <if test="param.schoolRegister != null">
                AND a.SCHOOL_REGISTER = #{param.schoolRegister}
            </if>
            <if test="param.qqform != null">
                AND a.QQFORM LIKE CONCAT('%', #{param.qqform}, '%')
            </if>
            <if test="param.buildingImage != null">
                AND a.BUILDING_IMAGE LIKE CONCAT('%', #{param.buildingImage}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Building">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Building">
        <include refid="selectSql"></include>
    </select>

    <select id="queryBuilding" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Building"
            resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Building">
        select t1.* from hnsl_building_user t left join hnsl_building t1 on t.building_id=t1.building_id
        where t.status=1
        <if test="userId!=null and userId!=''">
            AND t.USER_ID=#{userId}
        </if>
        <if test="schoolId!=null and schoolId!=''">
            AND t1.SCHOOL_ID=#{schoolId}
        </if>
        <if test="buildingName!=null and buildingName!=''">
            AND t1.BUILDING_NAME=#{buildingName}
        </if>
        <if test="buildingId!=null and  buildingId!=''">
            AND t.BUILDING_ID = #{buildingId}
        </if>
    </select>
</mapper>
