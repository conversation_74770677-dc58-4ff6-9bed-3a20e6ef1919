<!DOCTYPE html>
<html>
<head>
    <title>导出审批列表</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../js/common.js"></script>
    <style type="text/css">
        #loading {
            position: fixed;
            z-index: 1000;
            top: 0;
            left: 0;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 38px;
            height: 38px;
            border-radius: 50%;
            border-top-color: #000;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showList">
        <div class="row">
            <div class="form-group col-md-2" style="height: 40px">
                <label>文件名:</label>
                <input type="text" class="form-control" placeholder="请输入文件名" v-model="hnslExportList.fileName" />
            </div>

            <div class="form-group col-md-2" style="height: 40px">
                <label>导出类型:</label>
                <select class="form-control"
                        style="height: 32px;" v-model="hnslExportList.activeEvent">
                    <option value=''>全部</option>
                    <option v-for="itme in status" v-bind:value="itme.statusId">
                        {{itme.statusName}}
                    </option>
                </select>
            </div>

            <div style="color: red;font-weight: bold;">
                <p>自审批同意起, 72小时内可下载数据</p>
                <p>仅展示7天之内的数据</p>
            </div>
        </div>

        <div class="grid-btn" style="margin-left: 17Px; margin-top: 18px">
            <a v-if="hasPermission('hnslwhiteapprove:query')" class="btn btn-primary" @click="query"><i></i>&nbsp;查询</a>
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>

    <!--驳回原因弹窗-->
    <div class="modal"  id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true">×
                    </button>
                    <h4 class="modal-title" id="myModalLabel" style="text-align: center">驳回原因</h4>
                </div>
                <div class="form-horizontal" style="padding-top: 20px;width: 100%; height: 150px;">
                    <form id="uploadImg" enctype="multipart/form-data">
                            <div class="modal-body" style="display: flex;flex-direction: column">
                                <div style="margin-top: 5px">
                                    <textarea v-model="hnslExportList.remark"
                                              placeholder="请输入驳回原因,仅限于48个字符以内"
                                              style="width: 100%; height: 80px;"
                                              maxlength="48"
                                    ></textarea>
                                </div>
                            </div>
                    </form>
                </div>
                <div style="padding: 15px; text-align: center;">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                            style="width: 80px;background-color: #ccc;">关闭</button>
                    <button class="btn btn-primary" style="width: 80px" @click="submitReject" :disabled="isReject">提交</button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal"  id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="padding: 180px">
        <div class="modal-dialog" style="width: 300px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true">
                    </button>
                    <h5 class="modal-title" id="myModalLabel1" style="text-align: left">信息</h5>
                    <div style="padding-top: 20px; text-align: left;display: flex;justify-content: space-around;flex-direction: column">
                        <div style="">确认同意通过该导出审批?</div>
                        <div style="padding-top: 30px;text-align: right;">
                            <button type="button" class="btn btn-default" data-dismiss="modal"
                                    style="width: 50px;line-height: 28px;height: 28px;background-color: rgb(204, 204, 204);font-size: 13px;padding: 0 12px;">
                                关闭
                            </button>
                            <button class="btn btn-primary"
                               style="width: 50px;line-height: 28px;height: 28px;font-size: 13px;padding: 0 12px;"
                               @click="approvalPass" :disabled="isClick">确定</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="position:relative;display: none" id="loading">
        <div class="spinner" style="position: absolute; bottom: 350px; text-align: center; z-index: 999;  color: red;font-size: 18px;"></div>
    </div>

</div>

<script src="../../js/modules/hnsl/hnslExportApprovalList.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>