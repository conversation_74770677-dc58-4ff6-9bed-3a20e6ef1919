<html xmlns:th="http://www.thymeleaf.org">
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0">
    <title>登录</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico">
    <script src="libs/jquery.min.js"></script>
	<script src="plugins/jqgrid/jquery.jqGrid.min.js"></script>
	<script src="libs/vue.min.js"></script>
	<script src="plugins/layer/layer.js"></script>
	<script src="js/common.js"></script>
	<script type="text/javascript" src="common/js/jsbn.js"></script>
	<script type="text/javascript" src="common/js/prng4.js"></script>
	<script type="text/javascript" src="common/js/rng.js"></script>
	<script type="text/javascript" src="common/js/rsa.js"></script>
	<script type="text/javascript" src="common/js/base64.js"></script>
</head>

<body class="login-bg" style="height: 100% !important;margin-bottom: -10px;">

</body>
<script type="text/javascript" th:inline="javascript">
     var msg = [[${msg}]] ;  
   
    //重写confirm式样框
        layer.alert(msg, {  
	        yes: function(index, layero){  
	            // 点击确定后的处理逻辑  
	            window.location.href = baseURL + "/login.html"
	            layer.close(index); // 关闭对话框  
	        }  
	    });  
</script>
</html>
