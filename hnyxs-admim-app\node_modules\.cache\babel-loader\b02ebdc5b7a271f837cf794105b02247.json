{"ast": null, "code": "import \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.error.cause.js\";\nimport request from '@/utils/request';\nimport { hasPermission } from '@/utils/permission';\nimport { API_BASE_URL } from '@/config/setting';\nimport { getToken } from '@/utils/token-util'; // 通用下载URL\n\nexport const getDaoUsers = `${API_BASE_URL}/download/exportDaoUsers`; // 权限前缀\n\nconst permPrefix = 'hnzsxH5:hnzsxh5OrderInfo:';\n/**\r\n * 分页查询订单信息\r\n * @param data 查询条件\r\n */\n\nexport async function pageOrders(data) {\n  // 检查权限\n  if (!hasPermission(permPrefix + 'list')) {\n    return {\n      list: [],\n      count: 0\n    };\n  } // 调试日志\n\n\n  console.log('pageOrders请求参数:', JSON.stringify(data)); // 确保state和paymentStatus为数字类型\n\n  if (data.state !== undefined && data.state !== null && data.state !== '') {\n    data.state = Number(data.state);\n  }\n\n  if (data.paymentStatus !== undefined && data.paymentStatus !== null && data.paymentStatus !== '') {\n    data.paymentStatus = Number(data.paymentStatus);\n  }\n\n  const res = await request.post('/hnzsxH5/hnzsxh5-order-info/page', data, {\n    headers: {\n      'Content-Type': 'application/json'\n    }\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 获取所有订单列表\r\n * @param data 查询条件\r\n */\n\nexport async function listAllOrders(data) {\n  // 检查权限\n  if (!hasPermission(permPrefix + 'list')) {\n    return [];\n  }\n\n  const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getAllListByParam', data, {\n    headers: {\n      'Content-Type': 'application/json'\n    }\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 根据ID获取订单详情\r\n * @param {Object} data 包含id的对象\r\n * @returns {Promise<Object>} 订单详情\r\n */\n\nexport async function getOrderDetail(data) {\n  // 添加错误处理\n  if (!data || data.id === undefined || data.id === null) {\n    console.error('getOrderDetail调用错误: 无效的参数', data);\n    return Promise.reject(new Error('参数错误：订单ID不能为空'));\n  } // 检查权限\n\n\n  if (!hasPermission(permPrefix + 'list')) {\n    return {};\n  }\n\n  try {\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getInfoById', data, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    if (res.data.code === 0) {\n      return res.data.data;\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取订单详情失败'));\n  } catch (error) {\n    console.error('getOrderDetail请求异常:', error);\n    return Promise.reject(error);\n  }\n}\n/**\r\n * 获取订单统计数据\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Array>} 订单统计数据\r\n */\n\nexport async function getOrderCount(data) {\n  try {\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getCount', data, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    if (res.data.code === 0) {\n      return res.data.data;\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取订单统计数据失败'));\n  } catch (error) {\n    console.error('getOrderCount请求异常:', error);\n    return Promise.reject(error);\n  }\n}\n/**\r\n * 获取各地市每日订单数量\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Array>} 各地市每日订单数据\r\n */\n\nexport async function getDailyOrdersByCityCode(data) {\n  try {\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getDailyOrdersByCityCode', data, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    if (res.data.code === 0) {\n      return res.data.data;\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取各地市每日订单数据失败'));\n  } catch (error) {\n    console.error('getDailyOrdersByCityCode请求异常:', error);\n    return Promise.reject(error);\n  }\n}\n/**\r\n * 获取各地市按模块分类的订单数量\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Object>} 各地市按模块分类的订单数据\r\n */\n\nexport async function getOrderCountByModule(data) {\n  try {\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getOrderCountByModule', data, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    if (res.data.code === 0) {\n      return res.data.data;\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取各地市按模块分类订单数据失败'));\n  } catch (error) {\n    console.error('getOrderCountByModule请求异常:', error);\n    return Promise.reject(error);\n  }\n}\n/**\r\n * 获取按模块类型汇总发展总量统计\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Array>} 按模块类型汇总的订单统计数据\r\n */\n\nexport async function getOrderCountByModuleType(data) {\n  try {\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getOrderCountByModuleType', data, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    if (res.data.code === 0) {\n      return res.data.data;\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取按模块类型汇总统计数据失败'));\n  } catch (error) {\n    console.error('getOrderCountByModuleType请求异常:', error);\n    return Promise.reject(error);\n  }\n}\n/**\r\n * 获取按地市汇总发展总量统计\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Array>} 按地市汇总的订单统计数据\r\n */\n\nexport async function getOrderCountByCity(data) {\n  try {\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getOrderCountByCity', data, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    if (res.data.code === 0) {\n      return res.data.data;\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取按地市汇总统计数据失败'));\n  } catch (error) {\n    console.error('getOrderCountByCity请求异常:', error);\n    return Promise.reject(error);\n  }\n}\n/**\r\n * 获取订单状态映射\r\n */\n\nexport function getOrderStatusMap() {\n  return {\n    0: {\n      text: '完结',\n      type: 'success'\n    },\n    1: {\n      text: '暂存单',\n      type: 'info'\n    },\n    2: {\n      text: '订单确认',\n      type: 'primary'\n    },\n    3: {\n      text: '未支付',\n      type: 'warning'\n    },\n    4: {\n      text: '已支付',\n      type: 'success'\n    },\n    [-2]: {\n      text: '已删除',\n      type: 'danger'\n    },\n    [-3]: {\n      text: '生成暂存失败',\n      type: 'danger'\n    },\n    [-4]: {\n      text: '订单校验失败',\n      type: 'danger'\n    },\n    [-5]: {\n      text: '订单生单失败',\n      type: 'danger'\n    },\n    [-6]: {\n      text: '订单费用计算失败',\n      type: 'danger'\n    },\n    [-7]: {\n      text: '订单免填单生成失败',\n      type: 'danger'\n    },\n    [-8]: {\n      text: '订单收费确认失败',\n      type: 'danger'\n    }\n  };\n}\n/**\r\n * 获取支付状态映射\r\n */\n\nexport function getPaymentStatusMap() {\n  return {\n    2: {\n      text: '未支付',\n      type: 'warning'\n    },\n    1: {\n      text: '已支付',\n      type: 'success'\n    },\n    3: {\n      text: '费用0，无需支付',\n      type: 'info'\n    },\n    null: {\n      text: '未支付',\n      type: 'warning'\n    },\n    undefined: {\n      text: '未支付',\n      type: 'warning'\n    }\n  };\n} // 手动过费\n\nexport function manualPayment(data) {\n  return request({\n    url: '/hnzsxH5/hnzsxh5-order-info/h5CommitFee',\n    method: 'post',\n    data: data\n  }).then(res => {\n    return res.data;\n  }).catch(error => {\n    console.error('手动过费请求异常:', error);\n    return Promise.reject(error);\n  });\n}\n/**\r\n * 导出订单数据为Excel\r\n * @param {Object} data 查询条件\r\n */\n\nexport function exportOrderData(data) {\n  // 创建导出URL\n  let url = `/hnzsxH5/hnzsxh5-order-info/exportOrderData`; // 如果有查询参数，转换成JSON字符串\n\n  let config = {\n    responseType: 'blob',\n    headers: {\n      'Content-Type': 'application/json',\n      'Authorization': 'Bearer ' + getToken()\n    }\n  };\n  return request.post(url, data || {}, config);\n}\n/**\r\n * 查询暂存单信息\r\n * @param {string} orderNo 订单号\r\n * @returns {Promise<Object>} 暂存单信息\r\n * <AUTHOR>\r\n * @date 2025-05-19\r\n */\n\nexport function queryCancelExamTosafe(orderNo) {\n  return request({\n    url: '/hnzsxH5/hnzsxh5-order-info/queryCancelExamTosafe',\n    method: 'post',\n    data: {\n      orderNo\n    }\n  });\n}\n/**\r\n * 解冻用户微信订单\r\n * @param {string} besttoneOrderItemNo 固网担保流水号\r\n * @returns {Promise<Object>} 解冻结果\r\n * <AUTHOR>\r\n * @date 2025-05-21\r\n */\n\nexport function cancelExamTosafe(besttoneOrderItemNo) {\n  return request({\n    url: '/hnzsxH5/hnzsxh5-order-info/cancelCancelExamTosafe',\n    method: 'post',\n    data: {\n      besttoneOrderItemNo\n    }\n  });\n}\n/**\r\n * 变更订单状态\r\n * @param {Object} data 包含订单号、新状态和变更原因的对象\r\n * @returns {Promise<Object>} 变更结果\r\n * <AUTHOR>\r\n * @date 2025-06-01\r\n */\n\nexport function changeOrderState(data) {\n  return request({\n    url: '/hnzsxH5/hnzsxh5-order-info/changeOrderState',\n    method: 'post',\n    data: data\n  }).then(res => {\n    return res.data;\n  }).catch(error => {\n    console.error('订单状态变更请求异常:', error);\n    return Promise.reject(error);\n  });\n}\n/**\r\n * 变更订单支付状态\r\n * @param {Object} data 包含订单号、新支付状态和变更原因的对象\r\n * @returns {Promise<Object>} 变更结果\r\n * <AUTHOR>\r\n * @date 2025-06-28\r\n */\n\nexport function changeOrderPaymentStatus(data) {\n  return request({\n    url: '/hnzsxH5/hnzsxh5-order-info/changeOrderPaymentStatus',\n    method: 'post',\n    data: data\n  }).then(res => {\n    return res.data;\n  }).catch(error => {\n    console.error('订单支付状态变更请求异常:', error);\n    return Promise.reject(error);\n  });\n}\n/**\r\n * 统一变更订单状态和支付状态\r\n * @param {Object} data 包含订单号、新状态、新支付状态和变更原因的对象\r\n * @returns {Promise<Object>} 变更结果\r\n * <AUTHOR>\r\n * @date 2025-07-01\r\n */\n\nexport function changeOrderStatusUnified(data) {\n  return request({\n    url: '/hnzsxH5/hnzsxh5-order-info/changeOrderStatusUnified',\n    method: 'post',\n    data: data\n  }).then(res => {\n    return res.data;\n  }).catch(error => {\n    console.error('统一变更订单状态请求异常:', error);\n    return Promise.reject(error);\n  });\n}", "map": {"version": 3, "names": ["request", "hasPermission", "API_BASE_URL", "getToken", "getDaoUsers", "permPrefix", "pageOrders", "data", "list", "count", "console", "log", "JSON", "stringify", "state", "undefined", "Number", "paymentStatus", "res", "post", "headers", "code", "Promise", "reject", "Error", "message", "listAllOrders", "getOrderDetail", "id", "error", "getOrderCount", "getDailyOrdersByCityCode", "getOrderCountByModule", "getOrderCountByModuleType", "getOrderCountByCity", "getOrderStatusMap", "text", "type", "getPaymentStatusMap", "null", "manualPayment", "url", "method", "then", "catch", "exportOrderData", "config", "responseType", "queryCancelExamTosafe", "orderNo", "cancelExamTosafe", "besttoneOrderItemNo", "changeOrderState", "changeOrderPaymentStatus", "changeOrderStatusUnified"], "sources": ["D:/code/dianxinCode/新版省集约项目/hnyxs-admim-app/src/api/hnzsxH5/order.js"], "sourcesContent": ["import request from '@/utils/request';\r\nimport { hasPermission } from '@/utils/permission';\r\nimport { API_BASE_URL } from '@/config/setting';\r\nimport { getToken } from '@/utils/token-util';\r\n\r\n// 通用下载URL\r\nexport const getDaoUsers = `${API_BASE_URL}/download/exportDaoUsers`;\r\n\r\n// 权限前缀\r\nconst permPrefix = 'hnzsxH5:hnzsxh5OrderInfo:';\r\n\r\n/**\r\n * 分页查询订单信息\r\n * @param data 查询条件\r\n */\r\nexport async function pageOrders(data) {\r\n  // 检查权限\r\n  if (!hasPermission(permPrefix + 'list')) {\r\n    return {\r\n      list: [],\r\n      count: 0\r\n    };\r\n  }\r\n\r\n  // 调试日志\r\n  console.log('pageOrders请求参数:', JSON.stringify(data));\r\n\r\n  // 确保state和paymentStatus为数字类型\r\n  if (data.state !== undefined && data.state !== null && data.state !== '') {\r\n    data.state = Number(data.state);\r\n  }\r\n\r\n  if (data.paymentStatus !== undefined && data.paymentStatus !== null && data.paymentStatus !== '') {\r\n    data.paymentStatus = Number(data.paymentStatus);\r\n  }\r\n\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-order-info/page', data, {\r\n    headers: { 'Content-Type': 'application/json' }\r\n  });\r\n\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 获取所有订单列表\r\n * @param data 查询条件\r\n */\r\nexport async function listAllOrders(data) {\r\n  // 检查权限\r\n  if (!hasPermission(permPrefix + 'list')) {\r\n    return [];\r\n  }\r\n\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getAllListByParam', data, {\r\n    headers: { 'Content-Type': 'application/json' }\r\n  });\r\n\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 根据ID获取订单详情\r\n * @param {Object} data 包含id的对象\r\n * @returns {Promise<Object>} 订单详情\r\n */\r\nexport async function getOrderDetail(data) {\r\n  // 添加错误处理\r\n  if (!data || data.id === undefined || data.id === null) {\r\n    console.error('getOrderDetail调用错误: 无效的参数', data);\r\n    return Promise.reject(new Error('参数错误：订单ID不能为空'));\r\n  }\r\n\r\n  // 检查权限\r\n  if (!hasPermission(permPrefix + 'list')) {\r\n    return {};\r\n  }\r\n\r\n  try {\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getInfoById', data, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return res.data.data;\r\n    }\r\n    return Promise.reject(new Error(res.data.message || '获取订单详情失败'));\r\n  } catch (error) {\r\n    console.error('getOrderDetail请求异常:', error);\r\n    return Promise.reject(error);\r\n  }\r\n}\r\n\r\n/**\r\n * 获取订单统计数据\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Array>} 订单统计数据\r\n */\r\nexport async function getOrderCount(data) {\r\n  try {\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getCount', data, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return res.data.data;\r\n    }\r\n    return Promise.reject(new Error(res.data.message || '获取订单统计数据失败'));\r\n  } catch (error) {\r\n    console.error('getOrderCount请求异常:', error);\r\n    return Promise.reject(error);\r\n  }\r\n}\r\n\r\n/**\r\n * 获取各地市每日订单数量\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Array>} 各地市每日订单数据\r\n */\r\nexport async function getDailyOrdersByCityCode(data) {\r\n  try {\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getDailyOrdersByCityCode', data, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return res.data.data;\r\n    }\r\n    return Promise.reject(new Error(res.data.message || '获取各地市每日订单数据失败'));\r\n  } catch (error) {\r\n    console.error('getDailyOrdersByCityCode请求异常:', error);\r\n    return Promise.reject(error);\r\n  }\r\n}\r\n\r\n/**\r\n * 获取各地市按模块分类的订单数量\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Object>} 各地市按模块分类的订单数据\r\n */\r\nexport async function getOrderCountByModule(data) {\r\n  try {\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getOrderCountByModule', data, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return res.data.data;\r\n    }\r\n    return Promise.reject(new Error(res.data.message || '获取各地市按模块分类订单数据失败'));\r\n  } catch (error) {\r\n    console.error('getOrderCountByModule请求异常:', error);\r\n    return Promise.reject(error);\r\n  }\r\n}\r\n\r\n/**\r\n * 获取按模块类型汇总发展总量统计\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Array>} 按模块类型汇总的订单统计数据\r\n */\r\nexport async function getOrderCountByModuleType(data) {\r\n  try {\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getOrderCountByModuleType', data, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return res.data.data;\r\n    }\r\n    return Promise.reject(new Error(res.data.message || '获取按模块类型汇总统计数据失败'));\r\n  } catch (error) {\r\n    console.error('getOrderCountByModuleType请求异常:', error);\r\n    return Promise.reject(error);\r\n  }\r\n}\r\n\r\n/**\r\n * 获取按地市汇总发展总量统计\r\n * @param {Object} data 包含日期的对象\r\n * @returns {Promise<Array>} 按地市汇总的订单统计数据\r\n */\r\nexport async function getOrderCountByCity(data) {\r\n  try {\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getOrderCountByCity', data, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return res.data.data;\r\n    }\r\n    return Promise.reject(new Error(res.data.message || '获取按地市汇总统计数据失败'));\r\n  } catch (error) {\r\n    console.error('getOrderCountByCity请求异常:', error);\r\n    return Promise.reject(error);\r\n  }\r\n}\r\n\r\n/**\r\n * 获取订单状态映射\r\n */\r\nexport function getOrderStatusMap() {\r\n  return {\r\n    0: { text: '完结', type: 'success' },\r\n    1: { text: '暂存单', type: 'info' },\r\n    2: { text: '订单确认', type: 'primary' },\r\n    3: { text: '未支付', type: 'warning' },\r\n    4: { text: '已支付', type: 'success' },\r\n    [-2]: { text: '已删除', type: 'danger' },\r\n    [-3]: { text: '生成暂存失败', type: 'danger' },\r\n    [-4]: { text: '订单校验失败', type: 'danger' },\r\n    [-5]: { text: '订单生单失败', type: 'danger' },\r\n    [-6]: { text: '订单费用计算失败', type: 'danger' },\r\n    [-7]: { text: '订单免填单生成失败', type: 'danger' },\r\n    [-8]: { text: '订单收费确认失败', type: 'danger' }\r\n  };\r\n}\r\n\r\n/**\r\n * 获取支付状态映射\r\n */\r\nexport function getPaymentStatusMap() {\r\n  return {\r\n    2: { text: '未支付', type: 'warning' },\r\n    1: { text: '已支付', type: 'success' },\r\n    3: { text: '费用0，无需支付', type: 'info' },\r\n    null: { text: '未支付', type: 'warning' },\r\n    undefined: { text: '未支付', type: 'warning' }\r\n  };\r\n}\r\n\r\n// 手动过费\r\nexport function manualPayment(data) {\r\n  return request({\r\n    url: '/hnzsxH5/hnzsxh5-order-info/h5CommitFee',\r\n    method: 'post',\r\n    data: data\r\n  }).then(res => {\r\n    return res.data;\r\n  }).catch(error => {\r\n    console.error('手动过费请求异常:', error);\r\n    return Promise.reject(error);\r\n  });\r\n}\r\n\r\n/**\r\n * 导出订单数据为Excel\r\n * @param {Object} data 查询条件\r\n */\r\nexport function exportOrderData(data) {\r\n  // 创建导出URL\r\n  let url = `/hnzsxH5/hnzsxh5-order-info/exportOrderData`;\r\n\r\n  // 如果有查询参数，转换成JSON字符串\r\n  let config = {\r\n    responseType: 'blob',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      'Authorization': 'Bearer ' + getToken()\r\n    }\r\n  };\r\n\r\n  return request.post(url, data || {}, config);\r\n}\r\n\r\n/**\r\n * 查询暂存单信息\r\n * @param {string} orderNo 订单号\r\n * @returns {Promise<Object>} 暂存单信息\r\n * <AUTHOR>\r\n * @date 2025-05-19\r\n */\r\nexport function queryCancelExamTosafe(orderNo) {\r\n  return request({\r\n    url: '/hnzsxH5/hnzsxh5-order-info/queryCancelExamTosafe',\r\n    method: 'post',\r\n    data: { orderNo }\r\n  });\r\n}\r\n\r\n/**\r\n * 解冻用户微信订单\r\n * @param {string} besttoneOrderItemNo 固网担保流水号\r\n * @returns {Promise<Object>} 解冻结果\r\n * <AUTHOR>\r\n * @date 2025-05-21\r\n */\r\nexport function cancelExamTosafe(besttoneOrderItemNo) {\r\n  return request({\r\n    url: '/hnzsxH5/hnzsxh5-order-info/cancelCancelExamTosafe',\r\n    method: 'post',\r\n    data: { besttoneOrderItemNo }\r\n  });\r\n}\r\n\r\n/**\r\n * 变更订单状态\r\n * @param {Object} data 包含订单号、新状态和变更原因的对象\r\n * @returns {Promise<Object>} 变更结果\r\n * <AUTHOR>\r\n * @date 2025-06-01\r\n */\r\nexport function changeOrderState(data) {\r\n  return request({\r\n    url: '/hnzsxH5/hnzsxh5-order-info/changeOrderState',\r\n    method: 'post',\r\n    data: data\r\n  }).then(res => {\r\n    return res.data;\r\n  }).catch(error => {\r\n    console.error('订单状态变更请求异常:', error);\r\n    return Promise.reject(error);\r\n  });\r\n}\r\n\r\n/**\r\n * 变更订单支付状态\r\n * @param {Object} data 包含订单号、新支付状态和变更原因的对象\r\n * @returns {Promise<Object>} 变更结果\r\n * <AUTHOR>\r\n * @date 2025-06-28\r\n */\r\nexport function changeOrderPaymentStatus(data) {\r\n  return request({\r\n    url: '/hnzsxH5/hnzsxh5-order-info/changeOrderPaymentStatus',\r\n    method: 'post',\r\n    data: data\r\n  }).then(res => {\r\n    return res.data;\r\n  }).catch(error => {\r\n    console.error('订单支付状态变更请求异常:', error);\r\n    return Promise.reject(error);\r\n  });\r\n}\r\n\r\n/**\r\n * 统一变更订单状态和支付状态\r\n * @param {Object} data 包含订单号、新状态、新支付状态和变更原因的对象\r\n * @returns {Promise<Object>} 变更结果\r\n * <AUTHOR>\r\n * @date 2025-07-01\r\n */\r\nexport function changeOrderStatusUnified(data) {\r\n  return request({\r\n    url: '/hnzsxH5/hnzsxh5-order-info/changeOrderStatusUnified',\r\n    method: 'post',\r\n    data: data\r\n  }).then(res => {\r\n    return res.data;\r\n  }).catch(error => {\r\n    console.error('统一变更订单状态请求异常:', error);\r\n    return Promise.reject(error);\r\n  });\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,OAAP,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,oBAA9B;AACA,SAASC,YAAT,QAA6B,kBAA7B;AACA,SAASC,QAAT,QAAyB,oBAAzB,C,CAEA;;AACA,OAAO,MAAMC,WAAW,GAAI,GAAEF,YAAa,0BAApC,C,CAEP;;AACA,MAAMG,UAAU,GAAG,2BAAnB;AAEA;AACA;AACA;AACA;;AACA,OAAO,eAAeC,UAAf,CAA0BC,IAA1B,EAAgC;EACrC;EACA,IAAI,CAACN,aAAa,CAACI,UAAU,GAAG,MAAd,CAAlB,EAAyC;IACvC,OAAO;MACLG,IAAI,EAAE,EADD;MAELC,KAAK,EAAE;IAFF,CAAP;EAID,CAPoC,CASrC;;;EACAC,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BC,IAAI,CAACC,SAAL,CAAeN,IAAf,CAA/B,EAVqC,CAYrC;;EACA,IAAIA,IAAI,CAACO,KAAL,KAAeC,SAAf,IAA4BR,IAAI,CAACO,KAAL,KAAe,IAA3C,IAAmDP,IAAI,CAACO,KAAL,KAAe,EAAtE,EAA0E;IACxEP,IAAI,CAACO,KAAL,GAAaE,MAAM,CAACT,IAAI,CAACO,KAAN,CAAnB;EACD;;EAED,IAAIP,IAAI,CAACU,aAAL,KAAuBF,SAAvB,IAAoCR,IAAI,CAACU,aAAL,KAAuB,IAA3D,IAAmEV,IAAI,CAACU,aAAL,KAAuB,EAA9F,EAAkG;IAChGV,IAAI,CAACU,aAAL,GAAqBD,MAAM,CAACT,IAAI,CAACU,aAAN,CAA3B;EACD;;EAED,MAAMC,GAAG,GAAG,MAAMlB,OAAO,CAACmB,IAAR,CAAa,kCAAb,EAAiDZ,IAAjD,EAAuD;IACvEa,OAAO,EAAE;MAAE,gBAAgB;IAAlB;EAD8D,CAAvD,CAAlB;;EAIA,IAAIF,GAAG,CAACX,IAAJ,CAASc,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOH,GAAG,CAACX,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOe,OAAO,CAACC,MAAR,CAAe,IAAIC,KAAJ,CAAUN,GAAG,CAACX,IAAJ,CAASkB,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;;AACA,OAAO,eAAeC,aAAf,CAA6BnB,IAA7B,EAAmC;EACxC;EACA,IAAI,CAACN,aAAa,CAACI,UAAU,GAAG,MAAd,CAAlB,EAAyC;IACvC,OAAO,EAAP;EACD;;EAED,MAAMa,GAAG,GAAG,MAAMlB,OAAO,CAACmB,IAAR,CAAa,+CAAb,EAA8DZ,IAA9D,EAAoE;IACpFa,OAAO,EAAE;MAAE,gBAAgB;IAAlB;EAD2E,CAApE,CAAlB;;EAIA,IAAIF,GAAG,CAACX,IAAJ,CAASc,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOH,GAAG,CAACX,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOe,OAAO,CAACC,MAAR,CAAe,IAAIC,KAAJ,CAAUN,GAAG,CAACX,IAAJ,CAASkB,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAeE,cAAf,CAA8BpB,IAA9B,EAAoC;EACzC;EACA,IAAI,CAACA,IAAD,IAASA,IAAI,CAACqB,EAAL,KAAYb,SAArB,IAAkCR,IAAI,CAACqB,EAAL,KAAY,IAAlD,EAAwD;IACtDlB,OAAO,CAACmB,KAAR,CAAc,2BAAd,EAA2CtB,IAA3C;IACA,OAAOe,OAAO,CAACC,MAAR,CAAe,IAAIC,KAAJ,CAAU,eAAV,CAAf,CAAP;EACD,CALwC,CAOzC;;;EACA,IAAI,CAACvB,aAAa,CAACI,UAAU,GAAG,MAAd,CAAlB,EAAyC;IACvC,OAAO,EAAP;EACD;;EAED,IAAI;IACF,MAAMa,GAAG,GAAG,MAAMlB,OAAO,CAACmB,IAAR,CAAa,yCAAb,EAAwDZ,IAAxD,EAA8D;MAC9Ea,OAAO,EAAE;QAAE,gBAAgB;MAAlB;IADqE,CAA9D,CAAlB;;IAIA,IAAIF,GAAG,CAACX,IAAJ,CAASc,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAOH,GAAG,CAACX,IAAJ,CAASA,IAAhB;IACD;;IACD,OAAOe,OAAO,CAACC,MAAR,CAAe,IAAIC,KAAJ,CAAUN,GAAG,CAACX,IAAJ,CAASkB,OAAT,IAAoB,UAA9B,CAAf,CAAP;EACD,CATD,CASE,OAAOI,KAAP,EAAc;IACdnB,OAAO,CAACmB,KAAR,CAAc,qBAAd,EAAqCA,KAArC;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD;AACF;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAeC,aAAf,CAA6BvB,IAA7B,EAAmC;EACxC,IAAI;IACF,MAAMW,GAAG,GAAG,MAAMlB,OAAO,CAACmB,IAAR,CAAa,sCAAb,EAAqDZ,IAArD,EAA2D;MAC3Ea,OAAO,EAAE;QAAE,gBAAgB;MAAlB;IADkE,CAA3D,CAAlB;;IAIA,IAAIF,GAAG,CAACX,IAAJ,CAASc,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAOH,GAAG,CAACX,IAAJ,CAASA,IAAhB;IACD;;IACD,OAAOe,OAAO,CAACC,MAAR,CAAe,IAAIC,KAAJ,CAAUN,GAAG,CAACX,IAAJ,CAASkB,OAAT,IAAoB,YAA9B,CAAf,CAAP;EACD,CATD,CASE,OAAOI,KAAP,EAAc;IACdnB,OAAO,CAACmB,KAAR,CAAc,oBAAd,EAAoCA,KAApC;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD;AACF;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAeE,wBAAf,CAAwCxB,IAAxC,EAA8C;EACnD,IAAI;IACF,MAAMW,GAAG,GAAG,MAAMlB,OAAO,CAACmB,IAAR,CAAa,sDAAb,EAAqEZ,IAArE,EAA2E;MAC3Fa,OAAO,EAAE;QAAE,gBAAgB;MAAlB;IADkF,CAA3E,CAAlB;;IAIA,IAAIF,GAAG,CAACX,IAAJ,CAASc,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAOH,GAAG,CAACX,IAAJ,CAASA,IAAhB;IACD;;IACD,OAAOe,OAAO,CAACC,MAAR,CAAe,IAAIC,KAAJ,CAAUN,GAAG,CAACX,IAAJ,CAASkB,OAAT,IAAoB,eAA9B,CAAf,CAAP;EACD,CATD,CASE,OAAOI,KAAP,EAAc;IACdnB,OAAO,CAACmB,KAAR,CAAc,+BAAd,EAA+CA,KAA/C;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD;AACF;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAeG,qBAAf,CAAqCzB,IAArC,EAA2C;EAChD,IAAI;IACF,MAAMW,GAAG,GAAG,MAAMlB,OAAO,CAACmB,IAAR,CAAa,mDAAb,EAAkEZ,IAAlE,EAAwE;MACxFa,OAAO,EAAE;QAAE,gBAAgB;MAAlB;IAD+E,CAAxE,CAAlB;;IAIA,IAAIF,GAAG,CAACX,IAAJ,CAASc,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAOH,GAAG,CAACX,IAAJ,CAASA,IAAhB;IACD;;IACD,OAAOe,OAAO,CAACC,MAAR,CAAe,IAAIC,KAAJ,CAAUN,GAAG,CAACX,IAAJ,CAASkB,OAAT,IAAoB,kBAA9B,CAAf,CAAP;EACD,CATD,CASE,OAAOI,KAAP,EAAc;IACdnB,OAAO,CAACmB,KAAR,CAAc,4BAAd,EAA4CA,KAA5C;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD;AACF;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAeI,yBAAf,CAAyC1B,IAAzC,EAA+C;EACpD,IAAI;IACF,MAAMW,GAAG,GAAG,MAAMlB,OAAO,CAACmB,IAAR,CAAa,uDAAb,EAAsEZ,IAAtE,EAA4E;MAC5Fa,OAAO,EAAE;QAAE,gBAAgB;MAAlB;IADmF,CAA5E,CAAlB;;IAIA,IAAIF,GAAG,CAACX,IAAJ,CAASc,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAOH,GAAG,CAACX,IAAJ,CAASA,IAAhB;IACD;;IACD,OAAOe,OAAO,CAACC,MAAR,CAAe,IAAIC,KAAJ,CAAUN,GAAG,CAACX,IAAJ,CAASkB,OAAT,IAAoB,iBAA9B,CAAf,CAAP;EACD,CATD,CASE,OAAOI,KAAP,EAAc;IACdnB,OAAO,CAACmB,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD;AACF;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAeK,mBAAf,CAAmC3B,IAAnC,EAAyC;EAC9C,IAAI;IACF,MAAMW,GAAG,GAAG,MAAMlB,OAAO,CAACmB,IAAR,CAAa,iDAAb,EAAgEZ,IAAhE,EAAsE;MACtFa,OAAO,EAAE;QAAE,gBAAgB;MAAlB;IAD6E,CAAtE,CAAlB;;IAIA,IAAIF,GAAG,CAACX,IAAJ,CAASc,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAOH,GAAG,CAACX,IAAJ,CAASA,IAAhB;IACD;;IACD,OAAOe,OAAO,CAACC,MAAR,CAAe,IAAIC,KAAJ,CAAUN,GAAG,CAACX,IAAJ,CAASkB,OAAT,IAAoB,eAA9B,CAAf,CAAP;EACD,CATD,CASE,OAAOI,KAAP,EAAc;IACdnB,OAAO,CAACmB,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD;AACF;AAED;AACA;AACA;;AACA,OAAO,SAASM,iBAAT,GAA6B;EAClC,OAAO;IACL,GAAG;MAAEC,IAAI,EAAE,IAAR;MAAcC,IAAI,EAAE;IAApB,CADE;IAEL,GAAG;MAAED,IAAI,EAAE,KAAR;MAAeC,IAAI,EAAE;IAArB,CAFE;IAGL,GAAG;MAAED,IAAI,EAAE,MAAR;MAAgBC,IAAI,EAAE;IAAtB,CAHE;IAIL,GAAG;MAAED,IAAI,EAAE,KAAR;MAAeC,IAAI,EAAE;IAArB,CAJE;IAKL,GAAG;MAAED,IAAI,EAAE,KAAR;MAAeC,IAAI,EAAE;IAArB,CALE;IAML,CAAC,CAAC,CAAF,GAAM;MAAED,IAAI,EAAE,KAAR;MAAeC,IAAI,EAAE;IAArB,CAND;IAOL,CAAC,CAAC,CAAF,GAAM;MAAED,IAAI,EAAE,QAAR;MAAkBC,IAAI,EAAE;IAAxB,CAPD;IAQL,CAAC,CAAC,CAAF,GAAM;MAAED,IAAI,EAAE,QAAR;MAAkBC,IAAI,EAAE;IAAxB,CARD;IASL,CAAC,CAAC,CAAF,GAAM;MAAED,IAAI,EAAE,QAAR;MAAkBC,IAAI,EAAE;IAAxB,CATD;IAUL,CAAC,CAAC,CAAF,GAAM;MAAED,IAAI,EAAE,UAAR;MAAoBC,IAAI,EAAE;IAA1B,CAVD;IAWL,CAAC,CAAC,CAAF,GAAM;MAAED,IAAI,EAAE,WAAR;MAAqBC,IAAI,EAAE;IAA3B,CAXD;IAYL,CAAC,CAAC,CAAF,GAAM;MAAED,IAAI,EAAE,UAAR;MAAoBC,IAAI,EAAE;IAA1B;EAZD,CAAP;AAcD;AAED;AACA;AACA;;AACA,OAAO,SAASC,mBAAT,GAA+B;EACpC,OAAO;IACL,GAAG;MAAEF,IAAI,EAAE,KAAR;MAAeC,IAAI,EAAE;IAArB,CADE;IAEL,GAAG;MAAED,IAAI,EAAE,KAAR;MAAeC,IAAI,EAAE;IAArB,CAFE;IAGL,GAAG;MAAED,IAAI,EAAE,UAAR;MAAoBC,IAAI,EAAE;IAA1B,CAHE;IAILE,IAAI,EAAE;MAAEH,IAAI,EAAE,KAAR;MAAeC,IAAI,EAAE;IAArB,CAJD;IAKLtB,SAAS,EAAE;MAAEqB,IAAI,EAAE,KAAR;MAAeC,IAAI,EAAE;IAArB;EALN,CAAP;AAOD,C,CAED;;AACA,OAAO,SAASG,aAAT,CAAuBjC,IAAvB,EAA6B;EAClC,OAAOP,OAAO,CAAC;IACbyC,GAAG,EAAE,yCADQ;IAEbC,MAAM,EAAE,MAFK;IAGbnC,IAAI,EAAEA;EAHO,CAAD,CAAP,CAIJoC,IAJI,CAICzB,GAAG,IAAI;IACb,OAAOA,GAAG,CAACX,IAAX;EACD,CANM,EAMJqC,KANI,CAMEf,KAAK,IAAI;IAChBnB,OAAO,CAACmB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD,CATM,CAAP;AAUD;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASgB,eAAT,CAAyBtC,IAAzB,EAA+B;EACpC;EACA,IAAIkC,GAAG,GAAI,6CAAX,CAFoC,CAIpC;;EACA,IAAIK,MAAM,GAAG;IACXC,YAAY,EAAE,MADH;IAEX3B,OAAO,EAAE;MACP,gBAAgB,kBADT;MAEP,iBAAiB,YAAYjB,QAAQ;IAF9B;EAFE,CAAb;EAQA,OAAOH,OAAO,CAACmB,IAAR,CAAasB,GAAb,EAAkBlC,IAAI,IAAI,EAA1B,EAA8BuC,MAA9B,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASE,qBAAT,CAA+BC,OAA/B,EAAwC;EAC7C,OAAOjD,OAAO,CAAC;IACbyC,GAAG,EAAE,mDADQ;IAEbC,MAAM,EAAE,MAFK;IAGbnC,IAAI,EAAE;MAAE0C;IAAF;EAHO,CAAD,CAAd;AAKD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,gBAAT,CAA0BC,mBAA1B,EAA+C;EACpD,OAAOnD,OAAO,CAAC;IACbyC,GAAG,EAAE,oDADQ;IAEbC,MAAM,EAAE,MAFK;IAGbnC,IAAI,EAAE;MAAE4C;IAAF;EAHO,CAAD,CAAd;AAKD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,gBAAT,CAA0B7C,IAA1B,EAAgC;EACrC,OAAOP,OAAO,CAAC;IACbyC,GAAG,EAAE,8CADQ;IAEbC,MAAM,EAAE,MAFK;IAGbnC,IAAI,EAAEA;EAHO,CAAD,CAAP,CAIJoC,IAJI,CAICzB,GAAG,IAAI;IACb,OAAOA,GAAG,CAACX,IAAX;EACD,CANM,EAMJqC,KANI,CAMEf,KAAK,IAAI;IAChBnB,OAAO,CAACmB,KAAR,CAAc,aAAd,EAA6BA,KAA7B;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD,CATM,CAAP;AAUD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASwB,wBAAT,CAAkC9C,IAAlC,EAAwC;EAC7C,OAAOP,OAAO,CAAC;IACbyC,GAAG,EAAE,sDADQ;IAEbC,MAAM,EAAE,MAFK;IAGbnC,IAAI,EAAEA;EAHO,CAAD,CAAP,CAIJoC,IAJI,CAICzB,GAAG,IAAI;IACb,OAAOA,GAAG,CAACX,IAAX;EACD,CANM,EAMJqC,KANI,CAMEf,KAAK,IAAI;IAChBnB,OAAO,CAACmB,KAAR,CAAc,eAAd,EAA+BA,KAA/B;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD,CATM,CAAP;AAUD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASyB,wBAAT,CAAkC/C,IAAlC,EAAwC;EAC7C,OAAOP,OAAO,CAAC;IACbyC,GAAG,EAAE,sDADQ;IAEbC,MAAM,EAAE,MAFK;IAGbnC,IAAI,EAAEA;EAHO,CAAD,CAAP,CAIJoC,IAJI,CAICzB,GAAG,IAAI;IACb,OAAOA,GAAG,CAACX,IAAX;EACD,CANM,EAMJqC,KANI,CAMEf,KAAK,IAAI;IAChBnB,OAAO,CAACmB,KAAR,CAAc,eAAd,EAA+BA,KAA/B;IACA,OAAOP,OAAO,CAACC,MAAR,CAAeM,KAAf,CAAP;EACD,CATM,CAAP;AAUD"}, "metadata": {}, "sourceType": "module"}