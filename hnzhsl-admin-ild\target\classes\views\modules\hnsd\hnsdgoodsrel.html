<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">
		<div class="grid-btn">
			<a v-if="hasPermission('hnsdgoodsrel:save')" class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
			<a v-if="hasPermission('hnsdgoodsrel:update')" class="btn btn-primary" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
			<a v-if="hasPermission('hnsdgoodsrel:delete')" class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a>
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
    
    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
											<div class="form-group">
			   	<div class="col-sm-2 control-label">关联类型（1:可选包,2:优惠包，3：叠加包 4：100元预存 5:200元预存 6:300元预存 7：X元预存）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdGoodsRel.relType" placeholder="关联类型（1:可选包,2:优惠包，3：叠加包 4：100元预存 5:200元预存 6:300元预存 7：X元预存）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">关联商品说明</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdGoodsRel.goodsNumberRelMsg" placeholder="关联商品说明"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">当前状态（1：上架，0：下架）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdGoodsRel.status" placeholder="当前状态（1：上架，0：下架）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">关联商品编码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdGoodsRel.goodsNumberRel" placeholder="关联商品编码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">商品编码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdGoodsRel.goodsNumber" placeholder="商品编码"/>
			    </div>
			</div>
							<div class="form-group">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div>
		</form>
	</div>
</div>

<script src="../../js/modules/hnsd/hnsdgoodsrel.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>