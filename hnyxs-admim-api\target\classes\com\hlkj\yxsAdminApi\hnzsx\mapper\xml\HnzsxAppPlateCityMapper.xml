<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppPlateCityMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        select
            a.*
        from  hnzsx_plate_city a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.plateType != null">
                AND a.PLATE_TYPE = #{param.plateType}
            </if>
            <if test="param.plateName != null">
                AND a.PLATE_NAME = #{param.plateName}
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE = #{param.cityCode}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
        </where>
    </sql>


    <resultMap id="dataMap" type="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxPlateCity">
        <id property="id" column="id"></id>
        <result property="plateType" column="PLATE_TYPE"></result>
        <result property="plateName" column="PLATE_NAME"></result>
        <result property="cityCode" column="CITY_CODE"></result>
        <result property="cityName" column="CITY_NAME"></result>
        <result property="createdDate" column="CREATED_DATE"></result>
        <result property="updatedDate" column="UPDATED_DATE"></result>
        <collection property="modules"  javaType="list" ofType="map" column="id" select="queryModules"></collection>
    </resultMap>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultMap="dataMap">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询关联模块信息 -->
    <select id="queryModules" resultType="map" parameterType="Integer">
        select
            pm.ID as plateModuleId,
            pm.STATUS as plateModuleStatus,
            pm.READING_PERMISSION as readingPermission,
            pm.USER_INFO_STATUS as userInfoStatus,
            pm.ONE_PROVE_TEN_KA as oneProveTenKa,
            am.MODULE_TYPE_NAME as moduleTypeName,
            am.MODULE_NAME as moduleName,
            am.MODULE_CODE as moduleCode,
            am.IMG as img
        from
            hnzsx_plate_module pm
        inner join hnzsx_module am on
            pm.APP_MODULE_ID = am.ID
        where
            pm.APP_PLATE_CITY_ID = #{id}
        order by am.MODULE_TYPE_CODE
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxPlateCity">
        <include refid="selectSql"></include>
    </select>

</mapper>
