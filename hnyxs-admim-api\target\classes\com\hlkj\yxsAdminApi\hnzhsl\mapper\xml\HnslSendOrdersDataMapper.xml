<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslSendOrdersDataMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_send_orders_data a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.publicNumber != null">
                AND a.PUBLIC_NUMBER LIKE CONCAT('%', #{param.publicNumber}, '%')
            </if>
            <if test="param.touchId != null">
                AND a.TOUCH_ID LIKE CONCAT('%', #{param.touchId}, '%')
            </if>
            <if test="param.activityLogo != null">
                AND a.ACTIVITY_LOGO LIKE CONCAT('%', #{param.activityLogo}, '%')
            </if>
            <if test="param.outgoingNumber1 != null">
                AND a.OUTGOING_NUMBER1 LIKE CONCAT('%', #{param.outgoingNumber1}, '%')
            </if>
            <if test="param.outgoingNumber2 != null">
                AND a.OUTGOING_NUMBER2 LIKE CONCAT('%', #{param.outgoingNumber2}, '%')
            </if>
            <if test="param.outgoingNumber3 != null">
                AND a.OUTGOING_NUMBER3 LIKE CONCAT('%', #{param.outgoingNumber3}, '%')
            </if>
            <if test="param.recommendedPackage != null">
                AND a.RECOMMENDED_PACKAGE LIKE CONCAT('%', #{param.recommendedPackage}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.ordersStatus != null">
                AND a.ORDERS_STATUS = #{param.ordersStatus}
            </if>
            <if test="param.ordersLanId != null">
                AND a.ORDERS_LAN_ID LIKE CONCAT('%', #{param.ordersLanId}, '%')
            </if>
            <if test="param.schoolSixId != null">
                AND a.SCHOOL_SIX_ID LIKE CONCAT('%', #{param.schoolSixId}, '%')
            </if>
            <if test="param.mainPackageName != null">
                AND a.MAIN_PACKAGE_NAME LIKE CONCAT('%', #{param.mainPackageName}, '%')
            </if>
            <if test="param.monthBalance != null">
                AND a.MONTH_BALANCE LIKE CONCAT('%', #{param.monthBalance}, '%')
            </if>
            <if test="param.userSex != null">
                AND a.USER_SEX LIKE CONCAT('%', #{param.userSex}, '%')
            </if>
            <if test="param.userAge != null">
                AND a.USER_AGE LIKE CONCAT('%', #{param.userAge}, '%')
            </if>
            <if test="param.amountOwed != null">
                AND a.AMOUNT_OWED LIKE CONCAT('%', #{param.amountOwed}, '%')
            </if>
            <if test="param.packageGrade != null">
                AND a.PACKAGE_GRADE LIKE CONCAT('%', #{param.packageGrade}, '%')
            </if>
            <if test="param.timeStorage != null">
                AND a.TIME_STORAGE LIKE CONCAT('%', #{param.timeStorage}, '%')
            </if>
            <if test="param.trafficUsage != null">
                AND a.TRAFFIC_USAGE LIKE CONCAT('%', #{param.trafficUsage}, '%')
            </if>
            <if test="param.voiceUsage != null">
                AND a.VOICE_USAGE LIKE CONCAT('%', #{param.voiceUsage}, '%')
            </if>
            <if test="param.sixLevelName != null">
                AND a.SIX_LEVEL_NAME LIKE CONCAT('%', #{param.sixLevelName}, '%')
            </if>
            <if test="param.sixLevelOrganization != null">
                AND a.SIX_LEVEL_ORGANIZATION LIKE CONCAT('%', #{param.sixLevelOrganization}, '%')
            </if>
            <if test="param.operatorAircraft != null">
                AND a.OPERATOR_AIRCRAFT LIKE CONCAT('%', #{param.operatorAircraft}, '%')
            </if>
            <if test="param.machineNumbe != null">
                AND a.MACHINE_NUMBE LIKE CONCAT('%', #{param.machineNumbe}, '%')
            </if>
            <if test="param.accessDate != null">
                AND a.ACCESS_DATE LIKE CONCAT('%', #{param.accessDate}, '%')
            </if>
            <if test="param.userName != null">
                AND a.USER_NAME LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.ordersDate != null">
                AND a.ORDERS_DATE LIKE CONCAT('%', #{param.ordersDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrdersData">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrdersData">
        <include refid="selectSql"></include>
    </select>

</mapper>
