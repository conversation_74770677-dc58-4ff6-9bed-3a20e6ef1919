<!DOCTYPE html>
<html>
<head>
    <title>白名单申请</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../js/common.js"></script>

    <style type="text/css">
        #loading {
            position: fixed;
            z-index: 1000;
            top: 0;
            left: 0;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 38px;
            height: 38px;
            border-radius: 50%;
            border-top-color: #000;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showList">
        <div class="row">
            <div class="form-group col-md-2" style="height: 40px">
                <label>批次号:</label>
                <input type="text" class="form-control" placeholder="请输入批次号" v-model="hnslWhiteApply.batchCode"/>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>文件名:</label>
                <input type="text" class="form-control" placeholder="请输入文件名" v-model="hnslWhiteApply.fileName"/>
            </div>
            <div class="form-group col-md-2" style="height: 32px;">
                <!--                <label>申请日期:</label>-->
                <!--                <div class="input-group col-ms-2 ">-->
                <!--                    <input class="form-control pull-left dateRange date-picker "-->
                <!--                           id="dateTimeRange" @keyup.enter="query" value="" type="text"-->
                <!--                           placeholder="请选择申请日期">-->
                <!--                        <span class="input-group-addon">-->
                <!--							<i class="fa fa-calendar bigger-110"></i>-->
                <!-- 						</span>-->
                <!--                </div>-->
                <label>申请日期:</label>
                <div class="input-group col-ms-2">
                    <input class="form-control pull-left date-picker"
                           id="dateTimeRange" @keyup.enter="query" value="" type="text"
                           placeholder="请选择申请日期">
                    <span class="input-group-addon">
                        <i class="fa fa-calendar bigger-110"></i>
                    </span>
                </div>
            </div>
            <div class="form-group col-md-2">
                <label><span style="color: red">仅展示一年内的上传记录</span></label>
            </div>
        </div>
        <div class="grid-btn" style="margin-left: 17Px; margin-top: 18px">
            <a v-if="hasPermission('hnslwhiteapply:query')" class="btn btn-primary" @click="query"><i></i>&nbsp;查询</a>
            <a v-if="hasPermission('hnslwhiteapply:output')" class="btn btn-primary" @click="uploadApply($event,'1')">
                <i></i>&nbsp;上传</a>
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>

    <!--模板下载-->
    <!--    <div v-show="!templateShow" id="templateShow"-->
    <!--         class="panel panel-default">-->
    <!--        <div class="panel-heading">{{title}}</div>-->
    <!--        <div class="form-horizontal" style="padding-top: 0px; width: 100%;">-->
    <!--            <form id="uploadImg" enctype="multipart/form-data">-->
    <!--                <div class="templateShow-Info">-->
    <!--                    <p>下载模板：</p>-->
    <!--                    <p>为提高导入的成功率，请下载并使用系统提供的模板:</p>-->
    <!--                </div>-->
    <!--                <div style="margin-left: 125px;">-->
    <!--                    <a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>-->
    <!--                </div>-->
    <!--                <div class="templateShow-Info">-->
    <!--                    <p>上传文件：</p>-->
    <!--                    <p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>-->
    <!--                </div>-->
    <!--                <div style="margin-left: 125px;">-->
    <!--                    <a v-if="hasPermission('hnsduser:importUser')"-->
    <!--                       class="btn btn-primary" @click="importUser">&nbsp;开始导入</a>-->
    <!--                    <input-->
    <!--                        style="display: none;" name="uploadFile" id="uploadFile"-->
    <!--                        type="file" @change="uploadFile" />-->
    <!--                </div>-->
    <!--                <div style="width: 100%; text-align: center;">-->
    <!--                    <input type="button" class="btn btn-warning" @click="reload"-->
    <!--                           value="返回" />-->
    <!--                </div>-->
    <!--            </form>-->
    <!--        </div>-->
    <!--    </div>-->

    <!--上传白名单申请弹窗-->
    <div class="modal" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true">×
                    </button>
                    <h4 class="modal-title" id="myModalLabel" style="text-align: center">上传</h4>
                </div>
                <div class="form-horizontal" style="padding-top: 0px; width: 100%;">
                    <form id="uploadImg" enctype="multipart/form-data">
                        <div style="padding-left: 30px; font-size: 12px;">
                            <div class="modal-body" style="display: flex;flex-direction: column">
                                <label style="margin-right: 10px;margin-bottom: 0;"><span
                                        style="color: red;margin-right: 5px">*</span>白名单申请列表(支持.xlsx, .xls)</label>
                                <div class="input-group col-ms-2 " style="margin-top: 10px">
                                    <input style="display: none;" name="uploadFile" type="file" id="whiteListFile"
                                           accept=".xlsx, .xls">
                                    <div id="whiteListPreview" class="file-preview"></div>
                                </div>
                                <div style="margin-top: 5px">
                                    <div class="apply"></div>
                                    <a class="btn btn-primary" style="height: 30px;" @click="uploadWhiteFile(1)">上传</a>
                                    <a class="btn btn-primary" style="height: 30px;" @click="getTemplate">下载模板</a>
                                </div>
                            </div>

                            <div class="modal-body" style="display: flex;flex-direction: column">
                                <label style="margin-right: 10px;margin-bottom: 0;"><span
                                        style="color: red;margin-right: 5px">*</span>审批报告(支持jpg, png)</label>
                                <div class="input-group col-ms-2 " style="margin-top: 10px">
                                    <input type="file" id="approvalReportFile" accept=".jpg, .png"
                                           style="display: none;">
                                    <div id="approvalReportPreview" class="file-preview"></div>
                                </div>
                                <div style="margin-top: 5px">
                                    <a class="btn btn-primary" style="height: 30px;" @click="uploadWhiteFile(2)">上传</a>
                                </div>
                            </div>

                            <div class="modal-body" style="display: flex;flex-direction: column">
                                <label style="margin-right: 10px;margin-bottom: 0;">备注</label>
                                <div style="margin-top: 5px">
                                    <textarea v-model="hnslWhiteApply.remark"
                                              placeholder="请输入您需要备注的信息(48个字符以内)"
                                              maxlength="48"
                                              resize="none"
                                              style="width: 80%; height: 70px;"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div style="padding: 15px; text-align: center; border-top: 1px solid #e5e5e5;">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                            style="width: 80px;background-color: #ccc;" @click="closeResult">关闭
                    </button>
                    <a class="btn btn-primary" @click="uploadResult" style="width: 80px">确认</a>
                </div>
            </div>
        </div>
    </div>


    <!--审批人列表-->
    <div class="modal" id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" style="padding: 70px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true">×
                    </button>
                    <h4 class="modal-title" id="myModalLabel1" style="text-align: center">审批人列表</h4>
                    <table class="table table-bordered" style="width: 100%; text-align: center;margin-top: 10px">
                        <tbody id="myTable">
                        <tr>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                    <div style="width: 100%;text-align: center;">
                        <a href="#" id="prevPage" @click="prevPage">上一页</a>
                        <span id="currentPage">1</span>
                        <a href="#" id="nextPage" @click="nextPage">下一页</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--审批人列表-->
    <div class="modal" id="myModal2" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" style="padding: 120px">
            <div class="modal-content" style="width: 300px;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true">×
                    </button>
                    <h5 class="modal-title" id="myModalLabel2" style="text-align: left">信息</h5>
                    <div style="padding-top: 20px; text-align: left;display: flex;justify-content: space-around;flex-direction: column">
                        <div style="">确认要申请提交审批?</div>
                        <div style="padding-top: 30px;text-align: right;">
                            <button type="button" class="btn btn-default" data-dismiss="modal"
                                    style="width: 50px;line-height: 28px;height: 28px;background-color: rgb(204, 204, 204);font-size: 13px;padding: 0 12px;">
                                关闭
                            </button>
                            <a class="btn btn-primary"
                               style="width: 50px;line-height: 28px;height: 28px;font-size: 13px;padding: 0 12px;"
                               @click="applyApproval">确定</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="position:relative;display: none" id="loading">
        <div class="spinner" style="position: absolute; bottom: 350px; text-align: center; z-index: 999;  color: red;font-size: 18px;"></div>
    </div>
</div>

<script src="../../js/modules/hnsl/hnslWhiteApply.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>