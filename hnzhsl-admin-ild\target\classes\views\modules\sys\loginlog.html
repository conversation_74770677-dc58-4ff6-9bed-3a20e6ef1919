<!DOCTYPE html>
<html>
<head>
<title>登录日志</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<link rel="stylesheet" href="../../plugins/Daterangepicker/css/daterangepicker.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>
<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<script src="../../js/common.js"></script>
<style>
    .filter-row {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 10px;
    }
    .filter-item {
        display: flex;
        align-items: center;
        margin-right: 10px;
        margin-bottom: 5px;
    }
    .filter-label {
        margin-right: 5px;
        white-space: nowrap;
    }
    .filter-control {
        width: 140px;
    }
    .date-range-control {
        width: 220px;
    }
    @media (max-width: 992px) {
        .filter-control {
            width: 120px;
        }
        .date-range-control {
            width: 180px;
        }
    }
    @media (max-width: 768px) {
        .filter-row {
            flex-direction: column;
            align-items: flex-start;
        }
        .filter-item {
            width: 100%;
            margin-right: 0;
        }
        .filter-control, .date-range-control {
            width: 100%;
        }
    }
</style>
</head>
<body>
<div id="rrapp" v-cloak>
    <div>
        <div class="grid-btn">
            <div class="filter-row">
                <div class="filter-item">
                    <label class="filter-label">用户名：</label>
                    <input type="text" class="form-control filter-control" v-model="q.username" @keyup.enter="query" placeholder="用户名">
                </div>
                <div class="filter-item">
                    <label class="filter-label">状态：</label>
                    <select class="form-control filter-control" v-model="q.status">
                        <option value="">全部</option>
                        <option value="1">成功</option>
                        <option value="0">失败</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">登录方式：</label>
                    <select class="form-control filter-control" v-model="q.loginType">
                        <option value="">全部</option>
                        <option value="0">退出登录</option>
                        <option value="1">账号密码登录</option>
                        <option value="2">手机验证码</option>
                        <option value="3">第三方登录</option>
                        <option value="4">密码重置</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">时间范围：</label>
                    <input id="dateRange" type="text" class="form-control date-range-control" placeholder="点击选择时间范围">
                </div>
                <div class="filter-item">
                    <button class="btn btn-default" @click="query">查询</button>
                    <button class="btn btn-warning" @click="reset" style="margin-left:5px;">重置</button>
                    <button v-if="hasPermission('sys:loginlog:clean')" class="btn btn-danger" @click="clean" style="margin-left:5px;">清理历史日志</button>
                </div>
            </div>
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>
</div>

<script src="../../js/modules/sys/loginlog.js"></script>
</body>
</html> 