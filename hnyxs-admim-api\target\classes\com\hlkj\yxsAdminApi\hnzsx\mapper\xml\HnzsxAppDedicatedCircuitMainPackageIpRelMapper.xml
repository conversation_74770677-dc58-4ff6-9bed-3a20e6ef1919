<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppDedicatedCircuitMainPackageIpRelMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_dedicated_circuit_main_package_ip_rel a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.mainPackageId != null">
                AND a.MAIN_PACKAGE_ID = #{param.mainPackageId}
            </if>
            <if test="param.ipId != null">
                AND a.IP_ID = #{param.ipId}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.UPDATE_DATE LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitMainPackageIpRel">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitMainPackageIpRel">
        <include refid="selectSql"></include>
    </select>

</mapper>
