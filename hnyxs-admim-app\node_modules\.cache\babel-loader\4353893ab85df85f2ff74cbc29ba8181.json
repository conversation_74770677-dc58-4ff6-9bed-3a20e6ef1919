{"ast": null, "code": "import \"core-js/modules/es.array.sort.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/es.array.reduce.js\";\nimport * as echarts from 'echarts';\nimport { getOrderCount, getDailyOrdersByCityCode, getOrderCountByModule, getOrderCountByModuleType } from '@/api/hnzsxH5/order';\nexport default {\n  name: 'OrderStatistics',\n\n  data() {\n    return {\n      dateRange: [this.formatDate(new Date()), this.formatDate(new Date())],\n      // 默认今天到今天\n      tableData: [],\n      chartInstance: null,\n      trendChartInstance: null,\n      dailyOrdersChartInstance: null,\n      dailyOrdersData: [],\n      totalOrders: 0,\n      moduleTableData: [],\n      // 按模块分类的表格数据\n      moduleList: [],\n      // 模块列表\n      moduleTypeTableData: [],\n      // 按模块类型汇总的表格数据\n      dailyOrdersDialogVisible: false,\n      // 每日订单数量弹窗显示状态\n      selectedCity: null,\n      // 当前选中的城市\n      dailyOrdersTrendChartInstance: null,\n      // 订单数量走势图实例\n      cityMap: {\n        '730': '岳阳',\n        '731': '长沙',\n        '732': '湘潭',\n        '733': '株洲',\n        '734': '衡阳',\n        '735': '郴州',\n        '736': '常德',\n        '737': '益阳',\n        '738': '娄底',\n        '739': '邵阳',\n        '743': '湘西',\n        '744': '张家界',\n        '745': '怀化',\n        '746': '永州'\n      }\n    };\n  },\n\n  computed: {\n    // 图表所需数据\n    chartData() {\n      return this.tableData.map(item => ({\n        name: this.getCityName(item.cityCode),\n        value: item.saleNum\n      }));\n    }\n\n  },\n\n  mounted() {\n    this.initChart();\n    this.initTrendChart();\n    this.fetchData();\n    this.fetchDailyOrdersData();\n    this.fetchModuleData();\n    this.fetchModuleTypeData(); // 监听窗口大小变化，重绘图表\n\n    window.addEventListener('resize', this.resizeCharts);\n  },\n\n  beforeDestroy() {\n    // 移除事件监听\n    window.removeEventListener('resize', this.resizeCharts); // 销毁图表实例\n\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n      this.chartInstance = null;\n    }\n\n    if (this.trendChartInstance) {\n      this.trendChartInstance.dispose();\n      this.trendChartInstance = null;\n    }\n\n    if (this.dailyOrdersChartInstance) {\n      this.dailyOrdersChartInstance.dispose();\n      this.dailyOrdersChartInstance = null;\n    }\n\n    if (this.dailyOrdersTrendChartInstance) {\n      this.dailyOrdersTrendChartInstance.dispose();\n      this.dailyOrdersTrendChartInstance = null;\n    }\n  },\n\n  methods: {\n    // 初始化图表\n    initChart() {\n      this.chartInstance = echarts.init(this.$refs.chartContainer);\n      this.updateChart();\n    },\n\n    // 初始化趋势图表\n    initTrendChart() {\n      this.trendChartInstance = echarts.init(this.$refs.trendChartContainer);\n      this.updateTrendChart();\n    },\n\n    // 更新图表数据\n    updateChart() {\n      if (!this.chartInstance) return;\n      const option = {\n        title: {\n          text: '各地市订单数量统计',\n          left: 'center'\n        },\n        tooltip: {\n          trigger: 'item'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          data: this.chartData.map(item => item.name)\n        },\n        series: [{\n          name: '订单数量',\n          type: 'pie',\n          radius: '50%',\n          data: this.chartData,\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          }\n        }]\n      };\n      this.chartInstance.setOption(option);\n    },\n\n    // 更新趋势图表数据\n    updateTrendChart() {\n      if (!this.trendChartInstance) return; // 处理数据，按日期和城市分组\n\n      const dates = [...new Set(this.dailyOrdersData.map(item => item.date))].sort();\n      const cities = [...new Set(this.dailyOrdersData.map(item => item.cityCode))].sort(); // 准备每个城市的数据系列\n\n      const series = cities.map(cityCode => {\n        // 筛选该城市的数据\n        const cityData = dates.map(date => {\n          const item = this.dailyOrdersData.find(d => d.date === date && d.cityCode === cityCode);\n          return item ? item.orderCount : 0;\n        });\n        return {\n          name: this.getCityName(cityCode),\n          type: 'line',\n          data: cityData,\n          smooth: true\n        };\n      });\n      const option = {\n        title: {\n          text: '各地市每日订单走势图',\n          left: 'center'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: cities.map(cityCode => this.getCityName(cityCode)),\n          type: 'scroll',\n          orient: 'horizontal',\n          bottom: 0\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '15%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: dates,\n          axisLabel: {\n            rotate: 45,\n            interval: 0\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '订单数量'\n        },\n        series: series\n      };\n      this.trendChartInstance.setOption(option);\n    },\n\n    // 窗口大小变化时重绘图表\n    resizeCharts() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n\n      if (this.trendChartInstance) {\n        this.trendChartInstance.resize();\n      }\n\n      if (this.dailyOrdersChartInstance && this.dailyOrdersDialogVisible) {\n        this.dailyOrdersChartInstance.resize();\n      }\n\n      if (this.dailyOrdersTrendChartInstance && this.dailyOrdersDialogVisible) {\n        this.dailyOrdersTrendChartInstance.resize();\n      }\n    },\n\n    // 获取统计数据\n    async fetchData() {\n      try {\n        // 如果没有选择日期范围，使用默认值（今天）\n        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());\n        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());\n        const data = await getOrderCount({\n          date: startDate,\n          endDate: endDate\n        });\n        this.tableData = data || [];\n        this.totalOrders = this.tableData.reduce((sum, item) => sum + (parseInt(item.saleNum) || 0), 0);\n        this.$nextTick(() => {\n          this.updateChart();\n        });\n      } catch (error) {\n        this.$message.error('获取统计数据失败: ' + error.message);\n      }\n    },\n\n    // 获取各地市每日订单数据\n    async fetchDailyOrdersData() {\n      try {\n        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());\n        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());\n        const data = await getDailyOrdersByCityCode({\n          date: startDate,\n          endDate: endDate\n        });\n        this.dailyOrdersData = data || [];\n        this.$nextTick(() => {\n          this.updateTrendChart();\n        });\n      } catch (error) {\n        this.$message.error('获取每日订单数据失败: ' + error.message);\n      }\n    },\n\n    // 获取按模块分类的订单数据\n    async fetchModuleData() {\n      try {\n        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());\n        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());\n        const result = await getOrderCountByModule({\n          date: startDate,\n          endDate: endDate\n        }); // 设置模块列表\n\n        this.moduleList = result.modules || []; // 设置表格数据\n\n        this.moduleTableData = result.cityData || []; // 计算总订单数\n\n        this.totalOrders = this.moduleTableData.reduce((sum, city) => sum + (city.totalCount || 0), 0);\n      } catch (error) {\n        this.$message.error('获取按模块分类的订单数据失败: ' + error.message);\n      }\n    },\n\n    // 获取按模块类型汇总的订单数据\n    async fetchModuleTypeData() {\n      try {\n        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());\n        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());\n        const result = await getOrderCountByModuleType({\n          date: startDate,\n          endDate: endDate\n        }); // 设置模块类型汇总表格数据\n\n        this.moduleTypeTableData = result || [];\n      } catch (error) {\n        this.$message.error('获取按模块类型汇总的订单数据失败: ' + error.message);\n      }\n    },\n\n    // 获取指定城市和模块的订单数\n    getModuleCount(row, moduleId) {\n      if (!row.modules || !row.modules[moduleId]) {\n        return 0;\n      }\n\n      return row.modules[moduleId];\n    },\n\n    // 日期变化事件\n    dateChange(val) {\n      if (val) {\n        this.dateRange = val;\n      } else {\n        this.dateRange = [this.formatDate(new Date()), this.formatDate(new Date())];\n      }\n\n      this.fetchData();\n      this.fetchDailyOrdersData();\n      this.fetchModuleData();\n      this.fetchModuleTypeData();\n    },\n\n    // 获取城市名称\n    getCityName(cityCode) {\n      return this.cityMap[cityCode] || cityCode;\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = ('0' + (date.getMonth() + 1)).slice(-2);\n      const day = ('0' + date.getDate()).slice(-2);\n      return `${year}-${month}-${day}`;\n    },\n\n    // 显示每日订单数量图表\n    showDailyOrdersChart(city) {\n      this.selectedCity = city;\n      this.dailyOrdersDialogVisible = true; // 下一帧初始化图表\n\n      this.$nextTick(() => {\n        // 初始化柱状图\n        if (this.dailyOrdersChartInstance) {\n          this.dailyOrdersChartInstance.dispose();\n        }\n\n        this.dailyOrdersChartInstance = echarts.init(this.$refs.dailyOrdersChartContainer);\n        this.updateDailyOrdersChart(); // 初始化走势图\n\n        if (this.dailyOrdersTrendChartInstance) {\n          this.dailyOrdersTrendChartInstance.dispose();\n        }\n\n        this.dailyOrdersTrendChartInstance = echarts.init(this.$refs.dailyOrdersTrendChartContainer);\n        this.updateDailyOrdersTrendChart();\n      });\n    },\n\n    // 更新每日订单数量图表\n    updateDailyOrdersChart() {\n      if (!this.dailyOrdersChartInstance || !this.selectedCity) return; // 筛选当前城市的每日订单数据\n\n      const cityDailyOrders = this.dailyOrdersData.filter(item => item.cityCode === this.selectedCity.cityCode); // 按日期排序\n\n      cityDailyOrders.sort((a, b) => a.date.localeCompare(b.date)); // 提取日期和订单数量\n\n      const dates = cityDailyOrders.map(item => item.date);\n      const orderCounts = cityDailyOrders.map(item => item.orderCount); // 设置图表选项\n\n      const option = {\n        title: {\n          text: `${this.selectedCity.cityName}地市每日订单数量`,\n          left: 'center'\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '15%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: dates,\n          axisLabel: {\n            rotate: 45,\n            interval: 0\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '订单数量'\n        },\n        series: [{\n          name: '订单数量',\n          type: 'bar',\n          barWidth: '60%',\n          data: orderCounts,\n          itemStyle: {\n            color: '#409EFF'\n          },\n          label: {\n            show: true,\n            position: 'top',\n            formatter: '{c}'\n          }\n        }]\n      };\n      this.dailyOrdersChartInstance.setOption(option);\n    },\n\n    // 更新每日订单数量走势图（折线图）\n    updateDailyOrdersTrendChart() {\n      if (!this.dailyOrdersTrendChartInstance || !this.selectedCity) return; // 筛选当前城市的每日订单数据\n\n      const cityDailyOrders = this.dailyOrdersData.filter(item => item.cityCode === this.selectedCity.cityCode); // 按日期排序\n\n      cityDailyOrders.sort((a, b) => a.date.localeCompare(b.date)); // 提取日期和订单数量\n\n      const dates = cityDailyOrders.map(item => item.date);\n      const orderCounts = cityDailyOrders.map(item => item.orderCount); // 设置图表选项\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            label: {\n              backgroundColor: '#6a7985'\n            }\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: dates,\n          axisLabel: {\n            rotate: 45,\n            interval: 0\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '订单数量'\n        },\n        series: [{\n          name: '订单数量',\n          type: 'line',\n          stack: '总量',\n          emphasis: {\n            focus: 'series'\n          },\n          data: orderCounts,\n          smooth: true,\n          symbol: 'circle',\n          symbolSize: 6,\n          itemStyle: {\n            color: '#409EFF'\n          },\n          lineStyle: {\n            width: 3,\n            color: '#409EFF'\n          },\n          areaStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 0,\n              y2: 1,\n              colorStops: [{\n                offset: 0,\n                color: 'rgba(64,158,255,0.5)' // 渐变色起始颜色\n\n              }, {\n                offset: 1,\n                color: 'rgba(64,158,255,0.1)' // 渐变色结束颜色\n\n              }]\n            }\n          }\n        }]\n      };\n      this.dailyOrdersTrendChartInstance.setOption(option);\n    }\n\n  }\n};", "map": {"version": 3, "mappings": ";;;AA0HA;AACA;AAEA;EACAA,uBADA;;EAEAC;IACA;MACAC,qEADA;MACA;MACAC,aAFA;MAGAC,mBAHA;MAIAC,wBAJA;MAKAC,8BALA;MAMAC,mBANA;MAOAC,cAPA;MAQAC,mBARA;MAQA;MACAC,cATA;MASA;MACAC,uBAVA;MAUA;MACAC,+BAXA;MAWA;MACAC,kBAZA;MAYA;MACAC,mCAbA;MAaA;MACAC;QACA,WADA;QAEA,WAFA;QAGA,WAHA;QAIA,WAJA;QAKA,WALA;QAMA,WANA;QAOA,WAPA;QAQA,WARA;QASA,WATA;QAUA,WAVA;QAWA,WAXA;QAYA,YAZA;QAaA,WAbA;QAcA;MAdA;IAdA;EA+BA,CAlCA;;EAmCAC;IACA;IACAC;MACA;QACAjB,qCADA;QAEAkB;MAFA;IAIA;;EAPA,CAnCA;;EA4CAC;IACA;IACA;IACA;IACA;IACA;IACA,2BANA,CAOA;;IACAC;EACA,CArDA;;EAsDAC;IACA;IACAD,wDAFA,CAGA;;IACA;MACA;MACA;IACA;;IACA;MACA;MACA;IACA;;IACA;MACA;MACA;IACA;;IACA;MACA;MACA;IACA;EACA,CA1EA;;EA2EAE;IACA;IACAC;MACA;MACA;IACA,CALA;;IAMA;IACAC;MACA;MACA;IACA,CAVA;;IAWA;IACAC;MACA;MAEA;QACAC;UACAC,iBADA;UAEAC;QAFA,CADA;QAKAC;UACAC;QADA,CALA;QAQAC;UACAC,kBADA;UAEAJ,YAFA;UAGA3B;QAHA,CARA;QAaAgC,SACA;UACAjC,YADA;UAEAkC,WAFA;UAGAC,aAHA;UAIAlC,oBAJA;UAKAmC;YACAC;cACAC,cADA;cAEAC,gBAFA;cAGAC;YAHA;UADA;QALA,CADA;MAbA;MA8BA;IACA,CA9CA;;IA+CA;IACAC;MACA,qCADA,CAGA;;MACA;MACA,oFALA,CAOA;;MACA;QACA;QACA;UACA;UACA;QACA,CAHA;QAKA;UACAzC,gCADA;UAEAkC,YAFA;UAGAjC,cAHA;UAIAyC;QAJA;MAMA,CAbA;MAeA;QACAhB;UACAC,kBADA;UAEAC;QAFA,CADA;QAKAC;UACAC;QADA,CALA;QAQAC;UACA9B,wDADA;UAEAiC,cAFA;UAGAF,oBAHA;UAIAW;QAJA,CARA;QAcAC;UACAhB,UADA;UAEAiB,WAFA;UAGAF,aAHA;UAIAG;QAJA,CAdA;QAoBAC;UACAb,gBADA;UAEAc,kBAFA;UAGA/C,WAHA;UAIAgD;YACAC,UADA;YAEAC;UAFA;QAJA,CApBA;QA6BAC;UACAlB,aADA;UAEAlC;QAFA,CA7BA;QAiCAiC;MAjCA;MAoCA;IACA,CA5GA;;IA6GA;IACAoB;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;IACA,CA3HA;;IA4HA;IACA;MACA;QACA;QACA;QACA;QAEA;UACAC,eADA;UAEAC;QAFA;QAIA;QACA;QACA;UACA;QACA,CAFA;MAGA,CAdA,CAcA;QACA;MACA;IACA,CA/IA;;IAgJA;IACA;MACA;QACA;QACA;QAEA;UACAD,eADA;UAEAC;QAFA;QAKA;QACA;UACA;QACA,CAFA;MAGA,CAbA,CAaA;QACA;MACA;IACA,CAlKA;;IAmKA;IACA;MACA;QACA;QACA;QAEA;UACAD,eADA;UAEAC;QAFA,GAJA,CASA;;QACA,uCAVA,CAYA;;QACA,6CAbA,CAeA;;QACA;MAEA,CAlBA,CAkBA;QACA;MACA;IACA,CA1LA;;IA2LA;IACA;MACA;QACA;QACA;QAEA;UACAD,eADA;UAEAC;QAFA,GAJA,CASA;;QACA;MAEA,CAZA,CAYA;QACA;MACA;IACA,CA5MA;;IA6MA;IACAC;MACA;QACA;MACA;;MACA;IACA,CAnNA;;IAoNA;IACAC;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;;MACA;MACA;MACA;MACA;IACA,CA/NA;;IAgOA;IACAC;MACA;IACA,CAnOA;;IAoOA;IACAC;MACA;MACA;MACA;MACA;IACA,CA1OA;;IA2OA;IACAC;MACA;MACA,qCAFA,CAIA;;MACA;QACA;QACA;UACA;QACA;;QACA;QACA,8BANA,CAQA;;QACA;UACA;QACA;;QACA;QACA;MACA,CAdA;IAeA,CAhQA;;IAiQA;IACAC;MACA,iEADA,CAGA;;MACA,4DACAC,4CADA,EAJA,CAQA;;MACAC,6DATA,CAWA;;MACA;MACA,iEAbA,CAeA;;MACA;QACArC;UACAC,6CADA;UAEAC;QAFA,CADA;QAKAC;UACAC,eADA;UAEAkC;YACA9B;UADA;QAFA,CALA;QAWAU;UACAhB,UADA;UAEAiB,WAFA;UAGAF,aAHA;UAIAG;QAJA,CAXA;QAiBAC;UACAb,gBADA;UAEAjC,WAFA;UAGAgD;YACAC,UADA;YAEAC;UAFA;QAHA,CAjBA;QAyBAC;UACAlB,aADA;UAEAlC;QAFA,CAzBA;QA6BAiC,SACA;UACAjC,YADA;UAEAkC,WAFA;UAGA+B,eAHA;UAIAhE,iBAJA;UAKAoC;YACA6B;UADA,CALA;UAQAC;YACAC,UADA;YAEAC,eAFA;YAGAC;UAHA;QARA,CADA;MA7BA;MA+CA;IACA,CAlUA;;IAmUA;IACAC;MACA,sEADA,CAGA;;MACA,4DACAT,4CADA,EAJA,CAQA;;MACAC,6DATA,CAWA;;MACA;MACA,iEAbA,CAeA;;MACA;QACAlC;UACAC,eADA;UAEAkC;YACA9B,aADA;YAEAiC;cACAK;YADA;UAFA;QAFA,CADA;QAUA5B;UACAhB,UADA;UAEAiB,WAFA;UAGAF,YAHA;UAIAG;QAJA,CAVA;QAgBAC;UACAb,gBADA;UAEAc,kBAFA;UAGA/C,WAHA;UAIAgD;YACAC,UADA;YAEAC;UAFA;QAJA,CAhBA;QAyBAC;UACAlB,aADA;UAEAlC;QAFA,CAzBA;QA6BAiC,SACA;UACAjC,YADA;UAEAkC,YAFA;UAGAuC,WAHA;UAIArC;YACAsC;UADA,CAJA;UAOAzE,iBAPA;UAQAyC,YARA;UASAiC,gBATA;UAUAC,aAVA;UAWAvC;YACA6B;UADA,CAXA;UAcAW;YACAC,QADA;YAEAZ;UAFA,CAdA;UAkBAa;YACAb;cACAhC,cADA;cAEA8C,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC;gBACAC,SADA;gBACAnB,6BADA,CACA;;cADA,GAEA;gBACAmB,SADA;gBACAnB,6BADA,CACA;;cADA,CAFA;YANA;UADA;QAlBA,CADA;MA7BA;MAkEA;IACA;;EAvZA;AA3EA", "names": ["name", "data", "date<PERSON><PERSON><PERSON>", "tableData", "chartInstance", "trendChartInstance", "dailyOrdersChartInstance", "dailyOrdersData", "totalOrders", "moduleTableData", "moduleList", "moduleTypeTableData", "dailyOrdersDialogVisible", "selectedCity", "dailyOrdersTrendChartInstance", "cityMap", "computed", "chartData", "value", "mounted", "window", "<PERSON><PERSON><PERSON><PERSON>", "methods", "initChart", "initTrendChart", "updateChart", "title", "text", "left", "tooltip", "trigger", "legend", "orient", "series", "type", "radius", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "updateTrendChart", "smooth", "bottom", "grid", "right", "containLabel", "xAxis", "boundaryGap", "axisLabel", "rotate", "interval", "yAxis", "resi<PERSON><PERSON><PERSON><PERSON>", "date", "endDate", "getModuleCount", "dateChange", "getCityName", "formatDate", "showDailyOrders<PERSON>hart", "updateDailyOrdersChart", "item", "cityDailyOrders", "axisPointer", "<PERSON><PERSON><PERSON><PERSON>", "color", "label", "show", "position", "formatter", "updateDailyOrdersTrendChart", "backgroundColor", "stack", "focus", "symbol", "symbolSize", "lineStyle", "width", "areaStyle", "x", "y", "x2", "y2", "colorStops", "offset"], "sourceRoot": "src/views/hnzsxH5/order", "sources": ["statistics.vue"], "sourcesContent": ["<!-- 订单统计 -->\r\n<template>\r\n  <div class=\"ele-body\">\r\n    <el-card shadow=\"never\">\r\n      <div slot=\"header\">\r\n        <el-form :inline=\"true\" class=\"ele-form-search\">\r\n          <el-form-item label=\"统计日期:\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              @change=\"dateChange\"\r\n            ></el-date-picker>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 统计数据展示 -->\r\n      <div class=\"statistics-container\">\r\n        <!-- 订单总数展示 -->\r\n        <el-row :gutter=\"20\" class=\"mb-20\">\r\n          <el-col :span=\"24\">\r\n            <div class=\"total-orders\">\r\n              <span class=\"total-label\">订单总数：</span>\r\n              <span class=\"total-value\">{{ totalOrders }}</span>\r\n              <span class=\"total-unit\">单</span>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <div ref=\"chartContainer\" style=\"width: 100%; height: 400px;\"></div>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 每日订单走势图 -->\r\n        <el-row :gutter=\"20\" class=\"mt-20\">\r\n          <el-col :span=\"24\">\r\n            <div ref=\"trendChartContainer\" style=\"width: 100%; height: 400px;\"></div>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 按模块类型汇总发展总量统计 -->\r\n        <el-row :gutter=\"20\" class=\"mt-20\">\r\n          <el-col :span=\"24\">\r\n            <div class=\"chart-title\">按模块类型汇总发展总量统计</div>\r\n            <el-table :data=\"moduleTypeTableData\" border stripe>\r\n              <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n              <el-table-column prop=\"moduleTypeName\" label=\"模块类型名称\" width=\"150\" align=\"center\"></el-table-column>\r\n              <el-table-column prop=\"moduleTypeCode\" label=\"模块类型编码\" width=\"150\" align=\"center\"></el-table-column>\r\n              <el-table-column prop=\"totalCount\" label=\"订单总量\" width=\"120\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <span class=\"total-count-highlight\">{{ scope.row.totalCount }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"cityCount\" label=\"涉及地市数\" width=\"120\" align=\"center\"></el-table-column>\r\n              <el-table-column prop=\"percentage\" label=\"占比(%)\" width=\"100\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <span class=\"percentage-text\">{{ scope.row.percentage }}%</span>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 各地市按模块分类订单数量统计表 -->\r\n        <el-row :gutter=\"20\" class=\"mt-20\">\r\n          <el-col :span=\"24\">\r\n            <div class=\"chart-title\">各地市按模块分类订单数量统计</div>\r\n            <el-table :data=\"moduleTableData\" border stripe>\r\n              <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n              <el-table-column prop=\"cityCode\" label=\"地市编码\" width=\"100\" align=\"center\"></el-table-column>\r\n              <el-table-column prop=\"cityName\" label=\"地市名称\" width=\"100\" align=\"center\"></el-table-column>\r\n              <el-table-column prop=\"totalCount\" label=\"总订单数\" width=\"100\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <span\r\n                    class=\"clickable-total-count\"\r\n                    @click=\"showDailyOrdersChart(scope.row)\"\r\n                  >\r\n                    {{ scope.row.totalCount }}\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 动态生成模块列 -->\r\n              <el-table-column\r\n                v-for=\"module in moduleList\"\r\n                :key=\"module.id\"\r\n                :prop=\"'module_' + module.id\"\r\n                :label=\"module.moduleName\"\r\n                align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ getModuleCount(scope.row, module.id) }}\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 每日订单数量条形图弹窗 -->\r\n    <el-dialog\r\n      :title=\"selectedCity ? `${selectedCity.cityName} 地市每日订单数量` : '每日订单数量统计'\"\r\n      :visible.sync=\"dailyOrdersDialogVisible\"\r\n      width=\"70%\"\r\n      custom-class=\"city-daily-orders-dialog\"\r\n    >\r\n      <div ref=\"dailyOrdersChartContainer\" style=\"width: 100%; height: 400px;\"></div>\r\n\r\n      <!-- 添加订单数量走势图 -->\r\n      <div class=\"chart-subtitle\"></div>\r\n      <div ref=\"dailyOrdersTrendChartContainer\" style=\"width: 100%; height: 400px;\"></div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { getOrderCount, getDailyOrdersByCityCode, getOrderCountByModule, getOrderCountByModuleType } from '@/api/hnzsxH5/order';\r\n\r\nexport default {\r\n  name: 'OrderStatistics',\r\n  data() {\r\n    return {\r\n      dateRange: [this.formatDate(new Date()), this.formatDate(new Date())], // 默认今天到今天\r\n      tableData: [],\r\n      chartInstance: null,\r\n      trendChartInstance: null,\r\n      dailyOrdersChartInstance: null,\r\n      dailyOrdersData: [],\r\n      totalOrders: 0,\r\n      moduleTableData: [], // 按模块分类的表格数据\r\n      moduleList: [], // 模块列表\r\n      moduleTypeTableData: [], // 按模块类型汇总的表格数据\r\n      dailyOrdersDialogVisible: false, // 每日订单数量弹窗显示状态\r\n      selectedCity: null, // 当前选中的城市\r\n      dailyOrdersTrendChartInstance: null, // 订单数量走势图实例\r\n      cityMap: {\r\n        '730': '岳阳',\r\n        '731': '长沙',\r\n        '732': '湘潭',\r\n        '733': '株洲',\r\n        '734': '衡阳',\r\n        '735': '郴州',\r\n        '736': '常德',\r\n        '737': '益阳',\r\n        '738': '娄底',\r\n        '739': '邵阳',\r\n        '743': '湘西',\r\n        '744': '张家界',\r\n        '745': '怀化',\r\n        '746': '永州'\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    // 图表所需数据\r\n    chartData() {\r\n      return this.tableData.map(item => ({\r\n        name: this.getCityName(item.cityCode),\r\n        value: item.saleNum\r\n      }));\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initChart();\r\n    this.initTrendChart();\r\n    this.fetchData();\r\n    this.fetchDailyOrdersData();\r\n    this.fetchModuleData();\r\n    this.fetchModuleTypeData();\r\n    // 监听窗口大小变化，重绘图表\r\n    window.addEventListener('resize', this.resizeCharts);\r\n  },\r\n  beforeDestroy() {\r\n    // 移除事件监听\r\n    window.removeEventListener('resize', this.resizeCharts);\r\n    // 销毁图表实例\r\n    if (this.chartInstance) {\r\n      this.chartInstance.dispose();\r\n      this.chartInstance = null;\r\n    }\r\n    if (this.trendChartInstance) {\r\n      this.trendChartInstance.dispose();\r\n      this.trendChartInstance = null;\r\n    }\r\n    if (this.dailyOrdersChartInstance) {\r\n      this.dailyOrdersChartInstance.dispose();\r\n      this.dailyOrdersChartInstance = null;\r\n    }\r\n    if (this.dailyOrdersTrendChartInstance) {\r\n      this.dailyOrdersTrendChartInstance.dispose();\r\n      this.dailyOrdersTrendChartInstance = null;\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化图表\r\n    initChart() {\r\n      this.chartInstance = echarts.init(this.$refs.chartContainer);\r\n      this.updateChart();\r\n    },\r\n    // 初始化趋势图表\r\n    initTrendChart() {\r\n      this.trendChartInstance = echarts.init(this.$refs.trendChartContainer);\r\n      this.updateTrendChart();\r\n    },\r\n    // 更新图表数据\r\n    updateChart() {\r\n      if (!this.chartInstance) return;\r\n\r\n      const option = {\r\n        title: {\r\n          text: '各地市订单数量统计',\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'item'\r\n        },\r\n        legend: {\r\n          orient: 'vertical',\r\n          left: 'left',\r\n          data: this.chartData.map(item => item.name)\r\n        },\r\n        series: [\r\n          {\r\n            name: '订单数量',\r\n            type: 'pie',\r\n            radius: '50%',\r\n            data: this.chartData,\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowOffsetX: 0,\r\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      };\r\n\r\n      this.chartInstance.setOption(option);\r\n    },\r\n    // 更新趋势图表数据\r\n    updateTrendChart() {\r\n      if (!this.trendChartInstance) return;\r\n\r\n      // 处理数据，按日期和城市分组\r\n      const dates = [...new Set(this.dailyOrdersData.map(item => item.date))].sort();\r\n      const cities = [...new Set(this.dailyOrdersData.map(item => item.cityCode))].sort();\r\n\r\n      // 准备每个城市的数据系列\r\n      const series = cities.map(cityCode => {\r\n        // 筛选该城市的数据\r\n        const cityData = dates.map(date => {\r\n          const item = this.dailyOrdersData.find(d => d.date === date && d.cityCode === cityCode);\r\n          return item ? item.orderCount : 0;\r\n        });\r\n\r\n        return {\r\n          name: this.getCityName(cityCode),\r\n          type: 'line',\r\n          data: cityData,\r\n          smooth: true\r\n        };\r\n      });\r\n\r\n      const option = {\r\n        title: {\r\n          text: '各地市每日订单走势图',\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          data: cities.map(cityCode => this.getCityName(cityCode)),\r\n          type: 'scroll',\r\n          orient: 'horizontal',\r\n          bottom: 0\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '订单数量'\r\n        },\r\n        series: series\r\n      };\r\n\r\n      this.trendChartInstance.setOption(option);\r\n    },\r\n    // 窗口大小变化时重绘图表\r\n    resizeCharts() {\r\n      if (this.chartInstance) {\r\n        this.chartInstance.resize();\r\n      }\r\n      if (this.trendChartInstance) {\r\n        this.trendChartInstance.resize();\r\n      }\r\n      if (this.dailyOrdersChartInstance && this.dailyOrdersDialogVisible) {\r\n        this.dailyOrdersChartInstance.resize();\r\n      }\r\n      if (this.dailyOrdersTrendChartInstance && this.dailyOrdersDialogVisible) {\r\n        this.dailyOrdersTrendChartInstance.resize();\r\n      }\r\n    },\r\n    // 获取统计数据\r\n    async fetchData() {\r\n      try {\r\n        // 如果没有选择日期范围，使用默认值（今天）\r\n        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());\r\n        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());\r\n\r\n        const data = await getOrderCount({\r\n          date: startDate,\r\n          endDate: endDate\r\n        });\r\n        this.tableData = data || [];\r\n        this.totalOrders = this.tableData.reduce((sum, item) => sum + (parseInt(item.saleNum) || 0), 0);\r\n        this.$nextTick(() => {\r\n          this.updateChart();\r\n        });\r\n      } catch (error) {\r\n        this.$message.error('获取统计数据失败: ' + error.message);\r\n      }\r\n    },\r\n    // 获取各地市每日订单数据\r\n    async fetchDailyOrdersData() {\r\n      try {\r\n        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());\r\n        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());\r\n\r\n        const data = await getDailyOrdersByCityCode({\r\n          date: startDate,\r\n          endDate: endDate\r\n        });\r\n\r\n        this.dailyOrdersData = data || [];\r\n        this.$nextTick(() => {\r\n          this.updateTrendChart();\r\n        });\r\n      } catch (error) {\r\n        this.$message.error('获取每日订单数据失败: ' + error.message);\r\n      }\r\n    },\r\n    // 获取按模块分类的订单数据\r\n    async fetchModuleData() {\r\n      try {\r\n        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());\r\n        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());\r\n\r\n        const result = await getOrderCountByModule({\r\n          date: startDate,\r\n          endDate: endDate\r\n        });\r\n\r\n        // 设置模块列表\r\n        this.moduleList = result.modules || [];\r\n\r\n        // 设置表格数据\r\n        this.moduleTableData = result.cityData || [];\r\n\r\n        // 计算总订单数\r\n        this.totalOrders = this.moduleTableData.reduce((sum, city) => sum + (city.totalCount || 0), 0);\r\n\r\n      } catch (error) {\r\n        this.$message.error('获取按模块分类的订单数据失败: ' + error.message);\r\n      }\r\n    },\r\n    // 获取按模块类型汇总的订单数据\r\n    async fetchModuleTypeData() {\r\n      try {\r\n        const startDate = this.dateRange ? this.dateRange[0] : this.formatDate(new Date());\r\n        const endDate = this.dateRange ? this.dateRange[1] : this.formatDate(new Date());\r\n\r\n        const result = await getOrderCountByModuleType({\r\n          date: startDate,\r\n          endDate: endDate\r\n        });\r\n\r\n        // 设置模块类型汇总表格数据\r\n        this.moduleTypeTableData = result || [];\r\n\r\n      } catch (error) {\r\n        this.$message.error('获取按模块类型汇总的订单数据失败: ' + error.message);\r\n      }\r\n    },\r\n    // 获取指定城市和模块的订单数\r\n    getModuleCount(row, moduleId) {\r\n      if (!row.modules || !row.modules[moduleId]) {\r\n        return 0;\r\n      }\r\n      return row.modules[moduleId];\r\n    },\r\n    // 日期变化事件\r\n    dateChange(val) {\r\n      if (val) {\r\n        this.dateRange = val;\r\n      } else {\r\n        this.dateRange = [this.formatDate(new Date()), this.formatDate(new Date())];\r\n      }\r\n      this.fetchData();\r\n      this.fetchDailyOrdersData();\r\n      this.fetchModuleData();\r\n      this.fetchModuleTypeData();\r\n    },\r\n    // 获取城市名称\r\n    getCityName(cityCode) {\r\n      return this.cityMap[cityCode] || cityCode;\r\n    },\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      const year = date.getFullYear();\r\n      const month = ('0' + (date.getMonth() + 1)).slice(-2);\r\n      const day = ('0' + date.getDate()).slice(-2);\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n    // 显示每日订单数量图表\r\n    showDailyOrdersChart(city) {\r\n      this.selectedCity = city;\r\n      this.dailyOrdersDialogVisible = true;\r\n\r\n      // 下一帧初始化图表\r\n      this.$nextTick(() => {\r\n        // 初始化柱状图\r\n        if (this.dailyOrdersChartInstance) {\r\n          this.dailyOrdersChartInstance.dispose();\r\n        }\r\n        this.dailyOrdersChartInstance = echarts.init(this.$refs.dailyOrdersChartContainer);\r\n        this.updateDailyOrdersChart();\r\n\r\n        // 初始化走势图\r\n        if (this.dailyOrdersTrendChartInstance) {\r\n          this.dailyOrdersTrendChartInstance.dispose();\r\n        }\r\n        this.dailyOrdersTrendChartInstance = echarts.init(this.$refs.dailyOrdersTrendChartContainer);\r\n        this.updateDailyOrdersTrendChart();\r\n      });\r\n    },\r\n    // 更新每日订单数量图表\r\n    updateDailyOrdersChart() {\r\n      if (!this.dailyOrdersChartInstance || !this.selectedCity) return;\r\n\r\n      // 筛选当前城市的每日订单数据\r\n      const cityDailyOrders = this.dailyOrdersData.filter(item =>\r\n        item.cityCode === this.selectedCity.cityCode\r\n      );\r\n\r\n      // 按日期排序\r\n      cityDailyOrders.sort((a, b) => a.date.localeCompare(b.date));\r\n\r\n      // 提取日期和订单数量\r\n      const dates = cityDailyOrders.map(item => item.date);\r\n      const orderCounts = cityDailyOrders.map(item => item.orderCount);\r\n\r\n      // 设置图表选项\r\n      const option = {\r\n        title: {\r\n          text: `${this.selectedCity.cityName}地市每日订单数量`,\r\n          left: 'center'\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: dates,\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '订单数量'\r\n        },\r\n        series: [\r\n          {\r\n            name: '订单数量',\r\n            type: 'bar',\r\n            barWidth: '60%',\r\n            data: orderCounts,\r\n            itemStyle: {\r\n              color: '#409EFF'\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: '{c}'\r\n            }\r\n          }\r\n        ]\r\n      };\r\n\r\n      this.dailyOrdersChartInstance.setOption(option);\r\n    },\r\n    // 更新每日订单数量走势图（折线图）\r\n    updateDailyOrdersTrendChart() {\r\n      if (!this.dailyOrdersTrendChartInstance || !this.selectedCity) return;\r\n\r\n      // 筛选当前城市的每日订单数据\r\n      const cityDailyOrders = this.dailyOrdersData.filter(item =>\r\n        item.cityCode === this.selectedCity.cityCode\r\n      );\r\n\r\n      // 按日期排序\r\n      cityDailyOrders.sort((a, b) => a.date.localeCompare(b.date));\r\n\r\n      // 提取日期和订单数量\r\n      const dates = cityDailyOrders.map(item => item.date);\r\n      const orderCounts = cityDailyOrders.map(item => item.orderCount);\r\n\r\n      // 设置图表选项\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            rotate: 45,\r\n            interval: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '订单数量'\r\n        },\r\n        series: [\r\n          {\r\n            name: '订单数量',\r\n            type: 'line',\r\n            stack: '总量',\r\n            emphasis: {\r\n              focus: 'series'\r\n            },\r\n            data: orderCounts,\r\n            smooth: true,\r\n            symbol: 'circle',\r\n            symbolSize: 6,\r\n            itemStyle: {\r\n              color: '#409EFF'\r\n            },\r\n            lineStyle: {\r\n              width: 3,\r\n              color: '#409EFF'\r\n            },\r\n            areaStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: 'rgba(64,158,255,0.5)' // 渐变色起始颜色\r\n                }, {\r\n                  offset: 1, color: 'rgba(64,158,255,0.1)' // 渐变色结束颜色\r\n                }]\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      };\r\n\r\n      this.dailyOrdersTrendChartInstance.setOption(option);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.statistics-container {\r\n  padding: 10px;\r\n}\r\n.mt-20 {\r\n  margin-top: 20px;\r\n}\r\n.mb-20 {\r\n  margin-bottom: 20px;\r\n}\r\n/* 订单总数样式 */\r\n.total-orders {\r\n  text-align: center;\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n.total-label {\r\n  font-size: 16px;\r\n  color: #606266;\r\n  margin-right: 10px;\r\n}\r\n.total-value {\r\n  font-size: 24px;\r\n  color: #409EFF;\r\n  font-weight: bold;\r\n  margin-right: 5px;\r\n}\r\n.total-unit {\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n.chart-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  text-align: center;\r\n  margin-bottom: 15px;\r\n  font-weight: normal;\r\n  height: 22px;\r\n  line-height: 22px;\r\n}\r\n/* 可点击的总订单数样式 */\r\n.clickable-total-count {\r\n  color: #409EFF;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  text-decoration: underline;\r\n}\r\n.clickable-total-count:hover {\r\n  color: #66b1ff;\r\n}\r\n/* 对话框标题样式 */\r\n.dialog-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  text-align: center;\r\n  margin-bottom: 15px;\r\n  font-weight: bold;\r\n}\r\n/* 图表副标题样式 */\r\n.chart-subtitle {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  text-align: center;\r\n  margin: 20px 0 15px 0;\r\n  font-weight: bold;\r\n}\r\n/* 弹窗位置调整 */\r\n.city-daily-orders-dialog {\r\n  margin-left: 5% !important;\r\n}\r\n</style>\r\n"]}, "metadata": {}, "sourceType": "module"}