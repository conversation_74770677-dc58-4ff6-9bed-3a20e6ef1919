# 按地市汇总统计数据一致性问题修复说明

## 问题描述

在实现按地市汇总发展总量统计功能时，发现新增的"按地市汇总发展总量统计"与现有的"各地市按模块分类订单数量统计"的数据结果不一致。

### 问题现象
- 现有的"各地市按模块分类订单数量统计"显示的数据是准确的
- 新增的"按地市汇总发展总量统计"显示的数据与之不匹配

## 问题原因分析

### 1. 现有准确查询的逻辑（各地市按模块分类订单数量统计）
```java
// Mapper查询
@Select("SELECT a.CITY_CODE as cityCode, " +
        "COUNT(1) as totalCount, " +
        "m.MODULE_NAME as moduleName, " +
        "m.ID as moduleId, " +
        "COUNT(CASE WHEN g.MODULE_ID = m.ID THEN 1 ELSE NULL END) as moduleCount " +
        "FROM hnzsxh5_order_info a " +
        "LEFT JOIN hnzsxh5_goods_info g ON a.GOODS_ID = g.ID " +
        "LEFT JOIN hnzsxh5_module m ON g.MODULE_ID = m.ID " +
        "WHERE a.STATE = 0 " +
        "GROUP BY a.CITY_CODE, m.ID, m.MODULE_NAME")
List<Map<String, Object>> getOrderCountByModule(startDate, endDate);

// Service层汇总逻辑
for (Map<String, Object> orderCount : orderCounts) {
    String cityCode = (String) orderCount.get("cityCode");
    Integer moduleCount = ((Number) orderCount.get("moduleCount")).intValue();
    
    // 累加每个地市的总订单数
    Integer totalCount = ((Number) cityData.get("totalCount")).intValue();
    cityData.put("totalCount", totalCount + moduleCount);
}
```

### 2. 原有问题查询的逻辑（按地市汇总发展总量统计）
```java
// 直接在SQL中计算总数，可能存在JOIN导致的重复计算问题
@Select("SELECT a.CITY_CODE as cityCode, " +
        "COUNT(a.ID) as totalCount, " +
        "COUNT(DISTINCT g.MODULE_ID) as moduleCount " +
        "FROM hnzsxh5_order_info a " +
        "LEFT JOIN hnzsxh5_goods_info g ON a.GOODS_ID = g.ID " +
        "WHERE a.STATE = 0 " +
        "GROUP BY a.CITY_CODE")
```

### 3. 问题根源
- **查询逻辑不一致**：现有准确查询使用按地市+模块分组后在Service层汇总，新查询直接在SQL中按地市分组
- **JOIN关系处理不同**：可能导致数据重复计算或遗漏
- **统计维度差异**：两个查询的统计基准不完全一致

## 解决方案

### 修复策略
**复用现有准确查询的逻辑**，确保数据一致性。

### 具体修改

#### 1. 修改Service层实现
```java
@Override
public List<Map<String, Object>> getOrderCountByCity(String startDate, String endDate) {
    // 使用与getOrderCountByModule相同的逻辑来确保数据一致性
    List<Map<String, Object>> orderCounts = baseMapper.getOrderCountByModule(startDate, endDate);
    
    // 按地市代码分组并汇总
    Map<String, Map<String, Object>> cityDataMap = new HashMap<>();
    
    for (Map<String, Object> orderCount : orderCounts) {
        String cityCode = (String) orderCount.get("cityCode");
        Integer moduleCount = ((Number) orderCount.get("moduleCount")).intValue();
        
        // 创建或获取地市数据
        Map<String, Object> cityData = cityDataMap.computeIfAbsent(cityCode, k -> {
            Map<String, Object> data = new HashMap<>();
            data.put("cityCode", cityCode);
            data.put("cityName", getCityName(cityCode));
            data.put("totalCount", 0);
            data.put("moduleCount", 0);
            return data;
        });
        
        // 累加订单总数
        Integer totalCount = ((Number) cityData.get("totalCount")).intValue();
        cityData.put("totalCount", totalCount + moduleCount);
        
        // 统计有订单的模块数量
        if (moduleCount > 0) {
            Integer currentModuleCount = ((Number) cityData.get("moduleCount")).intValue();
            cityData.put("moduleCount", currentModuleCount + 1);
        }
    }
    
    // 转换为列表并排序
    List<Map<String, Object>> cityStats = new ArrayList<>(cityDataMap.values());
    cityStats.sort((a, b) -> {
        Integer countA = ((Number) a.get("totalCount")).intValue();
        Integer countB = ((Number) b.get("totalCount")).intValue();
        return countB.compareTo(countA);
    });
    
    return cityStats;
}
```

#### 2. 保留Mapper查询（作为备用）
虽然现在Service层复用了`getOrderCountByModule`的查询，但保留了独立的Mapper查询方法，以备将来优化使用。

## 修复效果

### 数据一致性保证
1. **相同的数据源**：两个统计都基于相同的查询逻辑
2. **相同的计算方式**：都使用Service层汇总的方式计算总数
3. **相同的过滤条件**：确保统计范围完全一致

### 验证方法
1. 对比"按地市汇总发展总量统计"中每个地市的订单总量
2. 与"各地市按模块分类订单数量统计"中对应地市的各模块订单数之和进行对比
3. 两者应该完全一致

### 示例验证
```
各地市按模块分类订单数量统计：
- 长沙：智能终端(245) + 其他模块(17) = 262
- 岳阳：智能终端(24) + 其他模块(0) = 24

按地市汇总发展总量统计：
- 长沙：262 ✓
- 岳阳：24 ✓
```

## 技术要点

### 1. 数据一致性原则
- 复用已验证正确的查询逻辑
- 避免重复实现相似的统计逻辑
- 确保统计基准和过滤条件一致

### 2. 代码复用策略
- Service层复用现有查询方法
- 在Service层进行不同维度的数据汇总
- 保持Mapper层查询的简洁性

### 3. 性能考虑
- 虽然复用查询可能会获取更多数据，但确保了数据准确性
- 可以在后续优化中考虑专门的汇总查询
- 当前方案优先保证数据正确性

## 总结

通过复用现有准确查询的逻辑，成功解决了按地市汇总统计与现有统计数据不一致的问题。这种修复方案：

1. **确保数据准确性**：使用已验证正确的查询逻辑
2. **提高代码复用性**：避免重复实现相似功能
3. **便于维护**：统一的数据处理逻辑，减少维护成本
4. **易于验证**：可以直接对比两个统计结果验证正确性

修复后，用户可以放心使用新增的按地市汇总统计功能，数据与现有统计完全一致。
