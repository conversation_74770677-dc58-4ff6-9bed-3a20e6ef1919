<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsCgrMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_cgr a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.cgrCategory != null">
                AND a.CGR_CATEGORY = #{param.cgrCategory}
            </if>
            <if test="param.cgrSmallCategory != null">
                AND a.CGR_SMALL_CATEGORY = #{param.cgrSmallCategory}
            </if>
            <if test="param.cgrCategoryName != null">
                AND a.CGR_CATEGORY_NAME LIKE CONCAT('%', #{param.cgrCategoryName}, '%')
            </if>
            <if test="param.cgrSmallCategoryName != null">
                AND a.CGR_SMALL_CATEGORY_NAME LIKE CONCAT('%', #{param.cgrSmallCategoryName}, '%')
            </if>
            <if test="param.cgrSalesGoodsId != null">
                AND a.CGR_SALES_GOODS_ID = #{param.cgrSalesGoodsId}
            </if>
            <if test="param.cgrCategoryOptionsName != null">
                AND a.CGR_CATEGORY_OPTIONS_NAME LIKE CONCAT('%', #{param.cgrCategoryOptionsName}, '%')
            </if>
            <if test="param.money != null">
                AND a.MONEY LIKE CONCAT('%', #{param.money}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.mainTypeId != null">
                AND a.MAIN_TYPE_ID = #{param.mainTypeId}
            </if>
            <if test="param.terminalCodeNumber != null">
                AND a.TERMINAL_CODE_NUMBER = #{param.terminalCodeNumber}
            </if>
            <if test="param.periods != null">
                AND a.PERIODS = #{param.periods}
            </if>
            <if test="param.terminalDeviceType != null">
                AND a.TERMINAL_DEVICE_TYPE LIKE CONCAT('%', #{param.terminalDeviceType}, '%')
            </if>
            <if test="param.configurationName != null">
                AND a.CONFIGURATION_NAME LIKE CONCAT('%', #{param.configurationName}, '%')
            </if>
            <if test="param.configurationId != null">
                AND a.CONFIGURATION_ID LIKE CONCAT('%', #{param.configurationId}, '%')
            </if>
            <if test="param.grade != null">
                AND a.GRADE LIKE CONCAT('%', #{param.grade}, '%')
            </if>
            <if test="param.typeCode != null">
                AND a.TYPE_CODE LIKE CONCAT('%', #{param.typeCode}, '%')
            </if>
            <if test="param.operator != null">
                AND a.OPERATOR LIKE CONCAT('%', #{param.operator}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsCgr">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsCgr">
        <include refid="selectSql"></include>
    </select>

    <select id="queryMaxSmallCategory" parameterType="Long" resultType="Long">
        select max(CGR_SMALL_CATEGORY) from HNZSX_GOODS_CGR where CGR_CATEGORY = #{cgrId}
    </select>

</mapper>
