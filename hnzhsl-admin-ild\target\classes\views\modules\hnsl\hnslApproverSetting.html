<!DOCTYPE html>
<html>
<head>
    <title>审批人设置列表</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../js/common.js"></script>
    <style>
        .left-title {
            text-align: center;
        }
        .custom-multiselect {
            border: 1px solid #ccc;
            width: 300px;
            padding: 8px;
            cursor: pointer;
            position: relative;
        }
        .selected {
            background-color: #409eff;
            color: #fff;
        }
        .selected-labels {
            min-height: 20px;
        }
        .options-container {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            border: 1px solid #ccc;
            max-height: 200px;
            overflow-y: auto;
            background: #fff;
            z-index: 1000;
            padding: 10px;
        }
    </style>
</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showIndex" id="isShowList">

        <div class="row">
            <div class="form-group col-md-2">
                <label>所属城市:</label>
                <select class="form-control"
                        style="height: 32px;" v-model="hnslApprovalList.cityCode">
                    <option value=''>全部</option>
                    <option v-for="itme in city" v-bind:value="itme.cityCode">
                        {{itme.cityName}}
                    </option>
                </select>
            </div>

            <div class="form-group col-md-2" style="height: 40px">
                <label>渠道:</label>
                <select class="form-control"
                        style="height: 32px;" v-model="hnslApprovalList.channelType">
                    <option value=''>全部</option>
                    <option v-for="itme in status" v-bind:value="itme.statusId">
                        {{itme.statusName}}
                    </option>
                </select>
            </div>
        </div>

        <div class="grid-btn" style="margin-left: 17Px;">
            <a class="btn btn-primary" @click="query"><i></i>&nbsp;查询</a>
            <a class="btn btn-primary" @click="addApprover"><i></i>&nbsp;新增审批人</a>
        </div>

        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>

    <!--新增审批人-->
    <div v-show="showList" class="panel panel-default">
        <div class="panel-heading" style="font-size: 23px">{{title}}</div>
        <form style="width: 35%;padding-top:20px;">
            <table class="textTable" style="width: 270%">
                <tr>
                    <td class="left-title "><label>姓名:</label></td>
                    <td style="width: 20%;">
                        <input type="text" v-model="hnslApprovalSetting.userName" placeholder="请输入姓名"/>
                    </td>

                    <td class="left-title " style="width:10%;text-align:center;"><label
                            label>手机号:</label></td>
                    <td style="width:20%">
                        <input type="text" v-model="hnslApprovalSetting.userPhone" placeholder="请输入手机号"/>
                    </td>

                    <td class="left-title "><label>身份证号:</label></td>
                    <td style="width: 20%;"><input type="text" v-model="hnslApprovalSetting.idCard" placeholder="请输入身份证"/>
                    </td>
                </tr>

                <tr>
                    <td class="left-title "><label>地市:</label></td>
                    <td style="width: 20%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="hnslApprovalSetting.cityCode">
                            <option value='0'>全部</option>
                            <option value='700'>全省</option>
                            <option value='731'>长沙</option>
                            <option value='732'>湘潭</option>
                            <option value='733'>株洲</option>
                            <option value='734'>衡阳</option>
                            <option value='735'>郴州</option>
                            <option value='736'>常德</option>
                            <option value='737'>益阳</option>
                            <option value='738'>娄底</option>
                            <option value='739'>邵阳</option>
                            <option value='730'>岳阳</option>
                            <option value='743'>湘西</option>
                            <option value='744'>张家界</option>
                            <option value='745'>怀化</option>
                            <option value='746'>永州</option>
                        </select>
                    </td>

                    <td class="left-title "><label>渠道:</label></td>
                    <td style="width: 20%;">
                        <select class="form-control"
                                style="height: 32px;" v-model="hnslApprovalSetting.channelType">
                            <option value='1'>全渠道</option>
                            <option value='2'>校园渠道</option>
                            <option value='3'>电渠互联网卡渠道</option>
                            <option value='4'>其它</option>
                        </select>
                    </td>

                    <td class="left-title "><label>审核角色:</label></td>
                    <td style="width: 20%;">
                        <div class="custom-multiselect" @click="toggleOptions">
                            <div class="selected-labels">{{ selectedLabels }}</div>
                            <div v-show="showOptions" class="options-container">
                                <div v-for="item in roleOptions"
                                     :key="item.value"
                                     :class="{ 'selected': auditRoles.includes(item.value) }"
                                     @click.stop="selectOption(item)">
                                    {{ item.label }}
                                </div>
                            </div>
                            <input type="hidden" name="auditRoles" :value="auditRoles.join(',')" />
                        </div>
                    </td>
                </tr>

<!--                <tr v-show="hnslApprovalSetting.auditRole === '1'">-->
<!--                    <td class="left-title"><label>所属学校:</label></td>-->
<!--                    <td style="width: 20%;">-->
<!--                        <input type="text" v-model="hnslApprovalSetting.schoolName" disabled/>-->
<!--                        <a v-show="bianji1" class="btn btn-primary" @click="school_show()"-->
<!--                           style="float: left; margin-left: 4px; ">编辑</a>-->
<!--                        <a v-show="bianji2" class="btn btn-primary" @click="school_close()"-->
<!--                           style="float: left; margin-left: 4px;">确定</a>-->
<!--                        <a v-show="bianji2" class="btn btn-warning" @click="school_back()"-->
<!--                           style="float: left; margin-left: 4px;">取消</a>-->
<!--                    </td>-->
<!--                </tr>-->
            </table>

<!--            <div v-show="schoolBelongShow">-->
<!--                &lt;!&ndash; 所属团队树 &ndash;&gt;-->
<!--                <table class="textTable">-->
<!--                    <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;</span>-->
<!--                    <div class="tree_pag">-->
<!--                        <div>-->
<!--                            <input type="text" v-model="searchQuery" placeholder="输入学校名称进行筛选"/>-->
<!--                        </div>-->
<!--                        <h5>用户对应学校</h5>-->
<!--                        <div class="tree_content">-->
<!--                            &lt;!&ndash;左边树状图&ndash;&gt;-->
<!--                            <div class="left_tree" >-->
<!--                                <div class="treebox scrollXY">-->
<!--                                    <div class="tree">-->
<!--                                        <ul >-->
<!--                                            <li class="main" v-for="(teamTree , index) in filteredTeamTreeRight"  @click="addCity(teamTree.schoolCode,teamTree.schoolName)" >-->
<!--                                                <a>{{teamTree.schoolName}}</a>-->
<!--                                            </li>-->
<!--                                        </ul>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                            &lt;!&ndash;右边树状图&ndash;&gt;-->
<!--                            <div class="right_tree">-->
<!--                                <div class="treebox scrollXY">-->
<!--                                    <div class="tree">-->
<!--                                        <ul>-->
<!--                                            <li class="main" v-for="(teamTree , index) in teamTreeLeft" @click="delCity(index)"> <a>{{teamTree.schoolName}}</a>-->
<!--                                            </li>-->
<!--                                        </ul>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </table>-->
<!--            </div>-->

            <div style="display: flex;margin: 30px -200px 10px 650px;">
                <div class="col-sm-2 control-label"></div>
                <input type="button" v-show="updateButton"
                       style="margin-left: 95%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
                       class="btn btn-warning" @click="updateReject" value="修改"/>
                <input type="button" v-if="!updateButton"
                       style="margin-left: 95%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
                       class="btn btn-warning" @click="submitReject" value="确定"/>
                &nbsp;&nbsp;<input type="button"
                                   style="margin-left: 5%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
                                   class="btn btn-warning" @click="back" value="返回"/>
            </div>
        </form>
    </div>
</div>

<script src="../../js/modules/hnsl/hnslApproverSettingList.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>