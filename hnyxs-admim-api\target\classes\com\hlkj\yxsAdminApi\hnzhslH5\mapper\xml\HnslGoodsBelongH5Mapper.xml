<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslGoodsBelongH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_h5_goods_belong a
        <where>
            <if test="param.goodsNumber != null">
                AND a.GOODS_NUMBER LIKE CONCAT('%', #{param.goodsNumber}, '%')
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsBelong">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsBelong">
        <include refid="selectSql"></include>
    </select>

    <update id="updateBygoodsNumber" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsBelong">
        update hnsl_h5_goods_belong
        <set>
            <if test="createdUser != null">CREATED_USER = #{createdUser}, </if>
            <if test="createdDate != null">CREATED_DATE = #{createdDate}, </if>
            <if test="schoolCode != null">SCHOOL_CODE = #{schoolCode} </if>
        </set>
        where GOODS_NUMBER = #{goodsNumber}
        <if test="schoolCode != null">
            AND SCHOOL_CODE = #{schoolCode}
        </if>
    </update>


</mapper>
