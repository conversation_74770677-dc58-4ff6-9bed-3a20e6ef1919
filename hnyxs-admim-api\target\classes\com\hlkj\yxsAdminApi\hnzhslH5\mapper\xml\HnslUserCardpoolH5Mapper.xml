<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslUserCardpoolH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT tt.*
        FROM (
        select uc.*, s1.school_name from hnsl_h5_user_cardpool uc
        left join hnsl_h5_school s1 on uc.school_code=s1.school_code
        WHERE 1 = 1
        <if test="param.cardPoolNumber !=null and param.cardPoolNumber !=''">
            AND uc.CARD_POOL_NUMBER LIKE concat(concat('%',#{param.cardPoolNumber}),'%')
        </if>
        <if test="param.cardPoolName !=null and param.cardPoolName !=''">
            AND uc.CARD_POOL_NAME LIKE concat(concat('%',#{param.cardPoolName}),'%')
        </if>
        <if test="param.status !=null and param.status !=''">
            AND uc.STATUS = #{param.status}
        </if>
        <if test="param.schoolName!=null and param.schoolName!=''">
            AND s1.school_name LIKE concat(concat('%',#{param.schoolName}),'%')
        </if>
        <if test="param.hnslType ==2 ">
            AND s1.SCHOOL_GRADE_TYPE in (1,2,5)
        </if>
        <if test="param.hnslType ==3 or param.hnslType ==4 ">
            AND s1.SCHOOL_GRADE_TYPE=#{param.hnslType}
        </if>
        order by uc.ID desc
        ) tt
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserCardpool">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserCardpool">
        <include refid="selectSql"></include>
    </select>

    <delete id="deleteBycardPoolNumber">
        DELETE FROM HNSL_h5_USER_CARDPOOL WHERE CARD_POOL_NUMBER = #{cardPoolNumber}
        <if test="channelId != null">
            and CHANNEL_ID = #{channelId}
        </if>

    </delete>

    <update id="updateCardPool" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserCardpool">
        update HNSL_h5_USER_CARDPOOL
        <set>
            <if test="cardPoolNumber != null">CARD_POOL_NUMBER = #{cardPoolNumber},</if>
            <if test="updatedUser != null">UPDATED_USER = #{updatedUser},</if>
            <if test="cardPoolName != null">CARD_POOL_NAME = #{cardPoolName},</if>
            <if test="createdUser != null">CREATED_USER = #{createdUser},</if>
            <if test="updatedDate != null">UPDATED_DATE = #{updatedDate},</if>
            <if test="createdDate != null">CREATED_DATE = #{createdDate},</if>
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="channelId != null">CHANNEL_ID = #{channelId},</if>
            <if test="cardPoolType != null and cardPoolType!=0">CARD_POOL_TYPE = #{cardPoolType},</if>
            <if test="cityCode != null">CITY_CODE = #{cityCode}</if>
        </set>
        where ID = #{id}
    </update>

    <select id="querySchoolCodesBycardPoolNumber" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserCardpool">
        SELECT * FROM HNSL_h5_USER_CARDPOOL WHERE  CARD_POOL_NUMBER = #{cardPoolNumber}
        <if test="channelId != null">
            and CHANNEL_ID = #{channelId}
        </if>
    </select>
</mapper>
