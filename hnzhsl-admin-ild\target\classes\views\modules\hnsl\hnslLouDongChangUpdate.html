<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta
	content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
	name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet"
	href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet"
	href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<link rel="stylesheet" href="../../css/building.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css"
	rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script
	src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script
	src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
<style type="text/css">
/* .templateShow-Info {
	float: left;
	width: 100%;
	font-size: 20px;
	padding: 10px 25px 0;
}

.templateShow-Info p {
	font-size: 20px;
	float: left;
	text-align: center;
	margin: 10px 0 10px;
	margin: 10px 0 10px;
}

.templateShow-Info p:nth-child(2) {
	color: #999 !important;
	font-size: 16px;
	line-height: 28px;
}
.jifen-bt p {
	font-size: 20px;
	float: left;
	width: 19%;
	text-align: center;
	margin: 10px 0 10px;
}
.jifen-Info p {
	width: 25%;
	float: left;
	text-align: center;
} */
.ui-th-div:nth-child(n+2) {
	text-align: center;
}
</style>
</head>
<body>
	<div id="rrapp" v-cloak>
		<div v-show="showList" id="isShowList">
			<div class="row">
				<div class="form-group col-md-2" style="height: 40px;width: 700px;">
					<label>当前楼栋长:</label> <input type="text" disabled="disabled"
						class="form-control" placeholder="请选择楼栋长"
						v-model="louDongChangName" id="louDongChangName" />
				</div>
				<div class="form-group col-md-2" style="height: 40px">
					<label>身份证|号码|姓名:</label> <input type="text" class="form-control"
						placeholder="身份证|号码|姓名" v-model="condition" />
				</div>
			</div>

			<div class="grid-btn" style="margin-left: 17Px; margin-top: 2%">
				<a v-if="hasPermission('hnslbuilding:query')"
					class="btn btn-primary" @click="query">&nbsp;查询</a> 
					<a v-if="hasPermission('hnslbuilding:update')" class="btn btn-primary" @click="updates">&nbsp;确定</a>
					<a class="btn btn-warning" @click="location.href='/hnxtadmin/modules/hnsl/hnslbuilding.html'">&nbsp;返回</a>
			</div>

			<table id="jqGrid"></table>
			<div id="jqGridPager"></div>

		</div>


		<script src="../../js/modules/hnsl/hnslLouDongChangUpdate.js"></script>
		<script src="../../js/components.js"></script>
</body>
</html>