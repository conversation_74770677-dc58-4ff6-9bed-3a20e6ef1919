<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslWhiteListReportMapper">

    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteReport">
        SELECT t.* FROM hnsl_white_report t
        <where>
            <if test="param.cityCode != null and param.cityCode != '' ">
                AND t.CITY_CODE = #{param.cityCode}
            </if>
            <if test="param.schoolChannel != null and param.schoolChannel != '' ">
                AND t.SCHOOL_CHANNEL = #{param.schoolChannel}
            </if>
            <if test="param.schoolName != null and param.schoolName != '' ">
                AND t.SCHOOL_NAME like concat('%',#{param.schoolName},'%')
            </if>
            <if test="param.periodWhitePhone == 1">
                AND t.FAILURE_DATE >= CURDATE()
                AND t.FAILURE_DATE BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
            </if>
            <if test="param.queryType == 6">
                AND t.CITY_CODE = #{param.queryType}
            </if>
        </where>
    </select>

    <select id="queryListTable" resultType="java.util.Map">
        SELECT
        t.CITY_CODE, DATE(t.CREATED_DATE) AS CREATED_DATE, t.SCHOOL_CHANNEL, t.FAILURE_DATE, t1.SCHOOL_NAME,
        COUNT(DISTINCT t.USER_PHONE) AS totalPhones,
        SUM(CASE WHEN t.FAILURE_DATE BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) AS
        expiringCount,
        SUM(CASE WHEN t.FAILURE_DATE >= CURDATE() AND t.NUMBER_STATUS = 1 or t.NUMBER_STATUS = 4 THEN 1 ELSE 0 END) AS
        unactivatedCount,
        SUM(CASE WHEN t.FAILURE_DATE >= CURDATE() AND t.NUMBER_STATUS = 3 THEN 1 ELSE 0 END) AS activatedCount
        FROM
        hnsl_white_list t
        LEFT JOIN
        hnsl_school t1 ON t.SCHOOL_CODE = t1.SCHOOL_CODE
        <where>
            t.`STATUS` = 1 AND t.FAILURE_DATE > CURDATE()
            <if test="status != null and status !='' ">
                AND t.STATUS = #{status}
            </if>
            <if test="numberStatus != null and numberStatus !='' ">
                AND t.NUMBER_STATUS = #{numberStatus}
            </if>
            <if test="cityCode != null and cityCode != '' ">
                AND t.CITY_CODE = #{cityCode}
            </if>
            <if test="schoolChannel != null and schoolChannel != '' ">
                AND t.SCHOOL_CHANNEL = #{schoolChannel}
            </if>
            <if test="schoolCode != null and schoolCode != '' ">
                AND t.SCHOOL_CODE = #{schoolCode}
            </if>
            <if test="schoolName != null and schoolName != '' ">
                AND t1.SCHOOL_NAME like concat('%',#{schoolName},'%')
            </if>
            <if test="periodWhitePhone == 1">
                AND FAILURE_DATE >= CURDATE()
                AND FAILURE_DATE BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
            </if>
            <if test="failureDate != null and failureDate != '' ">
                AND t.FAILURE_DATE = #{failureDate}
            </if>
            <if test="queryType == 6">
                AND t.CITY_CODE = #{queryCityCode}
            </if>
        </where>
        GROUP BY DATE(t.CREATED_DATE), t.CITY_CODE, t1.SCHOOL_NAME, t.SCHOOL_CHANNEL
    </select>
</mapper>