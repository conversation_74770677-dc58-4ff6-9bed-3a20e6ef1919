<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppRotographMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_rotograph a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.sortSize != null">
                AND a.SORT_SIZE = #{param.sortSize}
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.rotographName != null">
                AND a.ROTOGRAPH_NAME LIKE CONCAT('%', #{param.rotographName}, '%')
            </if>
            <if test="param.rotographUrl != null">
                AND a.ROTOGRAPH_URL LIKE CONCAT('%', #{param.rotographUrl}, '%')
            </if>
            <if test="param.skipSwitch != null">
                AND a.SKIP_SWITCH = #{param.skipSwitch}
            </if>
            <if test="param.skipUrl != null">
                AND a.SKIP_URL LIKE CONCAT('%', #{param.skipUrl}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.shelfPlatformType != null">
                AND a.SHELF_PLATFORM_TYPE = #{param.shelfPlatformType}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxRotograph">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxRotograph">
        <include refid="selectSql"></include>
    </select>

</mapper>
