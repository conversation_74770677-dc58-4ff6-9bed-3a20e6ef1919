<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
	<link rel="stylesheet" href="../../css/bootstrap-datetimepicker.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
	<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.min.js"></script>

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>

	<div v-show="showList">
	  <div class="row">
				<span style="font-size: 19px; height: 32px"> 楼栋名字:
					&nbsp;&nbsp;{{hnslBuilding.buildingName}}</span>
				<div>
					<span style="font-size: 15px; margin-left =5%; height: 32px">
						登记人数 &nbsp;&nbsp;&nbsp;: &nbsp;&nbsp;&nbsp;{{hnslBuilding.buildingRegisterExisting}}</span>
				  <a class="layui-btn layui-btn-small sxBtn"  href="javascript:history.go(-1)" title="返回">
				     <i class="layui-icon" style="font-size: 125%;margin-left: 86%;">返回</i>
				  </a>
				</div>
				<span style="font-size: 15px; height: 32px">本网用户 &nbsp;&nbsp;&nbsp;:
					&nbsp;&nbsp;&nbsp;{{hnslBuilding.networkPhoneExisting+hnslBuilding.networkExisting}}</span>
	 </div>
	 <div style="margin-top: 1%;"></div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
    
    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
											<div class="form-group">
			   	<div class="col-sm-2 control-label">房间编码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.roomNumber" placeholder="房间编码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">修改人</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.updatedUser" placeholder="修改人"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">现有登记入住人数</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.roomRegisterExisting" placeholder="现有登记入住人数"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">本网手机用户现有人数</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.networkPhoneExisting" placeholder="本网手机用户现有人数"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">创建人</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.createdUser" placeholder="创建人"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">本网宽带用户现有人数</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.networkExisting" placeholder="本网宽带用户现有人数"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">楼栋ID</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.buildingId" placeholder="楼栋ID"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">修改时间</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.updatedDate" placeholder="修改时间"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">创建时间</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.createdDate" placeholder="创建时间"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">房间名称</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.roomName" placeholder="房间名称"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">是否可用状态（0:否 1:是</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnslRoom.status" placeholder="是否可用状态（0:否 1:是"/>
			    </div>
			</div>
							<div class="form-group">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div>
		</form>
	</div>

	<!--修改入学年级弹窗-->
	<div class="modal"  id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true">×
					</button>
					<h4 class="modal-title" id="myModalLabel">
						修改房间入学年级
					</h4>
				</div>
				<div class="modal-body" style="display: flex;align-items: center;">
					<label style="margin-right: 10px;margin-bottom: 0;">选择年份:</label>
					<div class="input-group col-ms-2 ">
						<input type="text" class="form-control form-filter yearpicker" id="dateTimeRange"
							   placeholder="日期">
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default"
							data-dismiss="modal">关闭
					</button>
					<button type="button" class="btn btn-primary" @click="updateRoomResult">
						修改
					</button>
				</div>
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->
</div>



<script src="../../js/modules/hnsl/hnslroom.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>