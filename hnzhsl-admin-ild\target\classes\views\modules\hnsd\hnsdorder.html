<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>

	<div v-show="showList">
		
		<div class="row">
			
			<div class="form-group col-md-2" style="height:40px">
			  <label>订单编号</label> 
		     <input type="text" class="form-control" placeholder="订单编号"  v-model="seachOrder.orderId"/>
		     </div>
		   
		   <div class="form-group col-md-2" style="height:40px">
		      <label>所在地市</label> 
		      <select class="form-control" style="height: 32px;"  v-model="seachOrder.citycode">
					  <option value='' >全部</option>
					  <option v-for="itme in city" v-bind:value="itme.cityCode">
                          {{itme.cityName}}			   
					  </option>
					</select>
		   </div>
	
				<div class="form-group col-md-2">
					<label>订单状态</label> 
					<select class="form-control" style="height: 32px;" v-model="seachOrder.orderStatus" >
					  <option value='0' >全部</option>
					  <option value='1' >实名提交</option>
					  <option value='2' >审核通过</option>
					  <option value='3' >激活成功</option>
					</select>
				</div>
				
				<div class="form-group col-md-2">
					<label>客户姓名</label> 
				<input type="text" style="height: 32px;" class="form-control" placeholder="客户姓名" v-model="seachOrder.customerName"  />
				</div>
				<div class="form-group col-md-2">
					<label>客户身份证号码</label> 
				<input type="text" style="height: 32px;" class="form-control" placeholder="客户身份证号码"  v-model="seachOrder.customerCard" />
				</div>
				
				<div class="form-group col-md-2">
					<label>预占手机号码</label>
					<input type="text" style="height: 32px;" class="form-control" placeholder="预占手机号码" v-model="seachOrder.customerPhone"  />
				</div>
				
				<div class="row2">
					<div class="form-group col-md-2" style="height: 32px;">
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							id="dateTimeRange" @keyup.enter="query" value="" type="text" 
							placeholder="创建日期"> <span class="input-group-addon"> 
							<i class="fa fa-calendar bigger-110"></i> 
 						</span>
 						 <input name="beginTime" id="beginTime" type="hidden" >
 						 <input name="endTime" id="endTime" type="hidden" >  
 					</div> 
				  </div> 
			    </div>
		    <a v-if="hasPermission('hnsdgoods:query')" class="btn btn-primary"  @click="query">&nbsp;查&nbsp;&nbsp;&nbsp;询</a>
			<a v-if="hasPermission('hnsdgoods:update')" class="btn btn-primary" @click="update">&nbsp;批量激活</a>
			<a v-if="hasPermission('hnsdgoods:delete')" class="btn btn-primary" @click="outOrder">&nbsp;导出订单</a>
	</div>
		
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
    
     <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
	<form class="form-horizontal">
	
	
	
	<table class="textTable">	
	        <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;订单基本信息</span>
	        <tr>
				<td class="leftTd"><label><span>订单编号:</span></label></td>
				<td>
					<input type="text" class="form-control" v-model="hnsdOrder.orderId" placeholder="订单编号"/>
				</td>
				
				<td class="leftTd"><label><span>订购人:</span></label></td>
				<td>
					<input type="text" class="form-control" v-model="hnsdOrder.customerName" placeholder="订购人"/>
				</td> 
			</tr>
			
        	<tr>
        	    <td class="leftTd"><label><span>所属城市:</span></label></td>
				<td>
				    <select class="form-control" style="height: 32px;" v-model="hnsdOrder.citycode">
					  <option v-for="itme in city" v-bind:value="itme.cityCode">
                          {{itme.cityName}}			   
					  </option>
					</select>
				</td>
				<td class="leftTd"><label><span>联系电话:</span></label></td>
				<td>
					<input type="text" class="form-control" v-model="hnsdOrder.customerContactPhone" placeholder="联系电话"/>
				</td>
			</tr>
			
            <tr>
                <td class="leftTd"><label><span>下单时间:</span></label></td>
				<td>
					<input type="text" class="form-control" v-model="hnsdOrder.createdDate" placeholder="下单时间"/>
				</td>
                <td class="leftTd"><label><span>套餐名称:</span></label></td>
				<td>
					<input type="text" class="form-control" v-model="hnsdOrder2.goodsName" placeholder="套餐名称"/>
				</td>
            </tr>  
            
            <tr>
                <td class="leftTd"><label><span>ICCID号:</span></label></td>
				<td>
					<input type="text" class="form-control" v-model="hnsdOrder.iccid" placeholder="ICCID号"/>
				</td>
                <td class="leftTd"><label><span>订购号码：</span></label></td>
				<td>
			      <input type="text" class="form-control" v-model="hnsdOrder.customerPhone" placeholder="订购号码"/>
				</td>
			</tr>
			
			
			
			<tr>
			    <td class="leftTd"><label><span>订单状态:</span></label></td>
				<td>
				   <select class="form-control" v-model="hnsdOrder.orderStatus" style="height: 35px">
					    <option value='1' >实名提交</option>
					    <option value='2' >审核通过</option>
					    <option value='3' >激活成功</option>
					</select>
				</td> 
			     <td class="leftTd"><label><span>渠道：</span></label></td>
				 <td>
			      <input type="text" class="form-control" v-model="hnsdOrder2.channel" placeholder=""/>
				 </td>
			</tr>
			<tr>	 
				  <td class="leftTd"><label><span>角色：</span></label></td>
				 <td>
			      <input type="text" class="form-control" v-model="hnsdOrder2.role" placeholder=""/>
				 </td>
				 
				 <td class="leftTd"><label><span>团队:</span></label></td>
				 <td>
			      <input type="text" class="form-control" v-model="hnsdOrder2.team" placeholder="团队"/>
				 </td>
			</tr>
			<tr>
			    <td class="leftTd"><label><span>部门:</span></label></td>
				 <td>
					<input type="text" class="form-control" v-model="hnsdOrder2.department" placeholder="团队"/>
				 </td>
				 <td class="leftTd"><label><span>预存款:</span></label></td>
				 <td>
			      <input type="text" class="form-control" v-model="hnsdOrder.phonePreprice" placeholder="预存话费"/>
				 </td>
			</tr>
			<tr>
			    <td class="leftTd"><label><span>保底费:</span></label></td>
				 <td>
			      <input type="text" class="form-control" v-model="hnsdOrder.phoneNbrprice" placeholder="保底消费"/>
				 </td>
				 <td class="leftTd"><label><span>订单金额:</span></label></td>
				 <td>
			      <input type="text" class="form-control" v-model="hnsdOrder.orderPrice" placeholder="订单金额"/>
				 </td>
			</tr>
			
			<tr>
			    <td class="leftTd"><label><span>支付渠道:</span></label></td>
				 <td>
			      <input type="text" class="form-control" v-model="hnsdOrder.payChannel" placeholder="支付渠道"/>
				 </td>
				 <td class="leftTd"><label><span>CRM订单号:</span></label></td>
				 <td>
			      <input type="text" class="form-control" v-model="hnsdOrder.crmOrderId" placeholder="CRM订单号"/>
				 </td>
			</tr>
			
      	</table>
      	
			<div class="form-group">
				<div class="col-sm-2 control-label"></div>
<!-- 				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
 -->				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div> 
		</form>
	</div>
    
    
    
<!--     <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
											<div class="form-group">
			   	<div class="col-sm-2 control-label">电子发票：1、一次性发票 2、月结发票</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.invoicesType" placeholder="电子发票：1、一次性发票 2、月结发票"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">固定电话号码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.telephone" placeholder="固定电话号码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">订单回退原因</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.returnReason" placeholder="订单回退原因"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">实名提交时间</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.orderSubmitDate" placeholder="实名提交时间"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">支付方式（微信、支付宝）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.payChannel" placeholder="支付方式（微信、支付宝）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">修改时间</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.updatedDate" placeholder="修改时间"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">安装地址：市</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.installCity" placeholder="安装地址：市"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">审核通过时间</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.orderReviewedDate" placeholder="审核通过时间"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">终端直降串码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.preferentialTerminalId" placeholder="终端直降串码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">固话靓号审批验证码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.telephoneApproveVerifycode" placeholder="固话靓号审批验证码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">预存方案（4：100元预存 5:200元预存 6:300元预存）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.preType" placeholder="预存方案（4：100元预存 5:200元预存 6:300元预存）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">创建时间</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.createdDate" placeholder="创建时间"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">固话靓号审批单号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.telephoneApproveNumber" placeholder="固话靓号审批单号"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">安装地址：街道等详细信息</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.installAddress" placeholder="安装地址：街道等详细信息"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">业务底单:1、推送 2、不推送</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.businessOrder" placeholder="业务底单:1、推送 2、不推送"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">安装地址：区</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.installArea" placeholder="安装地址：区"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">机顶盒类型（1：客户自备 2：免费提供）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.stbModel" placeholder="机顶盒类型（1：客户自备 2：免费提供）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">宽带同地址移机（移机流程）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.bandwidthAddressSyn" placeholder="宽带同地址移机（移机流程）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">客户身份证号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.customerCard" placeholder="客户身份证号"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">订单号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.orderId" placeholder="订单号"/>
			    </div>
			</div>
						
									<div class="form-group">
			   	<div class="col-sm-2 control-label">靓号审批验证码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.phoneApproveVerifycode" placeholder="靓号审批验证码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">租用光猫和机顶盒费用</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.stbPrice" placeholder="租用光猫和机顶盒费用"/>
			    </div>
			</div>
							
									<div class="form-group">
			   	<div class="col-sm-2 control-label">安装地址编码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.installAddressId" placeholder="安装地址编码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">客户身份证地址</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.customerAddress" placeholder="客户身份证地址"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">终端串码(合约分机)</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.terminalId" placeholder="终端串码(合约分机)"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">创建人</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.createdUser" placeholder="创建人"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">靓号审批单号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.phoneApproveNumber" placeholder="靓号审批单号"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">揽机工号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.userId" placeholder="揽机工号"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">固话安装地址</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.telephoneInstallAddress" placeholder="固话安装地址"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">备注</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.orderRemark" placeholder="备注"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">固话安装地址编码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.telephoneInstallAddressId" placeholder="固话安装地址编码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">补卡号码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.replacePhone" placeholder="补卡号码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">固话预存话费（固话）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.telephonePreprice" placeholder="固话预存话费（固话）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">预存话费（主卡）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.phonePreprice" placeholder="预存话费（主卡）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">同步bps实名单（1：成功 0：失败）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.bpsRealnameSynchro" placeholder="同步bps实名单（1：成功 0：失败）"/>
			    </div>
			</div>
							
									<div class="form-group">
			   	<div class="col-sm-2 control-label">激活成功时间</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.orderActivateDate" placeholder="激活成功时间"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">固话同地址移机（移机流程）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.telephoneAddressSyn" placeholder="固话同地址移机（移机流程）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">BPS系统orderId</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.bpsOrderId" placeholder="BPS系统orderId"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">CRM系统订单确认(1:成功 0：失败)</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.crmSureSynchro" placeholder="CRM系统订单确认(1:成功 0：失败)"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">租用机顶盒（1：客户自备 2：装机师傅代购）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.itvType" placeholder="租用机顶盒（1：客户自备 2：装机师傅代购）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">修改人</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.updatedUser" placeholder="修改人"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">ITV同地址移机（移机流程）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.itvAddressSyn" placeholder="ITV同地址移机（移机流程）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">身份证免冠照</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.identityCardImage3" placeholder="身份证免冠照"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">固话靓号审批金额</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.telephoneApprovePrice" placeholder="固话靓号审批金额"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">电子邮箱（电子发票推送的邮箱）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.invoicesEmail" placeholder="电子邮箱（电子发票推送的邮箱）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">X元预存金额</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.xPreprice" placeholder="X元预存金额"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">商品编码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.goodsNumber" placeholder="商品编码"/>
			    </div>
			</div>
								
									<div class="form-group">
			   	<div class="col-sm-2 control-label">靓号审批金额</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.phoneApprovePrice" placeholder="靓号审批金额"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">租用光猫和机顶盒（1：客户自备 2：装机师傅代购）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.stbType" placeholder="租用光猫和机顶盒（1：客户自备 2：装机师傅代购）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">主卡号所属私有号池</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.cardpool" placeholder="主卡号所属私有号池"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">预约安装日期（YYYY-MM-DD）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.installDay" placeholder="预约安装日期（YYYY-MM-DD）"/>
			    </div>
			</div>
									
									<div class="form-group">
			   	<div class="col-sm-2 control-label">固话保底消费（固话）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.telephoneNbrprice" placeholder="固话保底消费（固话）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">登录人的揽机工号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.loginUserId" placeholder="登录人的揽机工号"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">${column.comments}</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.citycode" placeholder="${column.comments}"/>
			    </div>
			</div>
						
									<div class="form-group">
			   	<div class="col-sm-2 control-label">身份证正面照</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.identityCardImage1" placeholder="身份证正面照"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">客户身份证号有效期</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.customerCardValidity" placeholder="客户身份证号有效期"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">同步bps正式单（1：成功 0：失败）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.bpsFormalSynchro" placeholder="同步bps正式单（1：成功 0：失败）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">客户custId</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.custId" placeholder="客户custId"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">CRM系统orderId</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.crmOrderId" placeholder="CRM系统orderId"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">预约安装时段（1：上午 2：下午）</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.installTimeInterval" placeholder="预约安装时段（1：上午 2：下午）"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">身份证反面照</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.identityCardImage2" placeholder="身份证反面照"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">活体视频</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="hnsdOrder.livingbodyVideo" placeholder="活体视频"/>
			    </div>
			</div>
							<div class="form-group">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div>
		</form>
	</div> -->
</div>

<script src="../../js/modules/hnsd/hnsdorder.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>