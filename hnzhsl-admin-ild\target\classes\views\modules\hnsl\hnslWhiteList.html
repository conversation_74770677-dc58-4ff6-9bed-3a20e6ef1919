<!DOCTYPE html>
<html>
<head>
    <title>白名单清单</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showList">
        <div class="row">
            <div class="form-group col-md-2" style="height: 40px">
                <label>批次号:</label>
                <input type="text" class="form-control"  v-model="hnslWhiteList.batchCode"/>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>熟卡号码:</label>
                <input type="text" class="form-control" v-model="hnslWhiteList.userPhone"/>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>所属地市:</label>
                <select class="form-control"
                        style="height: 32px;" v-model="cityCode">
                    <option value=''>全部</option>
                    <option v-for="itme in city" v-bind:value="itme.cityCode">
                        {{itme.cityName}}
                    </option>
                </select>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>学校名称:</label>
                <input type="text" class="form-control" v-model="hnslWhiteList.schoolName"/>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>ICCID卡号:</label>
                <input type="text" class="form-control" v-model="hnslWhiteList.iccid"/>
            </div>
        </div>
        <div class="row2">
            <div class="form-group col-md-2">
                <label>状态:</label>
                <select class="form-control" style="height: 32px;" v-model="hnslWhiteList.status">
                    <option value=''>全部</option>
                    <option v-for="itme in status" v-bind:value="itme.statusId">
                        {{itme.statusName}}
                    </option>
                </select>
            </div>
            <div class="form-group col-md-2" style="height: 32px;">
                <label>创建日期:</label>
                <div class="input-group col-ms-2 ">
                    <input class="form-control pull-left dateRange date-picker "
                           id="dateTimeRange" @keyup.enter="query" value="" type="text"
                           placeholder="日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
 						</span>
                    <input name="beginTime" id="beginTime" type="hidden">
                    <input name="endTime" id="endTime" type="hidden">
                </div>
            </div>
            <div class="form-group col-md-2">
                <label>激活状态:</label>
                <select class="form-control" style="height: 32px;" v-model="hnslWhiteList.orderStatus">
                    <option value=''>全部</option>
                    <option v-for="itme in orderStatus" v-bind:value="itme.statusId">
                        {{itme.statusName}}
                    </option>
                </select>
            </div>
            <div class="form-group col-md-2">
                <label>有效期内熟卡号码:</label>
                <select class="form-control" style="height: 32px;" v-model="hnslWhiteList.periodWhitePhone">
                    <option value=''>展示全部</option>
                    <option v-for="itme in periodWhitePhone" v-bind:value="itme.statusId">
                        {{itme.statusName}}
                    </option>
                </select>
            </div>
        </div>
        <div class="grid-btn" style="margin-left: 17Px; margin-top: 18px">
            <a v-if="hasPermission('hnslwhitelist:query')" class="btn btn-primary" @click="query"><i></i>&nbsp;查询</a>
            <a v-if="hasPermission('hnslwhitelist:update')" class="btn btn-primary" @click="update"><i
                    class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
            <a v-if="hasPermission('hnslwhitelist:importUser')" class="btn btn-primary" @click="templateShowI">&nbsp;导入白名单</a>
            <a v-if="hasPermission('hnslwhitelist:output')" class="btn btn-primary"
               @click="outTableOrder($event,'2')"><i
                    class="fa fa-trash-o"></i>&nbsp;下载清单</a>
<!--            <a v-if="hasPermission('hnslwhitelist:output')" class="btn btn-primary"-->
<!--               @click="outTableOrder($event,'1')"><i-->
<!--                    class="fa fa-trash-o"></i>&nbsp;导出报表</a>-->
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>

    <!--模板下载-->
    <div v-show="!templateShow" id="templateShow"
         class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <div class="form-horizontal" style="padding-top: 0px; width: 100%;">
            <form id="uploadImg" enctype="multipart/form-data">
                <div class="templateShow-Info">
                    <p>下载模板：</p>
                    <p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
                </div>
                <div style="margin-left: 125px;">
                    <a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
                </div>
                <div class="templateShow-Info">
                    <p>上传文件：</p>
                    <p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
                </div>
                <div style="margin-left: 125px;">
                    <a v-if="hasPermission('hnsduser:importUser')"
                       class="btn btn-primary" @click="importUser">&nbsp;开始导入</a> <input
                        style="display: none;" name="uploadFile" id="uploadFile"
                        type="file" @change="uploadFile"/>
                </div>
                <div style="width: 100%; text-align: center;">
                    <input type="button" class="btn btn-warning" @click="reload"
                           value="返回"/>
                </div>
            </form>
        </div>
    </div>

    <div class="modal" id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" style="padding: 70px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true">×
                    </button>
                    <h4 class="modal-title" id="myModalLabel1" style="text-align: center">数据下载审批人选择</h4>
                    <div style="display: flex">
                        <input placeholder="请输入审批人姓名或手机号" v-model="searchText"
                               @input="onInput"/>
                    </div>

                    <table class="table table-bordered" style="width: 100%; text-align: center;margin-top: 10px">
                        <tbody id="myTable">
                        <tr>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                    <div style="width: 100%;text-align: center;">
                        <a href="#" id="prevPage" @click="prevPage">上一页</a>
                        <span id="currentPage">1</span>
                        <a href="#" id="nextPage" @click="nextPage">下一页</a>
                    </div>

                    <div style="text-align: right;margin-top: 20px;">
                        <div class="col-sm-2 control-label"></div>
                        <input type="button" class="btn btn-warning" @click="submitApprover" value="确定"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<script src="../../js/modules/hnsl/hnslWhiteList.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>