<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsGwdbTerminalRelMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_gwdb_terminal_rel a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsId != null">
                AND a.GOODS_ID = #{param.goodsId}
            </if>
            <if test="param.deviceType != null">
                AND a.DEVICE_TYPE = #{param.deviceType}
            </if>
            <if test="param.deviceName != null">
                AND a.DEVICE_NAME LIKE CONCAT('%', #{param.deviceName}, '%')
            </if>
            <if test="param.productType != null">
                AND a.PRODUCT_TYPE = #{param.productType}
            </if>
            <if test="param.productName != null">
                AND a.PRODUCT_NAME LIKE CONCAT('%', #{param.productName}, '%')
            </if>
            <if test="param.sourceType != null">
                AND a.SOURCE_TYPE = #{param.sourceType}
            </if>
            <if test="param.sourceName != null">
                AND a.SOURCE_NAME LIKE CONCAT('%', #{param.sourceName}, '%')
            </if>
            <if test="param.serviceType != null">
                AND a.SERVICE_TYPE = #{param.serviceType}
            </if>
            <if test="param.serviceName != null">
                AND a.SERVICE_NAME LIKE CONCAT('%', #{param.serviceName}, '%')
            </if>
            <if test="param.terminalType != null">
                AND a.TERMINAL_TYPE = #{param.terminalType}
            </if>
            <if test="param.terminalName != null">
                AND a.TERMINAL_NAME LIKE CONCAT('%', #{param.terminalName}, '%')
            </if>
            <if test="param.salesProductsId != null">
                AND a.SALES_PRODUCTS_ID LIKE CONCAT('%', #{param.salesProductsId}, '%')
            </if>
            <if test="param.salesProductsName != null">
                AND a.SALES_PRODUCTS_NAME LIKE CONCAT('%', #{param.salesProductsName}, '%')
            </if>
            <if test="param.fee != null">
                AND a.FEE = #{param.fee}
            </if>
            <if test="param.remarks != null">
                AND a.REMARKS LIKE CONCAT('%', #{param.remarks}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsGwdbTerminalRel">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsGwdbTerminalRel">
        <include refid="selectSql"></include>
    </select>

</mapper>
