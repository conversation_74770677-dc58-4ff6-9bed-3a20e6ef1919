<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxSilverHairTerminalMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_silver_hair_terminal a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.grade != null">
                AND a.GRADE = #{param.grade}
            </if>
            <if test="param.equityName != null">
                AND a.EQUITY_NAME LIKE CONCAT('%', #{param.equityName}, '%')
            </if>
            <if test="param.terminalName != null">
                AND a.TERMINAL_NAME LIKE CONCAT('%', #{param.terminalName}, '%')
            </if>
            <if test="param.terminalNumber != null">
                AND a.TERMINAL_NUMBER LIKE CONCAT('%', #{param.terminalNumber}, '%')
            </if>
            <if test="param.createDate != null">
                AND a.CREATE_DATE LIKE CONCAT('%', #{param.createDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSilverHairTerminal">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSilverHairTerminal">
        <include refid="selectSql"></include>
    </select>

</mapper>
