{"groups": [{"name": "config", "type": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "slauth", "type": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}], "properties": [{"name": "config.acctiond", "type": "java.lang.String", "description": "是否测试 @return", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.duanxin-error-nums", "type": "java.lang.String", "description": "短信验证码验证错误", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.duanxin-error-xianzhi-nums", "type": "java.lang.Integer", "description": "短信验证码输入错误次数", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.duanxin-length", "type": "java.lang.Integer", "description": "验证码的长度", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.duanxin-time", "type": "java.lang.Integer", "description": "验证码保存时长(秒)", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.duanxin-xiangzhi-data-nums", "type": "java.lang.Integer", "description": "一天限制次数", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.duanxin-xiangzhi-nums", "type": "java.lang.Integer", "description": "区域限制次数", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.hnzhsl-file-path", "type": "java.lang.String", "description": "智慧扫楼文件上传地址", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.hnzsx-admin-goods", "type": "java.lang.String", "description": "省集约上传商品图片桶名称", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.hnzsx-file-img-path", "type": "java.lang.String", "description": "掌上销图片上传地址", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.hnzsx-img-path", "type": "java.lang.String", "description": "图片访问地址", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.ip-arress-one-day-limit", "type": "java.lang.String", "description": "ip限制发送key", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.<PERSON><PERSON><PERSON><PERSON>", "type": "java.lang.Bo<PERSON>an", "description": "是否发送接着上面的短信信息（true:是，false：否）", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.moblie-one-day-limit", "type": "java.lang.String", "description": "手机号码限制发送key", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.one-minute-limit", "type": "java.lang.String", "description": "限制发送key", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.one-minute-limit-time", "type": "java.lang.Integer", "description": "限制发送时间(秒)", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.open-office-home", "type": "java.lang.String", "description": "OpenOffice的安装目录", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.sms-pars-url", "type": "java.lang.String", "description": "短信发送地址", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.swagger-base-package", "type": "java.lang.String", "description": "swagger扫描包", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.swagger-description", "type": "java.lang.String", "description": "swagger文档描述", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.swagger-host", "type": "java.lang.String", "description": "swagger地址", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.swagger-title", "type": "java.lang.String", "description": "swagger文档标题", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.swagger-version", "type": "java.lang.String", "description": "swagger文档版本号", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.thumbnail-size", "type": "java.lang.Integer", "description": "文件上传生成缩略图的大小(kb)", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.token-expire-time", "type": "java.lang.Long", "description": "token过期时间, 单位秒 60 * 60 * 24L 默认一天 调整为 30分钟", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.token-key", "type": "java.lang.String", "description": "生成token的密钥Key的base64字符", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.token-refresh-time", "type": "java.lang.Integer", "description": "token快要过期自动刷新时间, 单位分钟", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.upload-location", "type": "java.lang.Integer", "description": "文件上传磁盘位置", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.upload-uuid-name", "type": "java.lang.Bo<PERSON>an", "description": "文件上传是否使用uuid命名", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.user-phones", "type": "java.lang.String", "description": "放行的手机号码", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "config.zuizhong-duanxin-nums", "type": "java.lang.String", "description": "最终的短信验证码编码", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.ConfigProperties"}, {"name": "sjyauth.appid", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "sjyauth.appkey", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "sjyauth.client-id", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "sjyauth.client-secret", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "sjyauth.grant-type", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "sjyauth.method-get-access-token", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "sjyauth.method-get-code", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "sjyauth.private-key", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "sjyauth.redirect-uri", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "sjyauth.survival", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig"}, {"name": "slauth.appid", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}, {"name": "slauth.appkey", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}, {"name": "slauth.client-id", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}, {"name": "slauth.client-secret", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}, {"name": "slauth.grant-type", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}, {"name": "slauth.method-get-access-token", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}, {"name": "slauth.method-get-code", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}, {"name": "slauth.private-key", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}, {"name": "slauth.redirect-uri", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}, {"name": "slauth.survival", "type": "java.lang.String", "sourceType": "com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig"}], "hints": []}