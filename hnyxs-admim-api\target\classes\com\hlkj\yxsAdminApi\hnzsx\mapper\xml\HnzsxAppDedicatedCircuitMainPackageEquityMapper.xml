<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppDedicatedCircuitMainPackageEquityMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*,p.GOODS_NAME
        FROM hnzsx_dedicated_circuit_main_package_equity a
        inner join hnzsx_dedicated_circuit_main_package p on a.MAIN_PACKAGE_ID = p.ID
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.mainPackageId != null">
                AND a.MAIN_PACKAGE_ID = #{param.mainPackageId}
            </if>
            <if test="param.equityLevel != null">
                AND a.EQUITY_LEVEL LIKE CONCAT('%', #{param.equityLevel}, '%')
            </if>
            <if test="param.offerId != null">
                AND a.OFFER_ID LIKE CONCAT('%', #{param.offerId}, '%')
            </if>
            <if test="param.offerName != null">
                AND a.OFFER_NAME LIKE CONCAT('%', #{param.offerName}, '%')
            </if>
            <if test="param.depositAmount != null">
                AND a.DEPOSIT_AMOUNT LIKE CONCAT('%', #{param.depositAmount}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.UPDATE_DATE LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
            <if test="param.equityLevelName != null">
                AND a.EQUITY_LEVEL_NAME LIKE CONCAT('%', #{param.equityLevelName}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitMainPackageEquity">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitMainPackageEquity">
        <include refid="selectSql"></include>
    </select>

</mapper>
