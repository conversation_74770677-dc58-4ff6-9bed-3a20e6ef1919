<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsTerminalEqpackRelMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_terminal_eqpack_rel a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsDetailsId != null">
                AND a.GOODS_DETAILS_ID = #{param.goodsDetailsId}
            </if>
            <if test="param.terminalTypeId != null">
                AND a.TERMINAL_TYPE_ID = #{param.terminalTypeId}
            </if>
            <if test="param.eqpackCgrId != null">
                AND a.EQPACK_CGR_ID = #{param.eqpackCgrId}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsTerminalEqpackRel">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsTerminalEqpackRel">
        <include refid="selectSql"></include>
    </select>

</mapper>
