<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="Cache-Control" content="no-cache, must-revalidate">
<meta http-equiv="expires" content="Wed, 26 Feb 1997 08:21:57 GMT">
<meta http-equiv="expires" content="-1">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<!-- <link rel="stylesheet" href="../../css/tree.css">
 --><script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>
        <script>
            $(document).ready(function () {
                $(".main  a").click(
                        function () {
                            $(this).next().toggle();
                        }
                );
            });
        </script>
<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
<style>
            .main ul{
                display: none;
            }
        </style>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">
	
	<div class="row">
			
			<div class="form-group col-md-2" style="height:40px">
			  <label>产品名称</label> 
		     <input type="text" class="form-control" placeholder="产品名称" v-model="seachGoods.goodsName" />
		     </div>
		   
		   <div class="form-group col-md-2" style="height:40px">
		      <label>产品编码</label> 
		     <input type="text" class="form-control" placeholder="产品编码" v-model="seachGoods.goodsNumber" />
		   </div>
	
				<div class="form-group col-md-2">
					<label>商品类型</label> 
					<select class="form-control" style="height: 32px;"  v-model="seachGoods.goodsType">
					  <option value='0' >全部</option>
					  <option value='1' >号卡</option>
					  <option value='2' >宽带</option>
					  <option value='3' >融合</option>
					  <option value='4' >合约分期</option>
					</select>
				</div>
				
				<div class="form-group col-md-2">
					<label>商品子类别</label> 
					<select class="form-control" style="height: 32px;"  v-model="seachGoods.goodsChildTypeNumber">
					  <option value='0' >全部</option>
					  <option value='1' >单卡新装</option>
					  <option value='2' >融合礼包</option>
					  <option value='3' >宽带电视</option>
					  <option value='4' >合约分期</option>
					  <option value='5' >号卡补卡</option>
					  <option value='6' >终端移机</option>
					</select>
				</div>
				
				<div class="form-group col-md-2">
					<label>所属团队</label>
					<select  id="prov" name="prov" class="form-control" style="height: 32px;" v-model="seachGoods.teamCode">
                      <option v-for="item in listProv" v-bind:value="item.teamCode" :key="item.teamCode">{{item.teamName}}</option>
                     </select>
				</div>
				<div class="form-group col-md-2">
					<label>状态</label>
					<select class="form-control" style="height: 32px;"  v-model="seachGoods.status">
					  <option value='' >全部</option>
					  <option value="1">在架</option>
					  <option value="0">下架</option>
					</select>
				</div>
				
				<div class="row2">
					<div class="form-group col-md-2" style="height: 32px;">
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							id="dateTimeRange" @keyup.enter="query" value="" type="text" 
							placeholder="创建日期"> <span class="input-group-addon"> 
							<i class="fa fa-calendar bigger-110"></i> 
 						</span>
 						 <input name="beginTime" id="beginTime" type="hidden" v-model='seachGoods.beginTime'>
 						 <input name="endTime" id="endTime" type="hidden" v-model='seachGoods.endTime'>  
 					</div> 
				  </div> 
			    </div>
		    <a v-if="hasPermission('hnsdgoods:query')" class="btn btn-primary"  @click="query">&nbsp;查询</a>
			<a v-if="hasPermission('hnsdgoods:save')" class="btn btn-primary"  @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
			<a v-if="hasPermission('hnsdgoods:update')" class="btn btn-primary" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
	</div>
	
		
		
		
		
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
    
    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
	<form class="form-horizontal">
	
	<table class="textTable">	
	        <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;产品基本信息</span>
	        <tr>
				<td class="leftTd"><label><span>商品类型：</span></label></td>
				<td>
					<select class="form-control" style="height: 32px;" v-model="addGoods.goodsType">
					  <option value='1' >号卡</option>
					  <option value='2' >宽带</option>
					  <option value='3' >融合</option>
					  <option value='4' >合约分期</option>
					</select>
				</td>
				
				<td class="leftTd"><label><span>号卡类型：</span></label></td>
				<td>
					<select class="form-control" style="height: 32px;" v-model="addGoods.cardType">
					  <option value='' >请选择</option>
					  <option value='1' >天翼畅享</option>
					  <option value='2' >乐享4G</option>
					  <option value='3' >天翼大流量</option>
					  <option value='4' >其他</option>
					</select>
				</td> 
				
				
			</tr>
			
        	<tr>
        	    <td class="leftTd"><label><span>商品子类别：</span></label></td>
				<td>
					<select class="form-control" style="height: 32px;" v-model="addGoods.goodsChildtypeNumber">
					  <option value='1' >单卡新装</option>
					  <option value='2' >融合礼包</option>
					  <option value='3' >宽带电视</option>
					  <option value='4' >合约分期</option>
					  <option value='5' >号卡补卡</option>
					  <option value='6' >终端移机</option>
					</select>
				</td>
				<td class="leftTd"><label><span>宽带类型：</span></label></td>
				<td>
					<select class="form-control" style="height: 32px;" v-model="addGoods.bandwidthType"> 
					  <option value='' >请选择</option>
					  <option value='1' >电视宽带</option>
					  <option value='2' >单宽带</option>
					</select>
				</td>
				
			</tr>
			
            <tr>
                <td class="leftTd"><label><span>产品名称：</span></label></td>
				<td>
					<input type="text" value="" id="goodsName" v-model="addGoods.goodsName" />
				</td>
                <td class="leftTd"><label><span>产品编码：</span></label></td>
				<td>
						<input type="text" value=""  id="goodsNumber" v-model="addGoods.goodsNumber" />
				</td>
            </tr>  
            
            <tr>
                <td class="leftTd"><label><span>预存款：</span></label></td>
				<td>
					<input type="text" value="" id="prestore" v-model="addGoods.prestore" />
				</td>
                <td class="leftTd"><label><span>主套餐销售品编码：</span></label></td>
				<td>
					<input type="text" value=""   id="goodsMianNumber" v-model="addGoods.goodsMianNumber" />
				</td>
				
			</tr>
			
			<tr>
			    <td class="leftTd"><label><span>制卡费：</span></label></td>
				<td>
					<input type="text" value=""   id="productionPrice"  v-model="addGoods.productionPrice"/>
				</td> 
			     <td class="leftTd"><label><span>保底消费：</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" v-model="addGoods.minPrice" />
				 </td>
				
			</tr>
			<tr>	 
				  <td class="leftTd"><label><span>安装费/连接费：</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice"  v-model="addGoods.installPrice" />
				 </td>
				 <td class="leftTd"><label><span>带宽:</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" v-model="addGoods.bandwidth" />
				 </td>
			</tr>
			<tr>
			    <td class="leftTd"><label><span>号卡主套餐名称:</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" v-model="addGoods.cardGoodsName" />
				 </td>
			    <td class="leftTd"><label><span>号卡主套餐编码:</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" v-model="addGoods.cardGoodsNumber" />
				 </td>
			</tr>
			<tr>
			    <td class="leftTd"><label><span>宽带主套餐名称:</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" v-model="addGoods.bandwidthGoodsName" />
				 </td>
				 <td class="leftTd"><label><span>宽带主套餐编码:</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" v-model="addGoods.bandwidthGoodsNumber" />
				 </td>
			</tr>
			
			
			<tr>
			    <td class="leftTd"><label><span>可选包销售品编码:</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" placeholder="多个用逗号隔开" v-model="addGoods.keXuanBaoNumber" />
				 </td>
				 <td class="leftTd"><label><span>优惠包销售品编码:</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" placeholder="多个用逗号隔开" v-model="addGoods.youHuiBaoNumber" />
				 </td>
			</tr>
			
			<tr>
			     <td class="leftTd"><label><span>叠加包销售品编码:</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" placeholder="多个用逗号隔开"  v-model="addGoods.tieJiaBaoNumber" />
				 </td>
				 <td class="leftTd"><label><span>ITV销售品编码:</span></label></td>
				 <td>
					<input type="text" value=""  id="minPrice" placeholder=""  v-model="addGoods.itvNumber" />
				 </td>
			</tr>
			
			
			<tr>
			    <td class="leftTd"><label><span>100元预存:</span></label></td>
				  <td>
					   <input type="text" style="width:130px" value=""  id="minPrice" placeholder="预存编码" v-model="addGoods.yuCunNumber1"/> <input type="text" style="width:180px" value=""  id="minPrice" placeholder="预存说明" v-model="addGoods.yuCunMsg1"/>
				 </td>
			    <td class="leftTd"><label><span>200元预存:</span></label></td>
				 <td>
					   <input type="text" style="width:130px" value=""  id="minPrice" placeholder="预存编码"  v-model="addGoods.yuCunNumber2"/> <input type="text" style="width:180px" value=""  id="minPrice" placeholder="预存说明" v-model="addGoods.yuCunMsg2"/>
				 </td>
				
			</tr>
			
			<tr>   <td class="leftTd"><label><span>300元预存</span></label></td>
				   <td>
					   <input type="text" style="width:130px" value=""  id="minPrice" placeholder="预存编码" v-model="addGoods.yuCunNumber3"/> <input type="text" style="width:180px" value=""  id="minPrice" placeholder="预存说明" v-model="addGoods.yuCunMsg3"/>
				   </td>
			       <td class="leftTd"><label><span>X元预存</span></label></td>
				   <td>
					   <input type="text" style="width:130px" value=""  id="minPrice" placeholder="预存编码" v-model="addGoods.yuCunNumberX"/> <input type="text" style="width:180px" value=""  id="minPrice" placeholder="预存说明" v-model="addGoods.yuCunMsgX"/>
				   </td>
			</tr>
			
			
			<tr>
			      <td class="leftTd"><label><span>产品所属团队:</span></label></td>
				   <td>
					<input type="text" value=""  id="minPrice" />
					 <a  v-show="bianji1" class="btn btn-primary" @click="team_show()" style="float: left; margin-left: 4px; ">编辑</a>
					 <a  v-show="bianji2" class="btn btn-primary" @click="team_close()" style="float: left; margin-left: 4px;">确定</a>
					 <a  v-show="bianji2" class="btn btn-warning" @click="team_back()" style="float: left; margin-left: 4px;">取消</a>
				   </td>
				   <td class="leftTd"><label><span>可办理数量:</span></label></td>
				 <td>
					 <select class="form-control" style="height: 32px;" v-model="addGoods.transactionNum">
					    <option value='6' >不限制</option>
					    <option value='1' >1</option>
					    <option value='2' >2</option>
					    <option value='3' >3</option>
					    <option value='4' >4</option>
					    <option value='5' >5</option>
					</select>
				 </td>
			</tr>
			
      	</table>
      	
      	<div v-show="teamBelongShow">
      	<!--所属团队树-->
      	<table class="textTable">	
	        <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;</span>
	        <div class="tree_pag">
			<h5>产品对应团队</h5>		
			
			<div class="tree_content">	
			
		<!--左边树状图-->
			 <div class="left_tree" >				
					<div class="treebox scrollXY">
						<div class="tree">
							 <ul >
                                <li class="main" v-for="(teamTree , index) in teamTreeRight"  @dblclick="addCity(teamTree.cityName,index)" v-show="teamTree.team.length!=0"> <a>{{teamTree.cityName}}</a>
                                  <ul>
                                     <li v-for="(itme , indexs ) in teamTree.team"><a @click="addTeam(index,indexs,itme.teamCode,itme.teamName)">{{itme.teamName}}</a></li>
                                  </ul>
                                 </li>
                              </ul>
						</div>
					</div>
		    </div> 
        
        <!--右边树状图-->
		<div class="right_tree">
					<div class="treebox scrollXY">
						<div class="tree">
							 <ul>
                                <li class="main" v-for="(teamTree , index) in teamTreeLeft" @dblclick="delCity(index)"  v-show="teamTree.team.length!=0"> <a>{{teamTree.cityName}}</a>
                                  <ul>
                                     <li v-for="(itme , indexs) in teamTree.team"><a @click="delTeam(index,indexs)">{{itme.teamName}}</a></li>
                                  </ul>
                                 </li>
                              </ul>
						</div>
					</div>
		</div> 
	</div>

	</div>
	    </table>
	    
	    </div>
			<!-- 产品宣传 -->
	    <table class="textTable">	
	         <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;产品宣传</span>
	         <tr>
			    <td class="leftTd" style="height:150px;"><label><span>商品头图:</span></label></td>
				 <td>
					<a class="fileUp" style="width: 100px; height: 100px;">
					</a>
				 </td>
			</tr>
			<tr>
			    <td class="leftTd" style="height:150px;"><label><span>商品详细图:</span></label></td>
				 <td>
					<a class="fileUp" style="width: 100px; height: 100px;">
					</a>
				 </td>
			</tr>
			
			<tr>
			    <td class="leftTd" style="height:150px;"><label><span>商品描述:</span></label></td>
				 <td>
					 <textarea style="height:150px;" id="goodsdesc"  v-model="addGoods.goodsdesc"></textarea>
					  </a>
				 </td>
			</tr>
			
			<tr>
			    <td class="leftTd" style="height:150px;"><label><span>备注:</span></label></td>
				 <td>
					<textarea style="height:150px;" id="goodsdesc" v-model="addGoods.remark" ></textarea>
					</a>
				 </td>
			</tr>
			
			<tr>
			     <td class="leftTd"><label><span>排序:</span></label></td>
			     <td>
					<input type="text" value=""  id="minPrice" placeholder="数字越大排序越靠前" v-model="addGoods.orderNumber" />
				 </td>
			</tr>
			
			
	    </table>
	    
	    
			<div class="form-group">
				<div class="col-sm-2 control-label"></div>
				<input type="button" class="btn btn-primary" v-show="sureShow" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回" style="margin-left: 101%;padding:3% 13%;"/>
			</div> 
		</form>
	</div>
</div>

<script src="../../js/modules/hnsd/hnsdgoods.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>