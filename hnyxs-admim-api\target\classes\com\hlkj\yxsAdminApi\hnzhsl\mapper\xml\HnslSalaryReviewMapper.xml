<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslSalaryReviewMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_salary_review a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.teamCode != null">
                AND a.TEAM_CODE LIKE CONCAT('%', #{param.teamCode}, '%')
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.auditDate != null">
                AND a.AUDIT_DATE LIKE CONCAT('%', #{param.auditDate}, '%')
            </if>
            <if test="param.auditStatus != null">
                AND a.AUDIT_STATUS = #{param.auditStatus}
            </if>
            <if test="param.auditName != null">
                AND a.AUDIT_NAME LIKE CONCAT('%', #{param.auditName}, '%')
            </if>
            <if test="param.auditSalaryGrant != null">
                AND a.AUDIT_SALARY_GRANT = #{param.auditSalaryGrant}
            </if>
            <if test="param.money1 != null">
                AND a.MONEY1 LIKE CONCAT('%', #{param.money1}, '%')
            </if>
            <if test="param.money2 != null">
                AND a.MONEY2 LIKE CONCAT('%', #{param.money2}, '%')
            </if>
            <if test="param.money3 != null">
                AND a.MONEY3 LIKE CONCAT('%', #{param.money3}, '%')
            </if>
            <if test="param.money4 != null">
                AND a.MONEY4 LIKE CONCAT('%', #{param.money4}, '%')
            </if>
            <if test="param.money5 != null">
                AND a.MONEY5 LIKE CONCAT('%', #{param.money5}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.schoolName != null">
                AND a.SCHOOL_NAME LIKE CONCAT('%', #{param.schoolName}, '%')
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.userName != null">
                AND a.USER_NAME LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.userLevel != null">
                AND a.USER_LEVEL = #{param.userLevel}
            </if>
            <if test="param.teamLevel != null">
                AND a.TEAM_LEVEL = #{param.teamLevel}
            </if>
            <if test="param.clientName != null">
                AND a.CLIENT_NAME LIKE CONCAT('%', #{param.clientName}, '%')
            </if>
            <if test="param.userCard != null">
                AND a.USER_CARD LIKE CONCAT('%', #{param.userCard}, '%')
            </if>
            <if test="param.userDate != null">
                AND a.USER_DATE LIKE CONCAT('%', #{param.userDate}, '%')
            </if>
            <if test="param.userServiceTime != null">
                AND a.USER_SERVICE_TIME = #{param.userServiceTime}
            </if>
            <if test="param.statusSuperior != null">
                AND a.STATUS_SUPERIOR LIKE CONCAT('%', #{param.statusSuperior}, '%')
            </if>
            <if test="param.businessNumber1 != null">
                AND a.BUSINESS_NUMBER1 = #{param.businessNumber1}
            </if>
            <if test="param.businessIntegration1 != null">
                AND a.BUSINESS_INTEGRATION1 = #{param.businessIntegration1}
            </if>
            <if test="param.businessNumber2 != null">
                AND a.BUSINESS_NUMBER2 = #{param.businessNumber2}
            </if>
            <if test="param.businessIntegration2 != null">
                AND a.BUSINESS_INTEGRATION2 = #{param.businessIntegration2}
            </if>
            <if test="param.businessNumber3 != null">
                AND a.BUSINESS_NUMBER3 = #{param.businessNumber3}
            </if>
            <if test="param.businessIntegration3 != null">
                AND a.BUSINESS_INTEGRATION3 = #{param.businessIntegration3}
            </if>
            <if test="param.businessNumber4 != null">
                AND a.BUSINESS_NUMBER4 = #{param.businessNumber4}
            </if>
            <if test="param.businessIntegration4 != null">
                AND a.BUSINESS_INTEGRATION4 = #{param.businessIntegration4}
            </if>
            <if test="param.businessNumber5 != null">
                AND a.BUSINESS_NUMBER5 = #{param.businessNumber5}
            </if>
            <if test="param.businessIntegration5 != null">
                AND a.BUSINESS_INTEGRATION5 = #{param.businessIntegration5}
            </if>
            <if test="param.userIntegration != null">
                AND a.USER_INTEGRATION = #{param.userIntegration}
            </if>
            <if test="param.teamNumber != null">
                AND a.TEAM_NUMBER = #{param.teamNumber}
            </if>
            <if test="param.teamIntegration != null">
                AND a.TEAM_INTEGRATION = #{param.teamIntegration}
            </if>
            <if test="param.userKpi != null">
                AND a.USER_KPI LIKE CONCAT('%', #{param.userKpi}, '%')
            </if>
            <if test="param.userBank != null">
                AND a.USER_BANK LIKE CONCAT('%', #{param.userBank}, '%')
            </if>
            <if test="param.teamName != null">
                AND a.TEAM_NAME LIKE CONCAT('%', #{param.teamName}, '%')
            </if>
            <if test="param.agentPoint != null">
                AND a.AGENT_POINT = #{param.agentPoint}
            </if>
            <if test="param.userYearIntegration != null">
                AND a.USER_YEAR_INTEGRATION LIKE CONCAT('%', #{param.userYearIntegration}, '%')
            </if>
            <if test="param.userTotalIntegration != null">
                AND a.USER_TOTAL_INTEGRATION LIKE CONCAT('%', #{param.userTotalIntegration}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSalaryReview">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSalaryReview">
        <include refid="selectSql"></include>
    </select>

    <!--  (@i:=@i+1) as rows,  ,(Select @i:=0) b  -->
    <sql id="selectHashSql">
        SELECT  #{param.queryTime} as dates,u.CITY_CODE,u.SCHOOL_NAME,u.USER_NAME,u.USER_SFZ,u.SALES_CODE,u.USER_PHONE ,u.CREATED_DATE,IFNULL(u.jf,0) as jf,ub.BANK_NUMBER,ub.BANK_NAME,o.dq,o.dqSize,IFNULL(o.xz,0) as xz, IFNULL(o.xzf,0) as xzf,IFNULL(o.wx,0) as wx,IFNULL(o.wxf,0) as wxf ,p.LEVELS ,tt.TEAM_NAME,u.TEAM_IDENTITY,u.TEAM_IDENTITY_LEVEL,IFNULL(u.teamjf,0) as teamjf,u.teanCount,IFNULL(u.teamglf,0) as teamglf,
        IFNULL(o.yxxz,0) as yxxz, IFNULL(o.yxxzf,0) as yxxzf,IFNULL(o.yxwx,0) as yxwx,IFNULL(o.yxwxf,0) as yxwxf,IFNULL(u.yxteamjf,0) as yxteamjf,tt.TEAM_TYPE,st.approvalStatus
        ,u.TEAM_CODE,u.SCHOOL_CODE
        FROM (
        SELECT u.*,tm.TEAM_CODE,tm.TEAM_IDENTITY,tm.TEAM_IDENTITY_LEVEL,tm1.teamjf,tm1.yxteamjf,tm1.teanCount,tm1.teamglf,i.jf from
        (select u.INTEGRAL,u.CITY_CODE,u.USER_NAME,u.USER_SFZ,u.SALES_CODE,u.USER_PHONE ,u.CREATED_DATE,s.SCHOOL_NAME,s.SCHOOL_CODE from
        (select u.INTEGRAL,u.CITY_CODE,u.USER_NAME,u.USER_SFZ,u.SALES_CODE,u.USER_PHONE ,u.CREATED_DATE from hnsl_user u where u.STATUS_SF in (2,3,4) and u.`STATUS`=1
        <if test="param.cityCode !=null and param.cityCode !=''">
            AND u.city_code=#{param.cityCode}
        </if>
        <if test="param.userPhone !=null and param.userPhone !=''">
            AND u.user_phone=#{param.userPhone}
        </if>
        <if test="param.userName !=null and param.userName !=''">
            AND u.user_name like concat(concat('%',#{param.userName}),'%')
        </if>
        ) u
        left join hnsl_user_school us on u.USER_PHONE=us.USER_PHONE
        left join hnsl_school s on us.SCHOOL_CODE=s.SCHOOL_CODE
        <where>
            <if test="param.schoolList !=null and param.schoolList.size() >0">
                AND s.school_code in
                <foreach collection="param.schoolList" item="schoolCode" open="(" separator="," close=")">
                    #{schoolCode}
                </foreach>
            </if>
            <if test="param.schoolName !=null and param.schoolName !=''">
                AND s.school_name like concat(concat('%',#{param.schoolName}),'%')
            </if>
        </where>
        ) u
        left join hnsl_team_member tm on u.USER_PHONE=tm.USER_CODE
        left join (
        select tm.TEAM_CODE,SUM(IFNULL(i.jf,0)) as teamjf,SUM(IFNULL(i.yxjf,0)) as yxteamjf,count(*) as teanCount,sum(IFNULL(i.jfz,0)*0.7) as teamglf,SUM(IFNULL(i.jfz,0)) as teamjfz
        from (SELECT TEAM_CODE,USER_CODE from hnsl_team_member where `STATUS`=1 and TEAM_IDENTITY_LEVEL=1) tm left join
        (SELECT ot2.user_id,sum(case when wl.sp=1 then ot2.integral_operation else 0 end) as yxjf,sum(case when wl.sp is null then ot2.integral_operation else 0 end) as jf,sum(ot2.integral_operation) as jfz from
        (SELECT user_id,order_id,integral_operation from Hnsl_Integral where created_date BETWEEN #{param.startTime} and #{param.endTime}) ot2 left join
        (select CUSTOMER_PHONE,order_id from HNSL_ORDER where  ORDER_STATUS in (3,23,15) and created_date BETWEEN #{param.startTime} and #{param.endTime}) ot
        on ot2.order_id=ot.order_id
        left join
        (SELECT 1 as sp,USER_PHONE from hnsl_white_list where SPECIFY_POINTS>0  )  wl on ot.CUSTOMER_PHONE=wl.USER_PHONE
        group by ot2.user_id) i
        on tm.USER_CODE=i.USER_ID GROUP BY tm.TEAM_CODE
        ) tm1 on tm.TEAM_CODE=tm1.TEAM_CODE
        left join (select t.user_id,sum(t.integral_operation) as jf from hnsl_integral t where t.created_date BETWEEN #{param.startTime} and #{param.endTime} group by t.user_id) i on u.USER_PHONE=i.USER_ID
        <where>
            <if test="param.teamLevel !=null and param.teamLevel !=0">

                <choose>
                    <when test="param.teamLevel==1">
                        AND tm.team_identity=5
                    </when>
                    <when test="param.teamLevel==2">
                        AND tm.team_identity=4
                    </when>
                    <when test="param.teamLevel==3">
                        AND tm.team_identity not in (4,5)
                    </when>
                    <otherwise>

                    </otherwise>
                </choose>
            </if>
            <if test="param.userJF1 !=null and param.userJF1 !=''">
                AND i.jf&gt;=#{param.userJF1} AND i.jf &lt;=#{param.userJF2}
            </if>
            <if test="param.teamJF1 !=null and param.teamJF1 !=''">
                AND tm1.teamjfz&gt;=#{param.teamJF1} AND tm1.teamjfz &lt;=#{param.teamJF2}
            </if>
            <if test="param.submitStatus==1">
                AND i.jf&gt;=50
            </if>
            <if test="param.submitStatus==2">
                AND i.jf&lt;50
            </if>
        </where>
        ) u
        left join (SELECT USER_ID,BANK_NUMBER,BANK_NAME FROM hnsl_user_bankcard where `STATUS`=1) ub on u.USER_PHONE=ub.USER_ID
        left join (select COUNT(*) as dq,HHID,
        sum(case when ot.safl_type in(1,2,3,5,6,7,8,9) and wl.sp is null then 1 else 0 end) as xz, sum(case when ot.safl_type=4 and wl.sp is null then 1 else 0 end) as wx,sum(case when ot.safl_type in(1,2,3,5,6,7,8,9) and wl.sp is null then ot2.integral_operation else 0 end) as xzf,sum(case when ot.safl_type=4 and wl.sp is null then ot2.integral_operation else 0 end) as wxf,
        sum(case when ot.safl_type in(1,2,3,5,6,7,8,9) and wl.sp=1 then 1 else 0 end) as yxxz, sum(case when ot.safl_type=4 and wl.sp=1 then 1 else 0 end) as yxwx,sum(case when ot.safl_type in(1,2,3,5,6,7,8,9) and wl.sp=1 then ot2.integral_operation else 0 end) as yxxzf,sum(case when ot.safl_type=4 and wl.sp=1 then ot2.integral_operation else 0 end) as yxwxf
        , sum(ifnull(ot2.integral_operation,0)) as dqSize from (select order_id,safl_type,ORDER_STATUS,CUSTOMER_PHONE,HHID from HNSL_ORDER where  ORDER_STATUS in (3,23,15) and created_date BETWEEN #{param.startTime} and #{param.endTime}) ot left join
        (SELECT order_id,integral_operation from Hnsl_Integral where created_date BETWEEN #{param.startTime} and #{param.endTime}) ot2 on ot.order_id=ot2.order_id left join
        (SELECT 1 as sp,USER_PHONE from hnsl_white_list where SPECIFY_POINTS>0  )  wl on ot.CUSTOMER_PHONE=wl.USER_PHONE  group by ot.HHID) o on u.USER_PHONE=o.HHID

        left join (SELECT t.USER_ID,t.LEVELS from hnsl_performance t where date_format(t.created_date,'%Y-%m')=#{param.queryTime}) p on u.USER_PHONE=p.USER_ID
        left join hnsl_team tt on u.TEAM_CODE=tt.TEAM_CODE
        left join (SELECT USER_PHONE,SCHOOL_NAME,TEAM_NAME,1 as approvalStatus from hnsl_student_approval where AUDIT_DATE=#{param.queryTime} and `STATUS`=1) st on u.USER_PHONE=st.USER_PHONE  and u.SCHOOL_NAME=st.SCHOOL_NAME  and tt.TEAM_NAME=st.TEAM_NAME

        <where>
            <if test="param.teamName !=null and param.teamName !=''">
                AND tt.TEAM_NAME=#{param.teamName}
            </if>
        </where>
    </sql>

    <select id="queryReviewPageRel" resultType="java.util.HashMap">
        <include refid="selectHashSql"></include>
    </select>

    <select id="queryReviewListRel" resultType="java.util.HashMap">
        <include refid="selectHashSql"></include>
    </select>

    <select id="calculateManagementFee" resultType="int">
        select sum(tt.teamglf) from (select tm.TEAM_CODE,sum(case tm.TEAM_IDENTITY_LEVEL when 1 then  IFNULL(i.jfz,0)*0.7 else 0 end) as teamglf from (SELECT TEAM_CODE,USER_CODE,TEAM_IDENTITY_LEVEL from hnsl_team_member where `STATUS`=1) tm left join (SELECT ot2.user_id,sum(case when wl.sp=1 then ot2.integral_operation else 0 end) as yxjf,sum(case when wl.sp is null then ot2.integral_operation else 0 end) as jf,sum(ot2.integral_operation) as jfz from (SELECT user_id,order_id,integral_operation from Hnsl_Integral where created_date BETWEEN  #{startTime} and #{endTime}) ot2 left join (select CUSTOMER_PHONE,order_id from HNSL_ORDER where ORDER_STATUS in (3,23,15) and created_date BETWEEN #{startTime} and #{endTime}) ot on ot2.order_id=ot.order_id left join (SELECT 1 as sp,USER_PHONE from hnsl_white_list where SPECIFY_POINTS>0 ) wl on ot.CUSTOMER_PHONE=wl.USER_PHONE group by ot2.user_id) i on tm.USER_CODE=i.USER_ID GROUP BY tm.TEAM_CODE) tt left join
                                    (select DISTINCT tm.TEAM_CODE,tm.USER_CODE from hnsl_team_member tm where tm.TEAM_IDENTITY_LEVEL=2 and tm.`STATUS`=1) tm1 on tt.TEAM_CODE=tm1.TEAM_CODE
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       left join hnsl_user u on tm1.USER_CODE=u.USER_PHONE
        where u.USER_PHONE in (
            select USER_PHONE from hnsl_user where STATUS_SUPERIOR=#{phone} and STATUS_SF=3
            UNION ALL
            select USER_PHONE from hnsl_user where USER_PHONE=#{phone}
        )
    </select>
</mapper>
