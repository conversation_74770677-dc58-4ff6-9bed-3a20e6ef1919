<!DOCTYPE html>
<html>
<head>
<title>文件上传</title>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<link href="../../plugins/bootstrap-fileinput/css/fileinput.min.css" rel="stylesheet" />
	 
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<script src="../../libs/ajaxupload.js"></script>

<script src="../../js/form.js"></script>

<script src="../../js/common.js"></script>


<style type="text/css">
.list-group-item {
        cursor: pointer;
        height: 42px;
        text-align: center;
    }

        .list-group-item:last-child {
            margin-bottom: -1px;
            border-bottom-right-radius: 0px;
            border-bottom-left-radius: 0px;
        }
    .list-group-item-selected {
            color: #31708f!important;
            background-color: #d9edf7!important;
    }
    .bn {
        border: none;
        -ms-box-shadow: none;
        -webkit-box-shadow: none;
        box-shadow: none;
        cursor: default;
    }

    .notes {
        font-size: 3px;
        color: #aaa;
        padding-left: 15px;
    }

    .clearFill {
        margin: 0;
        padding: 0;
    }

    .container {
        -moz-min-width: 1100px !important;
        -ms-min-width: 1100px !important;
        -o-min-width: 1100px !important;
        -webkit-min-width: 1100px !important;
        min-width: 1100px !important;
    }
</style>
<style  type="text/css">
body{
	margin:0;
	font-family:'微软雅黑','Times New Roman', Times, serif;
	}
.navi_head{
	height:50px;
	background-color:#459df5;
}
.navi_body{
	overflow:hidden;
	height:50px;
	background:rgba(36,97,158,0.9);
	transition:height ease 0.5s;
}
.navi_body:hover{
	height:300px;
}

.navi_head>div>span{
	width:150px;
	text-align:center;
	height:300px;
	display:inline-block;
	font-weight:bold;
	color:#FFF;
	font-size:14px;
	vertical-align:top;
}

.navi_head>div>span>p a{
	color:#FFF;
	text-decoration:none;
}
.navi_head>div>span>p a:hover{
	color:#FFF;
	text-decoration:underline;
}

.navi_title{
	font-size:16px;
	line-height:50px;
	margin-top:0;
}

.navi_head>div>span:hover{
	background:rgba(100,100,100,0.2);
}
</style>
</head>
<body>
<div id="rrapp" v-cloak >
<div v-show="showList">
<div class="navi_body">
			<div class="navi_head">
				<div style="width:80%; margin-left:auto; margin-right:auto;">
					<span>
						<p class="navi_title">菜单1</p>
						<p><a href="">菜单1.1</a></p>
						<p><a href="">菜单1.2</a></p>
						<p><a href="">菜单1.3</a></p>
						<p><a href="">菜单1.4</a></p>
						<p><a href="">菜单1.5</a></p>
					</span>
					<span>
						<p class="navi_title">菜单2</p>
						<p><a href="">菜单2.1</a></p>
						<p><a href="">菜单2.2</a></p>
						<p><a href="">菜单2.3</a></p>
						<p><a href="">菜单2.4</a></p>
						<p><a href="">菜单2.5</a></p>
					</span>
					<span>
						<p class="navi_title">菜单3</p>
						<p><a href="">菜单3.1</a></p>
						<p><a href="">菜单3.2</a></p>
						<p><a href="">菜单3.3</a></p>
						<p><a href="">菜单3.4</a></p>
						<p><a href="">菜单3.5</a></p>
					</span>
				</div>
			</div>
		</div>
<!-- <div class="row"> -->
<!-- <ol class="breadcrumb"> -->
<!--     <li class="active">微信工具</li> -->
<!--     <li class="active">微信自定义菜单编辑工具</li> -->
<!-- </ol> -->
<div class="panel panel-default" id="divMain">
    <div class="panel-heading" style="min-height: 55px;">
        <div class="container-fluid" style="margin-bottom: -15px;">
          		 微信自定义菜单编辑工具
        </div>
    </div>

    <div class="panel-body" data-bind="with:Menus" id="divMenu" style="">
        <div style="height: 200px;"  >
            <div class="list-group col-xs-4 clearFill bn" id="menus1">
                <div class="list-group-item bn" :id="menus1.menus1-1.key" >{{menus1.menus1-1.name}}</div>
                <div class="list-group-item bn" :id="menus1.menus1-2.key" >{{menus1.menus1-2.name}}</div>
                <div class="list-group-item bn" :id="menus1.menus1-3.key" >{{menus1.menus1-3.name}}</div>
                <div class="list-group-item bn" :id="menus1.menus1-4.key" >{{menus1.menus1-4.name}}</div>
                <div class="list-group-item bn" :id="menus1.menus1-5.key" >{{menus1.menus1-5.name}}</div>
            </div>
        
            <div class="list-group col-xs-4 clearFill bn" id="menus2">
                <div class="list-group-item bn" :id="menus2.menus2-1.key" >{{menus2.menus2-1.name}}</div>
                <div class="list-group-item bn" id="menus2.menus2-2.key" >{{menus2.menus2-2.name}}</div>
                <div class="list-group-item bn" id="menus2.menus2-3.key" >{{menus2.menus2-3.name}}</div>
                <div class="list-group-item bn" id="menus2.menus2-4.key">{{menus2.menus2-4.name}}</div>
                <div class="list-group-item bn" id="menus2.menus2-5.key">{{menus2.menus2-5.name}}</div>
            </div>
        
            <div class="list-group col-xs-4 clearFill bn" id="menus3">
                <div class="list-group-item bn" id="menus3.menus3-1.key" >{{menus3.menus3-1.name}}</div>
                <div class="list-group-item bn" id="menus3.menus3-2.key" >{{menus3.menus3-2.name}}</div>
                <div class="list-group-item bn" id="menus3.menus3-3.key" >{{menus3.menus3-3.name}}</div>
                <div class="list-group-item bn" id="menus3.menus3-4.key" >{{menus3.menus3-4.name}}</div>
                <div class="list-group-item bn" id="menus3.menus3-5.key" >{{menus3.menus3-5.name}}</div>
            </div>
        </div>
        <div class=" col-xs-4 list-group-item "  @click="addMenu()">
		            <i class="fa fa-plus"></i>
		    </div>
        <div class="col-xs-4 list-group-item list-group-item-danger"   @click="addMenu()"  > </div>
        <div class="col-xs-4 list-group-item list-group-item-danger"    @click="addMenu()"   > </div>
        <div class="col-xs-4 list-group-item list-group-item-danger"   @click="addMenu()"   > </div>
<!--          <div class="col-xs-4 list-group-item" v-modul="menu2Index" @click="addmenu('2')"> -->
<!--             <i class="fa fa-plus"></i> -->
<!--         </div> -->
<!--          <div class="col-xs-4 list-group-item" v-modul="menu3Index" @click="addmenu('3')"> -->
<!--             <i class="fa fa-plus"></i> -->
<!--         </div> -->
        <div class="clearfix"></div>

        <div class="col-xs-12" style="border: 1px solid rgb(238, 238, 238); padding-top: 15px; margin-top: 15px;" data-bind="with:$root.Menu,visible:($root.Menu()!=undefined)">
            <form id="MenuForm" onsubmit="return false;" novalidate="novalidate">
                <div class="form-group col-xs-4">
                    <input type="text" class="form-control" name="name" data-placement="top" data-toggle="popover" placeholder="请输入名称" data-bind="value:name,event:{'keyup':$root.EventNameErrorMessage},attr:{'data-content':$root.NameErrorMessage}">
                </div>
                <div class="form-group col-xs-4">
                    <select class="form-control" onchange="$('#txtMenuButtonValue').attr('placeholder', $(this).find('option:selected').attr('pl'))" data-bind="value:type">
                        <option value="view" pl="请输入Url">跳转URL</option>
                        <option value="click" pl="请输入Key">点击推事件</option>
                        <option value="scancode_push" pl="请输入Key">扫码推事件</option>
                        <option value="scancode_waitmsg" pl="请输入Key">扫码推事件且弹出“消息接收中”提示框</option>
                        <option value="pic_sysphoto" pl="请输入Key">弹出系统拍照发图</option>
                        <option value="pic_photo_or_album" pl="请输入Key">弹出拍照或者相册发图</option>
                        <option value="pic_weixin" pl="请输入Key"> 弹出微信相册发图器</option>
                        <option value="location_select" pl="请输入Key">弹出地理位置选择器</option>
                    </select>
                </div>
                <div class="form-group col-xs-8">
                    <input type="text" id="txtMenuButtonValue" name="value" class="form-control" placeholder="请输入Url" data-placement="top" data-toggle="popover" data-bind="value:value,event:{'keyup':$root.EventValueErrorMessage},attr:{'data-content':$root.ValueErrorMessage}">
                </div>
                <div class="form-group col-xs-12">
                    <button type="submit" class="btn btn-primary" data-bind="click:$root.MenuSave">确定</button>
                    <button type="submit" class="btn btn-danger" data-bind="visible:$root.isEditMenu,click:$root.DeleteMenu" style="display: none;">删除</button>
                    <button type="button" class="btn btn-default" title="上移" data-bind="visible:$root.isEditMenu(),disable:!$root.IsUp(),click:$root.Up" disabled="" style="display: none;"><i class="fa fa-chevron-circle-up" aria-hidden="true"></i></button>
                    <button type="button" class="btn btn-default" title="下移" data-bind="visible:$root.isEditMenu(),disable:!$root.IsDown(),click:$root.Down" disabled="" style="display: none;"><i class="fa fa-chevron-circle-down" aria-hidden="true"></i></button>
                    <button type="button" class="btn btn-default" title="左移" data-bind="visible:$root.isEditMenu(),disable:!$root.IsLeft(),click:$root.Left" disabled="" style="display: none;"><i class="fa fa-chevron-circle-left" aria-hidden="true"></i></button>
                    <button type="button" class="btn btn-default" title="右移" data-bind="visible:$root.isEditMenu(),disable:!$root.IsRight(),click:$root.Right" disabled="" style="display: none;"><i class="fa fa-chevron-circle-right" aria-hidden="true"></i></button>
                    <button type="button" class="btn btn-default" title="复制菜单" data-bind="visible:$root.isEditMenu(),click:$root.Copy" style="display: none;">复制</button>
                    <button type="button" class="btn btn-default" title="粘贴菜单" data-bind="click:$root.Paste">粘贴</button>
                    <button type="submit" class="btn btn-default" data-bind="click:$root.CancelMenuSave">关闭</button>
                </div>
            </form>
        </div>
        <div class="clearfix"></div>
    </div>
    <div class="panel-footer" data-bind="with:Menus">
        <button id="btnSubmitMenu" type="button" class="btn btn-primary" data-bind="click:$root.SaveMenus" data-toggle="tooltip" data-placement="top" title="" data-original-title="发布到微信"><i class="fa fa-upload" aria-hidden="true"></i> 发布</button>
        <button id="btnQueryMenu" type="button" class="btn btn-default" data-bind="click:function (){$root.EditMenus(true)}" data-toggle="tooltip" data-placement="top" title="" data-original-title="查询微信现有菜单"><i class="fa fa-download" aria-hidden="true"></i> 查询菜单</button>
    </div>
</div>
<div class="panel panel-default">
    <div class="panel-heading">实时JSON</div>
    <div class="panel-body">
        <pre id="jsonShow" style="padding:0;border:none;background-color:#fff;" data-bind="html:JSON.stringify($root.NewMenu(),null,4)">{
    "button": []
}</pre>
    </div>
<!-- </div> -->

 


        </div>





<!-- <form id="submitForm" > -->
		
<!-- 	<div class="form-group"> -->
<!--         <label class="col-sm-2 control-label" for="cover">图片：</label> -->
<!-- 		<div class="col-sm-8"> -->
<!-- 			<input type="text" id="cover" name="cover" class="form-control" readOnly style="margin-bottom: 5px;" value=""/> -->
<!-- 			<input id="uploadCover" type="file" class="file"   class="file-loading"> -->
<!-- 		</div> -->
<!-- 	</div> -->
   
<!-- 	<div class="form-group" style="padding-left: unset; padding-right: unset;"> -->
<!--         <label class="col-sm-2 control-label" for="content"><font color="red">*</font>内容：</label> -->
<!-- 	    <div class="col-sm-8"> -->
<!-- 	        <script id="content" name="content" type="text/plain"></script> -->
<!-- 	    </div> -->
<!-- 	</div> -->
<!-- </form> -->
</div>
<!-- 	<div v-show="showList"> -->
<!-- 		<div class="grid-btn"> -->
<!-- 			<a class="btn btn-danger" @click="addConfig"><i class="fa fa-sun-o"></i>&nbsp;云存储配置</a> -->
<!-- 			<a class="btn btn-primary" id="upload"><i class="fa fa-plus"></i>&nbsp;上传文件</a> -->
<!-- 			<a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a> -->
<!-- 		</div> -->
<!-- 	    <table id="jqGrid"></table> -->
<!-- 	    <div id="jqGridPager"></div> -->
<!--     </div> -->
    
<!--     <div v-show="!showList" class="panel panel-default"> -->
<!-- 		<div class="panel-heading">{{title}}</div> -->
<!-- 		<form class="form-horizontal"> -->
<!-- 			<div class="form-group"> -->
<!-- 				<div class="col-sm-2 control-label">存储类型</div> -->
<!-- 				<label class="radio-inline"> -->
<!-- 					<input type="radio" name="type" v-model="config.type" value="1"/> 七牛 -->
<!-- 				</label> -->
<!-- 				<label class="radio-inline"> -->
<!-- 					<input type="radio" name="type" v-model="config.type" value="2"/> 阿里云 -->
<!-- 				</label> -->
<!-- 				<label class="radio-inline"> -->
<!-- 					<input type="radio" name="type" v-model="config.type" value="3"/> 腾讯云 -->
<!-- 				</label> -->
<!-- 			</div> -->
<!-- 			<div v-show="config.type == 1"> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">&nbsp;</div> -->
<!-- 					<p class="form-control-static"><a href="http://www.renren.io/open/qiniu.html" target="_blank">免费申请(七牛)10GB储存空间</a></p> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">域名</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 					  <input type="text" class="form-control" v-model="config.qiniuDomain" placeholder="七牛绑定的域名"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">路径前缀</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qiniuPrefix" placeholder="不设置默认为空"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">AccessKey</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qiniuAccessKey" placeholder="七牛AccessKey"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">SecretKey</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qiniuSecretKey" placeholder="七牛SecretKey"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">空间名</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qiniuBucketName" placeholder="七牛存储空间名"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 			</div> -->
<!-- 			<div v-show="config.type == 2"> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">域名</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.aliyunDomain" placeholder="阿里云绑定的域名"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">路径前缀</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.aliyunPrefix" placeholder="不设置默认为空"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">EndPoint</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.aliyunEndPoint" placeholder="阿里云EndPoint"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">AccessKeyId</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.aliyunAccessKeyId" placeholder="阿里云AccessKeyId"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">AccessKeySecret</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.aliyunAccessKeySecret" placeholder="阿里云AccessKeySecret"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">BucketName</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.aliyunBucketName" placeholder="阿里云BucketName"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 			</div> -->
<!-- 			<div v-show="config.type == 3"> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">域名</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qcloudDomain" placeholder="腾讯云绑定的域名"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">路径前缀</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qcloudPrefix" placeholder="不设置默认为空"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">AppId</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qcloudAppId" placeholder="腾讯云AppId"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">SecretId</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qcloudSecretId" placeholder="腾讯云SecretId"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">SecretKey</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qcloudSecretKey" placeholder="腾讯云SecretKey"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">BucketName</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qcloudBucketName" placeholder="腾讯云BucketName"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="form-group"> -->
<!-- 					<div class="col-sm-2 control-label">Bucket所属地区</div> -->
<!-- 					<div class="col-sm-10"> -->
<!-- 						<input type="text" class="form-control" v-model="config.qcloudRegion" placeholder="如：sh（可选值 ，华南：gz 华北：tj 华东：sh）"/> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 			</div> -->
<!-- 			<div class="form-group"> -->
<!-- 				<div class="col-sm-2 control-label"></div>  -->
<!-- 				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/> -->
<!-- 				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/> -->
<!-- 			</div> -->
<!-- 		</form> -->
<!-- 	</div> -->

</div>

<script src="../../plugins/ueditor/ueditor.config.js" type="text/javascript"></script>
<script src="../../plugins/ueditor/ueditor.all.js" type="text/javascript"> </script>
<script src="../../plugins/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript"></script>

<script src="../../plugins/bootstrap-fileinput/js/fileinput.min.js"></script>
<script src="../../plugins/bootstrap-fileinput/js/fileinput_locale_zh.js"></script>

<!-- <script src="../../js/component/VueImgInputer.js"></script> -->
<script src="../../js/modules/oss/oss.js"></script>

<script type="text/javascript">
// Vue.component('VueImgInputer', VueImgInputer);

// uploadImg("uploadCover", "cover", "upload/uploadImg", "1");
// $(function () {
	//初始化富文本编辑器
// 	initUeditor();
 
// 上传图片

// 	submit = function(){
// 		frmValidate();
// 		var data = $("#submitForm").serialize();
// 		$.ajax({
//             url: _urlPath + "admin/tech/addArticle",
//             dataType: "json",
//             type: "post",
//             data: data,
//             success: function (req){
//                 if (req.retcode == 1) {
//                 	goPage("admin/tech/articlePage")
//                 } else {
//                 	$("#errDiv").show();
//     				$("#err").html(req.retmsg);
//                 }
//             },
//             error: function(req){
//             	$("#errDiv").show();
// 				$("#err").html(req.statusText);
//             }
//         });
// 	}
// });
</script>
</body>
</html>