<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5CustomerPositionStaffRelMapper">

    <!-- 批量插入定位工号关联关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO hnzsxh5_customer_position_staff_rel
        (POSITION_ID, STAFF_CODE, CITY, CITY_CODE, CREATED_DATE)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.positionId}, #{item.staffCode}, #{item.city}, #{item.cityCode}, NOW())
        </foreach>
    </insert>

    <!-- 根据定位ID删除关联关系 -->
    <delete id="deleteByPositionId" parameterType="java.lang.Integer">
        DELETE FROM hnzsxh5_customer_position_staff_rel WHERE POSITION_ID = #{positionId}
    </delete>

    <!-- 批量保存定位工号关联（带地市信息） -->
    <insert id="batchSave" parameterType="java.util.List">
        INSERT INTO hnzsxh5_customer_position_staff_rel
        (POSITION_ID, STAFF_CODE, CITY, CITY_CODE, CREATED_DATE)
        VALUES
        <foreach collection="relList" item="item" separator=",">
            (#{item.positionId}, #{item.staffCode}, #{item.city}, #{item.cityCode}, NOW())
        </foreach>
    </insert>
    
    <!-- 检查定位对工号是否可见 -->
    <select id="checkVisibility" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM hnzsxh5_customer_position_staff_rel 
        WHERE POSITION_ID = #{positionId} AND STAFF_CODE = #{staffCode}
    </select>
    

    
    <!-- 根据定位ID获取关联的用户列表 -->
    <select id="getStaffByPositionId" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5User">
        SELECT DISTINCT u.ID, u.STAFF_CODE, u.USER_NAME, u.CITY, u.CITYCODE, u.ORG_NAME, u.ORG_ID
        FROM hnzsxh5_user u
        INNER JOIN hnzsxh5_customer_position_staff_rel r ON r.STAFF_CODE = u.STAFF_CODE AND r.CITY_CODE = u.CITYCODE
        WHERE r.POSITION_ID = #{positionId}
        <if test="staffCodeAndCityCode != null and staffCodeAndCityCode.size() > 0">
            AND (
            <foreach collection="staffCodeAndCityCode" item="item" separator=" OR ">
                (u.STAFF_CODE = #{item.staffCode} AND u.CITYCODE = #{item.cityCode})
            </foreach>
            )
        </if>
    </select>

</mapper> 