<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslUserSwitchMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        select a.*,t1.user_name,t1.city_code,t2.school_name from hnsl_user_switch a left join hnsl_user t1 on a.user_phone=t1.user_phone
        left join (select t.user_phone,t1.school_name from hnsl_user_school t left join hnsl_school t1 on t.school_code=t1.school_code
        where t.status=1) t2 on a.user_phone=t2.user_phone
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.commitmentStatus != null">
                AND a.COMMITMENT_STATUS = #{param.commitmentStatus}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE &gt;=  #{param.createdDate}
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE &gt;=  #{param.updatedDate}
            </if>
            <if test="param.commitmentImage != null">
                AND a.COMMITMENT_IMAGE LIKE CONCAT('%', #{param.commitmentImage}, '%')
            </if>
            <if test="param.userName != null and param.userName.trim() != ''">
                and t1.user_name LIKE CONCAT('%',#{param.userName},'%')
            </if>
            <if test="param.schoolName != null and param.schoolName.trim() != ''">
                and t2.school_name LIKE CONCAT('%',#{param.schoolName},'%')
            </if>
            <if test="param.cityCode != null and param.cityCode.trim() != ''">
                and t1.city_code = #{param.cityCode}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserSwitch">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserSwitch">
        <include refid="selectSql"></include>
    </select>

</mapper>
