<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5OrderInfoOfferinstlistAttrlistMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsxh5_order_info_offerinstlist_attrlist a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.orderInfoOfferinstlistId != null">
                AND a.ORDER_INFO_OFFERINSTLIST_ID LIKE CONCAT('%', #{param.orderInfoOfferinstlistId}, '%')
            </if>
            <if test="param.attrShowType != null">
                AND a.ATTR_SHOW_TYPE LIKE CONCAT('%', #{param.attrShowType}, '%')
            </if>
            <if test="param.checkMessage != null">
                AND a.CHECK_MESSAGE LIKE CONCAT('%', #{param.checkMessage}, '%')
            </if>
            <if test="param.attrId != null">
                AND a.ATTR_ID LIKE CONCAT('%', #{param.attrId}, '%')
            </if>
            <if test="param.valuesList != null">
                AND a.VALUES_LIST LIKE CONCAT('%', #{param.valuesList}, '%')
            </if>
            <if test="param.attrValue != null">
                AND a.ATTR_VALUE LIKE CONCAT('%', #{param.attrValue}, '%')
            </if>
            <if test="param.attrName != null">
                AND a.ATTR_NAME LIKE CONCAT('%', #{param.attrName}, '%')
            </if>
            <if test="param.attrValueName != null">
                AND a.ATTR_VALUE_NAME LIKE CONCAT('%', #{param.attrValueName}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5OrderInfoOfferinstlistAttrlist">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5OrderInfoOfferinstlistAttrlist">
        <include refid="selectSql"></include>
    </select>

</mapper>
