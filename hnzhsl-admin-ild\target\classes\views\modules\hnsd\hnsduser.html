<!DOCTYPE html>
<html>
<head>
    <title>2</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
            name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet"
          href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet"
          href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <link rel="stylesheet" href="../../css/AdminLTE.min.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css"
          rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script
            src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script
            src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../libs/ajaxfileupload.js"></script>
    <script src="../../libs/jquery.form.js"></script>

    <script src="../../js/common.js"></script>

</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showList" id="isShowList">

        <div class="row">
            <div class="form-group col-md-2">
                <label>渠道</label> <select class="form-control"
                                          style="height: 32px;" v-model='seachUser.channel'>
                <option value=''>全部</option>
                <option v-for="itme in channel" v-bind:value="itme.channelId">
                    {{itme.channelName}}
                </option>
            </select>
            </div>
            <div class="form-group col-md-2">
                <label>角色</label> <select class="form-control"
                                          style="height: 32px;" v-model='seachUser.role'>
                <option value=''>全部</option>
                <option v-for="itme in role" v-bind:value="itme.id">
                    {{itme.roleName}}
                </option>
            </select>
            </div>

            <div class="form-group col-md-2">
                <label>状态:</label> <select class="form-control"
                                           style="height: 32px;" v-model='seachUser.status'>
                <option value=''>全部</option>
                <option v-for="itme in status" v-bind:value="itme.statusId">
                    {{itme.statusName}}
                </option>
            </select>
            </div>

            <div class="form-group col-md-2">
                <label>所属城市:</label> <select class="form-control"
                                             style="height: 32px;" v-model="seachUser.cityCode">
                <option value=''>全部</option>
                <option v-for="itme in city" v-bind:value="itme.cityCode">
                    {{itme.cityName}}
                </option>
            </select>
            </div>
            <div class="form-group col-md-2">
                <label>所属团队:</label> <select id="prov" name="prov"
                                             class="form-control" style="height: 32px;"
                                             v-model="seachUser.teamCode">
                <option v-for="item in listProv" v-bind:value="item.teamCode"
                        :key="item.teamCode">{{item.teamName}}
                </option>
            </select>
            </div>
            <div class="form-group col-md-2" style="height: 32px;">
                <label>日期:</label>
                <div class="input-group col-ms-2 ">
                    <input class="form-control pull-left dateRange date-picker "
                           id="dateTimeRange" @keyup.enter="query" value="" type="text"
                           placeholder="日期"> <span class="input-group-addon">
								<i class="fa fa-calendar bigger-110"></i>
							</span> <input name="beginTime" id="beginTime" type="hidden"> <input
                        name="endTime" id="endTime" type="hidden">
                </div>
            </div>
        </div>

        <div class="row2">
            <div class="form-group col-md-2" style="height: 40px">
                <input type="text" class="form-control" placeholder="销售员编码"
                       v-model='seachUser.userNumber'/>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <input type="text" class="form-control" placeholder="揽机工号"
                       v-model='seachUser.userId'/>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <input type="text" class="form-control" placeholder="手机号码"
                       v-model='seachUser.userPhone'/>
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <input type="text" class="form-control" placeholder="姓   名"
                       v-model='seachUser.userName'/>
            </div>
        </div>

        <div class="grid-btn">
            <a v-if="hasPermission('hnsduser:query')" class="btn btn-primary"
               @click="select">&nbsp;查询</a> <a
                v-if="hasPermission('hnsduser:importUser')"
                class="btn btn-primary" @click="templateShowI">&nbsp;导入</a> <input
                style="display: none;" type="file" @change="uploadFile"/>
            <a
                    v-if="hasPermission('hnsduser:exportUser')"
                    class="btn btn-primary" @click="outUser">&nbsp;导出</a>
            <a
                v-if="hasPermission('hnsduser:update')" class="btn btn-primary"
                @click="update2">&nbsp;修改</a>
            <a v-if="hasPermission('hnsduser:delete')" class="btn btn-primary"
               @click="del">&nbsp;删除</a>
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="UserList">
            <div class="form-group">
                <div class="col-sm-2 control-label">姓名:</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdUser.userName"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">销售员编码:</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdUser.userNumber"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">揽机工号</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control" v-model="hnsdUser.userId"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">受理工号:</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdUser.userAcceptId"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">手机号码:</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdUser.userPhone"/>
                </div>
            </div>


            <div class="form-group">
                <div class="col-sm-2 control-label">员工绑定微信号:</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdUser.userWechat"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">QQ邮箱:</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control" v-model="hnsdUser.email"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">QQ</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control" v-model="hnsdUser.qq"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">部门:</div>
                <div class="col-sm-10">
                    <input type="text" class="form-control"
                           v-model="hnsdUser.department"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">所属团队:</div>
                <div class="col-sm-10">
                    <select style="height: 30px; width: 110px;"
                            v-model="hnsdUser.teamCode">
                        <option v-for="team in teamList" v-bind:value="team.teamCode">
                            {{team.teamName}}
                        </option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">所在城市:</div>
                <div class="col-sm-10">
                    <select style="height: 30px; width: 110px;"
                            v-model="hnsdUser.citycode">
                        <option v-for="itme in city" v-bind:value="itme.cityCode">
                            {{itme.cityName}}
                        </option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">角色:</div>
                <select style="height: 30px; width: 85px;" v-model="hnsdUser.role">
                    <option v-for="itme in role" v-bind:value="itme.id">
                        {{itme.roleName}}
                    </option>
                </select>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">状态:</div>
                <select style="height: 30px; width: 85px;"
                        v-model="hnsdUser.status">
                    <option v-for="itme in status" v-bind:value="itme.statusId">
                        {{itme.statusName}}
                    </option>
                </select>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">渠道:</div>
                <select style="height: 30px; width: 85px;"
                        v-model="hnsdUser.channel">
                    <option v-for="itme in channel" v-bind:value="itme.channelId">
                        {{itme.channelName}}
                    </option>
                </select>
            </div>
            <div class="form-group">
                <div class="col-sm-2 control-label">模块</div>
                <div class="col-sm-10">
                    <label v-for="module in moduleDataList" class="checkbox-inline"
                           style="margin-left: 0.1%; padding-right: 6%;"> <input
                            type="checkbox" :value="module.id" v-model="moduleLis"
                            style="margin-left: -15px;">{{module.moduleSubclassName}}
                    </label>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-2 control-label"></div>
                <input type="button" class="btn btn-primary" @click="saveOrUpdate"
                       v-show="!showSure" value="确定"/> &nbsp;&nbsp;<input type="button"
                                                                          class="btn btn-warning" @click="reload"
                                                                          value="返回"/>
            </div>
        </form>
    </div>

    <div v-show="!templateShow" id="templateShow"
         class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <div class="form-horizontal" style="padding-top: 0px; width: 100%;">
            <form id="uploadImg" enctype="multipart/form-data">
                <div class="templateShow-Info">
                    <p>下载模板：</p>
                    <p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
                </div>
                <div style="margin-left: 125px;">
                    <a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
                </div>
                <div class="templateShow-Info">
                    <p>上传文件：</p>
                    <p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
                </div>
                <div style="margin-left: 125px;">
                    <a class="btn btn-primary" @click="importFile">&nbsp;开始导入</a> <input
                        style="display: none;" name="uploadFile" id="uploadFile"
                        type="file" @change="uploadFile"/>
                </div>
                <div style="width: 100%; text-align: center;">
                    <input type="button" class="btn btn-warning" @click="htmlReolad"
                           value="返回"/>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../../js/modules/hnsd/hnsduser.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>