<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxH5ApprovalContentMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_h5_approval_content a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.approvalScenario != null">
                AND a.APPROVAL_SCENARIO LIKE CONCAT('%', #{param.approvalScenario}, '%')
            </if>
            <if test="param.approvalTypeName != null">
                AND a.APPROVAL_TYPE_NAME LIKE CONCAT('%', #{param.approvalTypeName}, '%')
            </if>
            <if test="param.approvalTypeId != null">
                AND a.APPROVAL_TYPE_ID LIKE CONCAT('%', #{param.approvalTypeId}, '%')
            </if>
<!--             <if test="param.categoryOfApproval != null"> -->
<!--                 AND a.CATEGORY_OF_APPROVAL LIKE CONCAT('%', #{param.categoryOfApproval}, '%') -->
<!--             </if> -->
<!--             <if test="param.approvalSubclass != null"> -->
<!--                 AND a.APPROVAL_SUBCLASS LIKE CONCAT('%', #{param.approvalSubclass}, '%') -->
<!--             </if> -->
<!--             <if test="param.objectOfApproval != null"> -->
<!--                 AND a.OBJECT_OF_APPROVAL LIKE CONCAT('%', #{param.objectOfApproval}, '%') -->
<!--             </if> -->
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.phoneType != null">
                AND a.PHONE_TYPE = #{param.phoneType}
            </if>
<!--             <if test="param.isDefaultApprovalObject != null"> -->
<!--                 AND a.IS_DEFAULT_APPROVAL_OBJECT = #{param.isDefaultApprovalObject} -->
<!--             </if> -->
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxH5ApprovalContent">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxH5ApprovalContent">
        <include refid="selectSql"></include>
    </select>

</mapper>
