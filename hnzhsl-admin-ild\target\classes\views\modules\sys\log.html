<!DOCTYPE html>
<html>
<head>
<title>系统日志</title>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>
<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak >
	<div>
		<div class="grid-btn">
			<div class="form-group col-sm-2">
				<input type="text" class="form-control" v-model="q.key" @keyup.enter="query" placeholder="用户名、用户操作">
			</div>
			<a class="btn btn-default" @click="query">查询</a>
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
    
    <!-- 日志详情弹窗 -->
    <div id="logDetailLayer" style="display: none; padding: 10px;">
        <div class="panel panel-info">
            <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-user"></i> 用户信息</h3>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">用户名：</label>
                            <span>{{log.username}}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">工号：</label>
                            <span>{{log.workNumber}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="panel panel-info">
            <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-cog"></i> 操作信息</h3>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">日志类型：</label>
                            <span class="label" :class="getLogTypeClass()">{{log.logTypeName}}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">创建时间：</label>
                            <span>{{log.createDate}}</span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">操作菜单：</label>
                            <span>{{log.operateMenu}}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">操作按钮：</label>
                            <span>{{log.operateButton}}</span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="control-label">用户操作：</label>
                            <span>{{log.operation}}</span>
                        </div>
                    </div>
                </div>
                <div class="row" v-if="log.operateContent">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="control-label">操作内容：</label>
                            <div class="well well-sm">{{log.operateContent}}</div>
                        </div>
                    </div>
                </div>
                <div class="row" v-if="log.operateReason">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="control-label">操作原因：</label>
                            <div class="well well-sm">{{log.operateReason}}</div>
                        </div>
                    </div>
                </div>
                <div class="row" v-if="log.approver">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="control-label">审批人：</label>
                            <span>{{log.approver}}</span>
                        </div>
                    </div>
                </div>
                <div class="row" v-if="log.logType === 3">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="control-label">批量记录数：</label>
                            <span class="badge">{{log.batchCount}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 导出操作相关信息 -->
        <div class="panel panel-info" v-if="log.logType === 4">
            <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-download"></i> 导出信息</h3>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">数据名称：</label>
                            <span>{{log.exportDataName}}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">数据量：</label>
                            <span class="badge">{{log.exportDataCount}}</span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="control-label">导出条件：</label>
                            <div class="well well-sm">{{log.exportCondition || '无'}}</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">文件大小：</label>
                            <span>{{log.exportFileSize}} KB</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">保存期限：</label>
                            <span>{{log.retentionPeriod}} 个月</span>
                        </div>
                    </div>
                </div>
                <div class="row" v-if="log.exportFields">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="control-label">导出字段：</label>
                            <div class="well well-sm">{{log.exportFields}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="panel panel-info">
            <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-code"></i> 技术信息</h3>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">请求方法：</label>
                            <code>{{log.method}}</code>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">IP地址：</label>
                            <span>{{log.ip}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../../js/modules/sys/log.js?v=1.1"></script>
</body>
</html>