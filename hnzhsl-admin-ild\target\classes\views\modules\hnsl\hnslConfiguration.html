<!DOCTYPE html>
<html>
<head>
    <title>2</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../js/common.js"></script>
</head>
<body>
<div>
    <label>登录获取位置信息</label>
</div>
<div id="rrapp" v-cloak>
    <div v-show="showLocation">
        <div class="row">
            <div class="form-group col-md-2">
                <label>位置服务:</label>
                <select class="form-control"
                                             style="height: 32px;" v-model="locationStatus">
                <option value='0'>开启</option>
                <option value='1'>关闭</option>
            </select>
            </div>
            <div class="form-group col-md-2" style="height: 40px;width: 450px">
                <label>位置服务key:</label> <input type="text" class="form-control"
                                          placeholder="key" v-model="locationKey" />
            </div>
        </div>
        <div class="grid-btn" style="margin-left: 17Px; margin-right: 17Px;margin-top: 18px">
            <a v-if="hasPermission('hnslusercardpool:query')" class="btn btn-primary" @click="update(1)"><i
                   ></i>&nbsp;保存位置服务状态</a>

            <a v-if="hasPermission('hnsdusercardpool:save')" class="btn btn-primary" @click="update(2)"
               style="margin-left: 85px;"><i></i>保存位置服务key</a>

        </div>
    </div>
</div>

<script src="../../js/modules/hnsl/hnslConfiguration.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>