<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslOrderMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        select tt.*,
        t1.user_name,t1.status_sf,t2.goods_name,t3.school_name,su.enroltime,t1.sales_code,t1.numbers,t1.CHANNEL_TYPE,wl.INTEGRAL_OPERATION
        from
        (
            select a.citycode,a.created_date,a.status,a.customer_name,a.safl_type,a.order_status
            ,a.customer_phone,a.order_id,a.bps_order_id,a.hhid,a.hhid_school
            ,a.goods_number,a.client_number,a.id,a.crm_order_id,a.customer_contact_phone,
            a.order_price,a.iccid,a.SIGNATURE_STATUS,a.SCHOOL_CHANNEL_TYPE,a.BROAD_BAND
            from hnsl_order a
            <where>
                <if test="param.id != null">
                    AND a.ID = #{param.id}
                </if>
                <if test="param.orderId != null">
                    AND a.ORDER_ID LIKE CONCAT('%', #{param.orderId}, '%')
                </if>
                <if test="param.goodsNumber != null">
                    AND a.GOODS_NUMBER LIKE CONCAT('%', #{param.goodsNumber}, '%')
                </if>
                <if test="param.userId != null">
                    AND a.USER_ID LIKE CONCAT('%', #{param.userId}, '%')
                </if>
                <if test="param.orderPrice != null">
                    AND a.ORDER_PRICE = #{param.orderPrice}
                </if>
                <if test="param.identityCardImage1 != null">
                    AND a.IDENTITY_CARD_IMAGE1 LIKE CONCAT('%', #{param.identityCardImage1}, '%')
                </if>
                <if test="param.identityCardImage2 != null">
                    AND a.IDENTITY_CARD_IMAGE2 LIKE CONCAT('%', #{param.identityCardImage2}, '%')
                </if>
                <if test="param.identityCardImage3 != null">
                    AND a.IDENTITY_CARD_IMAGE3 LIKE CONCAT('%', #{param.identityCardImage3}, '%')
                </if>
                <if test="param.livingbodyVideo != null">
                    AND a.LIVINGBODY_VIDEO LIKE CONCAT('%', #{param.livingbodyVideo}, '%')
                </if>
                <if test="param.customerName != null">
                    AND a.CUSTOMER_NAME LIKE CONCAT('%', #{param.customerName}, '%')
                </if>
                <if test="param.customerCard != null">
                    AND a.CUSTOMER_CARD LIKE CONCAT('%', #{param.customerCard}, '%')
                </if>
                <if test="param.customerCardValidity != null">
                    AND a.CUSTOMER_CARD_VALIDITY LIKE CONCAT('%', #{param.customerCardValidity}, '%')
                </if>
                <if test="param.customerContactPhone != null">
                    AND a.CUSTOMER_CONTACT_PHONE LIKE CONCAT('%', #{param.customerContactPhone}, '%')
                </if>
                <if test="param.customerPhone != null">
                    AND a.CUSTOMER_PHONE LIKE CONCAT('%', #{param.customerPhone}, '%')
                </if>
                <if test="param.customerAddress != null">
                    AND a.CUSTOMER_ADDRESS LIKE CONCAT('%', #{param.customerAddress}, '%')
                </if>
                <if test="param.iccid != null">
                    AND a.ICCID LIKE CONCAT('%', #{param.iccid}, '%')
                </if>
                <if test="param.citycode != null">
                    AND a.CITYCODE LIKE CONCAT('%', #{param.citycode}, '%')
                </if>
                <if test="param.phoneNbrprice != null">
                    AND a.PHONE_NBRPRICE LIKE CONCAT('%', #{param.phoneNbrprice}, '%')
                </if>
                <if test="param.phonePreprice != null">
                    AND a.PHONE_PREPRICE LIKE CONCAT('%', #{param.phonePreprice}, '%')
                </if>
                <if test="param.preType != null">
                    AND a.PRE_TYPE LIKE CONCAT('%', #{param.preType}, '%')
                </if>
                <if test="param.installCity != null">
                    AND a.INSTALL_CITY LIKE CONCAT('%', #{param.installCity}, '%')
                </if>
                <if test="param.installArea != null">
                    AND a.INSTALL_AREA LIKE CONCAT('%', #{param.installArea}, '%')
                </if>
                <if test="param.installDay != null">
                    AND a.INSTALL_DAY LIKE CONCAT('%', #{param.installDay}, '%')
                </if>
                <if test="param.installTimeInterval != null">
                    AND a.INSTALL_TIME_INTERVAL LIKE CONCAT('%', #{param.installTimeInterval}, '%')
                </if>
                <if test="param.stbType != null">
                    AND a.STB_TYPE LIKE CONCAT('%', #{param.stbType}, '%')
                </if>
                <if test="param.payChannel != null">
                    AND a.PAY_CHANNEL LIKE CONCAT('%', #{param.payChannel}, '%')
                </if>
                <if test="param.invoicesType != null">
                    AND a.INVOICES_TYPE LIKE CONCAT('%', #{param.invoicesType}, '%')
                </if>
                <if test="param.invoicesEmail != null">
                    AND a.INVOICES_EMAIL LIKE CONCAT('%', #{param.invoicesEmail}, '%')
                </if>
                <if test="param.businessOrder != null">
                    AND a.BUSINESS_ORDER LIKE CONCAT('%', #{param.businessOrder}, '%')
                </if>
                <if test="param.orderRemark != null">
                    AND a.ORDER_REMARK LIKE CONCAT('%', #{param.orderRemark}, '%')
                </if>
                <if test="param.orderStatus != null">
                    AND a.ORDER_STATUS LIKE CONCAT('%', #{param.orderStatus}, '%')
                </if>
                <if test="param.orderSubmitDate != null">
                    AND a.ORDER_SUBMIT_DATE LIKE CONCAT('%', #{param.orderSubmitDate}, '%')
                </if>
                <if test="param.orderReviewedDate != null">
                    AND a.ORDER_REVIEWED_DATE LIKE CONCAT('%', #{param.orderReviewedDate}, '%')
                </if>
                <if test="param.orderActivateDate != null">
                    AND a.ORDER_ACTIVATE_DATE LIKE CONCAT('%', #{param.orderActivateDate}, '%')
                </if>
                <if test="param.crmOrderId != null">
                    AND a.CRM_ORDER_ID LIKE CONCAT('%', #{param.crmOrderId}, '%')
                </if>
                <if test="param.bpsOrderId != null">
                    AND a.BPS_ORDER_ID LIKE CONCAT('%', #{param.bpsOrderId}, '%')
                </if>
                <if test="param.custId != null">
                    AND a.CUST_ID LIKE CONCAT('%', #{param.custId}, '%')
                </if>
                <if test="param.bpsFormalSynchro != null">
                    AND a.BPS_FORMAL_SYNCHRO LIKE CONCAT('%', #{param.bpsFormalSynchro}, '%')
                </if>
                <if test="param.bpsRealnameSynchro != null">
                    AND a.BPS_REALNAME_SYNCHRO LIKE CONCAT('%', #{param.bpsRealnameSynchro}, '%')
                </if>
                <if test="param.crmSureSynchro != null">
                    AND a.CRM_SURE_SYNCHRO LIKE CONCAT('%', #{param.crmSureSynchro}, '%')
                </if>
                <if test="param.createdUser != null">
                    AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
                </if>
                <if test="param.createdDate != null">
                    AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
                </if>
                <if test="param.updatedUser != null">
                    AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
                </if>
                <if test="param.updatedDate != null">
                    AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
                </if>
                <if test="param.hhid != null">
                    AND a.HHID LIKE CONCAT('%', #{param.hhid}, '%')
                </if>
                <if test="param.clientNumber != null">
                    AND a.CLIENT_NUMBER LIKE CONCAT('%', #{param.clientNumber}, '%')
                </if>
                <if test="param.saflType != null">
                    AND a.SAFL_TYPE = #{param.saflType}
                </if>
                <if test="param.mailingAddress != null">
                    AND a.MAILING_ADDRESS LIKE CONCAT('%', #{param.mailingAddress}, '%')
                </if>
                <if test="param.mailingDetailedAddress != null">
                    AND a.MAILING_DETAILED_ADDRESS LIKE CONCAT('%', #{param.mailingDetailedAddress}, '%')
                </if>
                <if test="param.customerSex != null">
                    AND a.CUSTOMER_SEX = #{param.customerSex}
                </if>
                <if test="param.hhidSchool != null">
                    AND a.HHID_SCHOOL LIKE CONCAT('%', #{param.hhidSchool}, '%')
                </if>
                <if test="param.bandOrderId != null">
                    AND a.BAND_ORDER_ID LIKE CONCAT('%', #{param.bandOrderId}, '%')
                </if>
                <if test="param.broadBand != null">
                    AND a.BROAD_BAND = #{param.broadBand}
                </if>
                <if test="param.status != null">
                    AND a.STATUS = #{param.status}
                </if>
                <if test="param.errorStatus != null">
                    AND a.ERROR_STATUS = #{param.errorStatus}
                </if>
                <if test="param.crmAskId != null">
                    AND a.CRM_ASK_ID LIKE CONCAT('%', #{param.crmAskId}, '%')
                </if>
                <if test="param.dispose != null">
                    AND a.DISPOSE = #{param.dispose}
                </if>
                <if test="param.synchronizationBps != null">
                    AND a.SYNCHRONIZATION_BPS = #{param.synchronizationBps}
                </if>
                <if test="param.installationType != null">
                    AND a.INSTALLATION_TYPE = #{param.installationType}
                </if>
                <if test="param.identityCardImage4 != null">
                    AND a.IDENTITY_CARD_IMAGE4 LIKE CONCAT('%', #{param.identityCardImage4}, '%')
                </if>
                <if test="param.identityCardImage5 != null">
                    AND a.IDENTITY_CARD_IMAGE5 LIKE CONCAT('%', #{param.identityCardImage5}, '%')
                </if>
                <if test="param.identityCardImage6 != null">
                    AND a.IDENTITY_CARD_IMAGE6 LIKE CONCAT('%', #{param.identityCardImage6}, '%')
                </if>
                <if test="param.grade != null">
                    AND a.GRADE LIKE CONCAT('%', #{param.grade}, '%')
                </if>
                <if test="param.childCustomerName != null">
                    AND a.CHILD_CUSTOMER_NAME LIKE CONCAT('%', #{param.childCustomerName}, '%')
                </if>
                <if test="param.childCustomerCard != null">
                    AND a.CHILD_CUSTOMER_CARD LIKE CONCAT('%', #{param.childCustomerCard}, '%')
                </if>
                <if test="param.childCustomerAddress != null">
                    AND a.CHILD_CUSTOMER_ADDRESS LIKE CONCAT('%', #{param.childCustomerAddress}, '%')
                </if>
                <if test="param.childCustId != null">
                    AND a.CHILD_CUST_ID LIKE CONCAT('%', #{param.childCustId}, '%')
                </if>
                <if test="param.childCustomerCardValidity != null">
                    AND a.CHILD_CUSTOMER_CARD_VALIDITY LIKE CONCAT('%', #{param.childCustomerCardValidity}, '%')
                </if>
                <if test="param.livingbodyVideo1 != null">
                    AND a.LIVINGBODY_VIDEO1 LIKE CONCAT('%', #{param.livingbodyVideo1}, '%')
                </if>
                <if test="param.reviewTypes != null">
                    AND a.REVIEW_TYPES = #{param.reviewTypes}
                </if>
                <if test="param.payOrderno != null">
                    AND a.PAY_ORDERNO LIKE CONCAT('%', #{param.payOrderno}, '%')
                </if>
                <if test="param.bpsActivateNotification != null">
                    AND a.BPS_ACTIVATE_NOTIFICATION = #{param.bpsActivateNotification}
                </if>
                <if test="param.bpsActivateRemark != null">
                    AND a.BPS_ACTIVATE_REMARK LIKE CONCAT('%', #{param.bpsActivateRemark}, '%')
                </if>
                <if test="param.bpsActivateDate != null">
                    AND a.BPS_ACTIVATE_DATE LIKE CONCAT('%', #{param.bpsActivateDate}, '%')
                </if>
                <if test="param.bpsFormalActivate != null">
                    AND a.BPS_FORMAL_ACTIVATE = #{param.bpsFormalActivate}
                </if>
                <if test="param.bpsRealnameActivate != null">
                    AND a.BPS_REALNAME_ACTIVATE = #{param.bpsRealnameActivate}
                </if>
                <if test="param.signatureStatus != null">
                    AND a.SIGNATURE_STATUS = #{param.signatureStatus}
                </if>
                <if test="param.photographStatus != null">
                    AND a.PHOTOGRAPH_STATUS = #{param.photographStatus}
                </if>
                <if test="param.crmOrderPrice != null">
                    AND a.CRM_ORDER_PRICE LIKE CONCAT('%', #{param.crmOrderPrice}, '%')
                </if>
                <if test="param.orderOpenid != null">
                    AND a.ORDER_OPENID LIKE CONCAT('%', #{param.orderOpenid}, '%')
                </if>
                <if test="param.touchId != null">
                    AND a.TOUCH_ID LIKE CONCAT('%', #{param.touchId}, '%')
                </if>
                <if test="param.identityVideoImage1 != null">
                    AND a.IDENTITY_VIDEO_IMAGE1 LIKE CONCAT('%', #{param.identityVideoImage1}, '%')
                </if>
                <if test="param.identityVideoImage2 != null">
                    AND a.IDENTITY_VIDEO_IMAGE2 LIKE CONCAT('%', #{param.identityVideoImage2}, '%')
                </if>
                <if test="param.schoolChannelType != null">
                    AND a.SCHOOL_CHANNEL_TYPE = #{param.schoolChannelType}
                </if>
                <if test="param.wtOrderId != null">
                    AND a.WT_ORDER_ID LIKE CONCAT('%', #{param.wtOrderId}, '%')
                </if>
                <if test="param.workPermitList != null">
                    AND a.WORK_PERMIT_LIST LIKE CONCAT('%', #{param.workPermitList}, '%')
                </if>
                <if test="param.merchantCode != null">
                    AND a.MERCHANT_CODE LIKE CONCAT('%', #{param.merchantCode}, '%')
                </if>
                <if test="param.ftpActivateDate != null">
                    AND a.FTP_ACTIVATE_DATE LIKE CONCAT('%', #{param.ftpActivateDate}, '%')
                </if>
                <if test="param.preventionControlAddress != null">
                    AND a.PREVENTION_CONTROL_ADDRESS LIKE CONCAT('%', #{param.preventionControlAddress}, '%')
                </if>
                <if test="param.reservationOrderId != null">
                    AND a.RESERVATION_ORDER_ID LIKE CONCAT('%', #{param.reservationOrderId}, '%')
                </if>
                <if test="param.reservationCustomerName != null">
                    AND a.RESERVATION_CUSTOMER_NAME LIKE CONCAT('%', #{param.reservationCustomerName}, '%')
                </if>
                <if test="param.reservationContactPhone != null">
                    AND a.RESERVATION_CONTACT_PHONE LIKE CONCAT('%', #{param.reservationContactPhone}, '%')
                </if>
                <if test="param.reservationDate != null">
                    AND a.RESERVATION_DATE LIKE CONCAT('%', #{param.reservationDate}, '%')
                </if>
                <if test="param.reservationContactAddress != null">
                    AND a.RESERVATION_CONTACT_ADDRESS LIKE CONCAT('%', #{param.reservationContactAddress}, '%')
                </if>
                <if test="param.reservationCustomerCard != null">
                    AND a.RESERVATION_CUSTOMER_CARD LIKE CONCAT('%', #{param.reservationCustomerCard}, '%')
                </if>
                <if test="param.reservationStatus != null">
                    AND a.RESERVATION_STATUS = #{param.reservationStatus}
                </if>
            </where>
            ) tt
            left join hnsl_user t1 on tt.hhid=t1.user_phone
            left join hnsl_goods t2 on tt.goods_number=t2.goods_number
            left join HNSL_SCHOOL_USER su ON tt.CLIENT_NUMBER = su.CLIENT_NUMBER
            left join hnsl_school t3 on tt.hhid_school=t3.school_code
            left join hnsl_integral wl on tt.order_id=wl.ORDER_ID
        <where>
            <if test="param.schoolName!=null and param.schoolName!=''">
                AND t3.SCHOOL_NAME like concat(concat('%',#{param.schoolName}),'%')
            </if>
            <if test="param.goodsName!=null and param.goodsName!=''">
                AND t2.goods_name like concat(concat('%',#{param.goodsName}),'%')
            </if>
            <if test="param.statusSf !='-1' and param.statusSf !=null and param.statusSf!=''">
                AND t1.STATUS_SF =#{param.statusSf}
            </if>
        </where>
            order by tt.created_date desc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslOrder">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslOrder">
        <include refid="selectSql"></include>
    </select>


    <select id="queryObject" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslOrder">
        select t.*,a.user_name,b.goods_name,c.name,c.school_code,c.building_id ,c.room_id,c.enroltime,bb.school_name,dd.building_name,ff.room_name from  HNSL_ORDER  t left join HNSL_USER a on t.HHID = a.user_phone left join HNSL_GOODS b ON t.goods_number = b.goods_number left join HNSL_SCHOOL_USER C ON C.CLIENT_NUMBER = t.CLIENT_NUMBER  left join HNSL_SCHOOL bb ON t.hhid_school = bb.school_code left join HNSL_BUILDING dd on dd.building_id = C.building_id left join HNSL_ROOM ff ON ff.room_number = C.room_id
        <where>
            ${ew.sqlSegment}
        </where>
        LIMIT 1
    </select>


</mapper>
