<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppDedicatedCircuitMainPackageMapper">
    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_dedicated_circuit_main_package a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.dedicatedCircuitType != null">
                AND a.DEDICATED_CIRCUIT_TYPE = #{param.dedicatedCircuitType}
            </if>
            <if test="param.dedicatedCircuitName != null">
                AND a.DEDICATED_CIRCUIT_NAME LIKE CONCAT('%', #{param.dedicatedCircuitName}, '%')
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.cityName != null">
                AND a.CITY_NAME LIKE CONCAT('%', #{param.cityName}, '%')
            </if>
            <if test="param.goodsName != null">
                AND a.GOODS_NAME LIKE CONCAT('%', #{param.goodsName}, '%')
            </if>
            <if test="param.offerId != null">
                AND a.OFFER_ID LIKE CONCAT('%', #{param.offerId}, '%')
            </if>
            <if test="param.offerName != null">
                AND a.OFFER_NAME LIKE CONCAT('%', #{param.offerName}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.UPDATE_DATE LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
            <if test="param.offerLevel != null">
                AND a.OFFER_LEVEL LIKE CONCAT('%', #{param.offerLevel}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitMainPackage">
        <include refid="selectSql">
        </include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitMainPackage">
        <include refid="selectSql">
        </include>
    </select>

    <resultMap id="mainPackage" type="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitMainPackage">
        <result column="ID" property="id"/>
        <result column="DEDICATED_CIRCUIT_TYPE" property="dedicatedCircuitType"/>
        <result column="DEDICATED_CIRCUIT_NAME" property="dedicatedCircuitName"/>
        <result column="CITY_CODE" property="cityCode"/>
        <result column="CITY_NAME" property="cityName"/>
        <result column="GOODS_NAME" property="goodsName"/>
        <result column="OFFER_ID" property="offerId"/>
        <result column="OFFER_NAME" property="offerName"/>
        <association property="dedicatedCircuitMainPackageEquity" column="id"
                     select="getDedicatedCircuitMainPackageEquity">
            <result column="MAIN_PACKAGE_ID" property="mainPackageId"/>
            <result column="EQUITY_LEVEL" property="equityLevel"/>
            <result column="OFFER_ID" property="offerId"/>
            <result column="OFFER_NAME" property="offerName"/>
            <result column="DEPOSIT_AMOUNT" property="depositAmount"/>
            <result column="EQUITY_LEVEL_NAME" property="equityLevelName"/>
        </association>

        <association property="dedicatedCircuitMainPackageIp" column="id"
                     select="getDedicatedCircuitMainPackageIp">
            <result column="MAIN_PACKAGE_ID" property="mainPackageId"/>
            <result column="IP_NAME" property="ipName"/>
            <result column="IP_OFFER_ID" property="ipOfferId"/>
            <result column="IP_OFFER_NAME" property="ipOfferName"/>
        </association>
    </resultMap>

    <select id="getDedicatedCircuitMainPackageEquity"
            resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitMainPackageEquity">
        select *
        from hnzsx_dedicated_circuit_main_package_equity
        where MAIN_PACKAGE_ID = #{id}
    </select>

    <select id="getDedicatedCircuitMainPackageIp"
            resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitMainPackageIp">
        select *
        from hnzsx_dedicated_circuit_main_package_ip
        where MAIN_PACKAGE_ID = #{id}
    </select>

    <!--  查询专线主套餐配置  -->
    <select id="getDedicatedCircuitMainPackage" parameterType="map" resultMap="mainPackage">
        <include refid="selectSql"></include>
    </select>
</mapper>
