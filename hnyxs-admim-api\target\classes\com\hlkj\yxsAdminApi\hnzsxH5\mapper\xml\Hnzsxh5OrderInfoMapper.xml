<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5OrderInfoMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsxh5_order_info a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.orderNo != null">
                AND a.ORDER_NO LIKE CONCAT('%', #{param.orderNo}, '%')
            </if>
            <if test="param.userName != null">
                AND a.USER_NAME LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.userMoble != null">
                AND a.USER_MOBLE LIKE CONCAT('%', #{param.userMoble}, '%')
            </if>
            <if test="param.userCard != null">
                AND a.USER_CARD LIKE CONCAT('%', #{param.userCard}, '%')
            </if>
            <if test="param.userAddress != null">
                AND a.USER_ADDRESS LIKE CONCAT('%', #{param.userAddress}, '%')
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.cityName != null">
                AND a.CITY_NAME LIKE CONCAT('%', #{param.cityName}, '%')
            </if>
            <if test="param.goodsId != null">
                AND a.GOODS_ID = #{param.goodsId}
            </if>
            <if test="param.goodsName != null">
                AND a.GOODS_NAME LIKE CONCAT('%', #{param.goodsName}, '%')
            </if>
            <if test="param.goodsImgUrl != null">
                AND a.GOODS_IMG_URL LIKE CONCAT('%', #{param.goodsImgUrl}, '%')
            </if>
            <if test="param.goodsPrice != null">
                AND a.GOODS_PRICE LIKE CONCAT('%', #{param.goodsPrice}, '%')
            </if>
            <if test="param.templateInfoIdList != null">
                AND a.TEMPLATE_INFO_ID_LIST LIKE CONCAT('%', #{param.templateInfoIdList}, '%')
            </if>
            <if test="param.serviceOfferId != null">
                AND a.SERVICE_OFFER_ID LIKE CONCAT('%', #{param.serviceOfferId}, '%')
            </if>
            <if test="param.state != null">
                AND a.STATE = #{param.state}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.templateIdList != null">
                AND a.TEMPLATE_ID_LIST LIKE CONCAT('%', #{param.templateIdList}, '%')
            </if>
            <if test="param.sceneInstIdList != null">
                AND a.SCENE_INST_ID_LIST LIKE CONCAT('%', #{param.sceneInstIdList}, '%')
            </if>
            <if test="param.custOrderIdList != null">
                AND a.CUST_ORDER_ID_LIST LIKE CONCAT('%', #{param.custOrderIdList}, '%')
            </if>
            <if test="param.custOrderId != null">
                AND a.CUST_ORDER_ID_LIST LIKE CONCAT('%', #{param.custOrderId}, '%')
            </if>
            <if test="param.totalAmount != null">
                AND a.TOTAL_AMOUNT LIKE CONCAT('%', #{param.totalAmount}, '%')
            </if>
            <if test="param.loginUserId != null">
                AND a.LOGIN_USER_ID = #{param.loginUserId}
            </if>
            <if test="param.loginStaffCode != null">
                AND a.LOGIN_STAFF_CODE LIKE CONCAT('%', #{param.loginStaffCode}, '%')
            </if>
            <if test="param.loginUserAcceptId != null">
                AND a.LOGIN_USER_ACCEPT_ID LIKE CONCAT('%', #{param.loginUserAcceptId}, '%')
            </if>
            <if test="param.loginUserNumber != null">
                AND a.LOGIN_USER_NUMBER LIKE CONCAT('%', #{param.loginUserNumber}, '%')
            </if>
            <if test="param.loginUserName != null">
                AND a.LOGIN_USER_NAME LIKE CONCAT('%', #{param.loginUserName}, '%')
            </if>
            <if test="param.loginUserPhone != null">
                AND a.LOGIN_USER_PHONE LIKE CONCAT('%', #{param.loginUserPhone}, '%')
            </if>
            <if test="param.loginSaleBoxCode != null">
                AND a.LOGIN_SALE_BOX_CODE LIKE CONCAT('%', #{param.loginSaleBoxCode}, '%')
            </if>
            <if test="param.loginSalesOrgCode != null">
                AND a.LOGIN_SALES_ORG_CODE LIKE CONCAT('%', #{param.loginSalesOrgCode}, '%')
            </if>
            <if test="param.paymentMethod != null">
                AND a.PAYMENT_METHOD LIKE CONCAT('%', #{param.paymentMethod}, '%')
            </if>
            <if test="param.paymentStatus != null">
                AND a.PAYMENT_STATUS = #{param.paymentStatus}
            </if>
            <if test="param.paymentTransactionId != null">
                AND a.PAYMENT_TRANSACTION_ID LIKE CONCAT('%', #{param.paymentTransactionId}, '%')
            </if>
            <if test="param.paymentTime != null">
                AND a.PAYMENT_TIME LIKE CONCAT('%', #{param.paymentTime}, '%')
            </if>
            <if test="param.signatureStatus != null">
                AND a.SIGNATURE_STATUS = #{param.signatureStatus}
            </if>
            <if test="param.liveStatus != null">
                AND a.LIVE_STATUS = #{param.liveStatus}
            </if>
            <if test="param.custId != null">
                AND a.CUST_ID LIKE CONCAT('%', #{param.custId}, '%')
            </if>
            <if test="param.receptInvoiceEmail != null">
                AND a.RECEPT_INVOICE_EMAIL LIKE CONCAT('%', #{param.receptInvoiceEmail}, '%')
            </if>
            <if test="param.pushInvoiceWay != null">
                AND a.PUSH_INVOICE_WAY LIKE CONCAT('%', #{param.pushInvoiceWay}, '%')
            </if>
            <if test="param.paymentStatusName != null">
                AND a.PAYMENT_STATUS_NAME LIKE CONCAT('%', #{param.paymentStatusName}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5OrderInfo">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5OrderInfo">
        <include refid="selectSql"></include>
    </select>

</mapper>
