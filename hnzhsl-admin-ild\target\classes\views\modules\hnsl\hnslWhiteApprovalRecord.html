<!DOCTYPE html>
<html>
<head>
    <title>白名单审批记录</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showList">
        <div class="row">
            <div class="form-group col-md-2" style="height: 40px">
                <label>批次号:</label>
                <input type="text" class="form-control" placeholder="请输入批次号" v-model="hnslWhiteApprovalRecord.batchCode" />
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>文件名:</label>
                <input type="text" class="form-control" placeholder="请输入文件名" v-model="hnslWhiteApprovalRecord.fileName" />
            </div>
            <div class="form-group col-md-2" style="height: 32px;">
                <label>提交日期:</label>
                <div class="input-group col-ms-2 ">
                    <input class="form-control pull-left dateRange date-picker "
                           id="dateTimeRange" @keyup.enter="query" value="" type="text"
                           placeholder="请选择申请日期">
                    <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
 						</span>
                    <input name="beginTime" id="beginTime" type="hidden">
                </div>
            </div>
        </div>
        <div class="grid-btn" style="margin-left: 17Px; margin-top: 18px">
            <a v-if="hasPermission('hnslwhiteapprovalrecord:query')" class="btn btn-primary" @click="query"><i></i>&nbsp;查询</a>
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>

    <!--模板下载-->
    <div v-show="!templateShow" id="templateShow"
         class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <div class="form-horizontal" style="padding-top: 0px; width: 100%;">
            <form id="uploadImg" enctype="multipart/form-data">
                <div class="templateShow-Info">
                    <p>下载模板：</p>
                    <p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
                </div>
                <div style="margin-left: 125px;">
                    <a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
                </div>
                <div class="templateShow-Info">
                    <p>上传文件：</p>
                    <p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
                </div>
                <div style="margin-left: 125px;">
                    <a v-if="hasPermission('hnsduser:importUser')"
                       class="btn btn-primary" @click="importUser">&nbsp;开始导入</a> <input
                        style="display: none;" name="uploadFile" id="uploadFile"
                        type="file" @change="uploadFile" />
                </div>
                <div style="width: 100%; text-align: center;">
                    <input type="button" class="btn btn-warning" @click="reload"
                           value="返回" />
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../../js/modules/hnsl/hnslWhiteApprovalRecord.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>