<!DOCTYPE html>
<html>
<head>
    <title>2</title>
    <meta charset="UTF-8">
    <META HTTP-EQUIV="pragma" CONTENT="no-cache">
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
    <META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
    <META HTTP-EQUIV="expires" CONTENT="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="../../css/bootstrap.min.css">
    <link rel="stylesheet" href="../../css/font-awesome.min.css">
    <link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
    <link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../libs/jquery.min.js"></script>
    <script src="../../plugins/layer/layer.js"></script>
    <script src="../../libs/bootstrap.min.js"></script>
    <script src="../../libs/vue.min.js"></script>
    <script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
    <script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
    <script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

    <link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
    <!-- select2组件 -->
    <link href="../../plugins/select2/css/select2.min.css" rel="stylesheet"/>

    <!-- select2组件 -->
    <script src="../../plugins/select2/js/select2.min.js"></script>

    <script src="../../plugins/Daterangepicker/js/common.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

    <script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
    <!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

    <script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
    <div v-show="showList">
        <div class="row">
            <div class="form-group col-md-2" style="height: 40px">
                <label>号池编码:</label> <input type="text" class="form-control"
                                           v-model="hnslUserCardpool.cardPoolNumber" />
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>号池名称:</label> <input type="text" class="form-control"
                                           v-model="hnslUserCardpool.cardPoolName" />
            </div>
            <div class="form-group col-md-2" style="height: 40px">
                <label>学校:</label> <input type="text" class="form-control"
                                          placeholder="学校" v-model="hnslUserCardpool.schoolName" />
            </div>
            <div class="form-group col-md-2">
                <label>状态:</label> <select class="form-control"
                                           style="height: 32px;" v-model="hnslCardpool.status">
                <option value=''>全部</option>
                <option v-for="itme in status" v-bind:value="itme.statusId">
                    {{itme.statusName}}</option>
            </select>
            </div>
        </div>
        <div class="grid-btn" style="margin-left: 17Px; margin-top: 18px">
            <a v-if="hasPermission('hnslusercardpool:query')" class="btn btn-primary" @click="query"><i
                   ></i>&nbsp;查询</a>

            <a v-if="hasPermission('hnsdusercardpool:save')" class="btn btn-primary" @click="add"><i
                    class="fa fa-plus"></i>&nbsp;新增</a>
            <a v-if="hasPermission('hnsdusercardpool:update')" class="btn btn-primary" @click="update"><i
                    class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
            <a v-if="hasPermission('hnsdusercardpool:delete')" class="btn btn-primary" @click="del"><i
                    class="fa fa-trash-o"></i>&nbsp;删除</a>
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" style="width:100%;">
            <table class="textTable" style="width:100%;">
                <tr>
                    <td class="leftTd info-text"><label>号池编码</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="hnslUserCardpool.cardPoolNumber"/></td>
                    <td class="leftTd info-text"><label>号池名称</label></td>
                    <td style="width:50%">
                        <input type="text" v-model="hnslUserCardpool.cardPoolName"/></td>
                </tr>
                <tr>
                    <td class="leftTd info-text"><label>地市</label></td>
                    <td>
                        <select class="form-control"
                                style="height: 32px;" v-model="hnslUserCardpool.cityCode">
                            <option value='0'>无</option>
                            <option value='700'>全省</option>
                            <option value='731'>长沙</option>
                            <option value='732'>湘潭</option>
                            <option value='733'>株洲</option>
                            <option value='734'>衡阳</option>
                            <option value='735'>郴州</option>
                            <option value='736'>常德</option>
                            <option value='737'>益阳</option>
                            <option value='738'>娄底</option>
                            <option value='739'>邵阳</option>
                            <option value='730'>岳阳</option>
                            <option value='743'>湘西</option>
                            <option value='744'>张家界</option>
                            <option value='745'>怀化</option>
                            <option value='746'>永州</option>
                        </select>
                    </td>
                    <td class="leftTd info-text"><label>状态</label></td>
                    <td>
                        <select class="form-control"
                                style="height: 32px;" v-model="hnslUserCardpool.status">
                            <option value="0">无效</option>
                            <option value="1">有效</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="leftTd"><label style="text-align: center">所属学校</label></td>
                    <td style="width: 31%;">
                        <input type="text" v-model="hnslUserCardpool.schoolName" disabled/>
                        <a v-show="bianji1" class="btn btn-primary" @click="school_show()"
                           style="float: left; margin-left: 4px; ">编辑</a>
                        <a v-show="bianji2" class="btn btn-primary" @click="school_close()"
                           style="float: left; margin-left: 4px;">确定</a>
                        <a v-show="bianji2" class="btn btn-warning" @click="school_back()"
                           style="float: left; margin-left: 4px;">取消</a>
                    </td>
                    <td class="leftTd info-text"><label>渠道ID</label></td>
                    <td style="width:50%">
                        <input type="text" v-model="hnslUserCardpool.channelId"/></td>
                </tr>
                <tr>
                    <td class="leftTd info-text"><label>MD5加密值</label></td>
                    <td style="width:50%">
                        <input type="text" v-model="hnslUserCardpool.md5Key"/></td>
                    <td class="leftTd info-text"><label>状态</label></td>
                    <td>
                        <select class="form-control"
                                style="height: 32px;" v-model="hnslUserCardpool.cardPoolType">
                            <option value="1">省内3.0</option>
                            <option value="2">集团号池</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="leftTd info-text"><label>配置业务模块</label></td>
                    <td>
                        <select class="form-control"
                                style="height: 32px;" v-model="hnslUserCardpool.businessModule">
                            <option value="1">号卡新装，融合新装</option>
                            <option value="2">一人一码</option>
                            <option value="3">号卡新装，融合新装，一人一码</option>
                        </select>
                    </td>
                </tr>
            </table>
            <!-- 所属学校树形图 -->
            <div v-show="schoolBelongShow">
                <table class="textTable">
                    <span style="font-size: 25px;">&nbsp;&nbsp;&nbsp;</span>
                    <div class="tree_pag">
                        <h5>用户对应学校</h5>
                        <div class="tree_content">
                            <!--左边树状图-->
                            <div class="left_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in teamTreeRight"
                                                @click="addCity(teamTree.schoolCode,teamTree.schoolName)">
                                                <a>{{teamTree.schoolName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!--右边树状图-->
                            <div class="right_tree">
                                <div class="treebox scrollXY">
                                    <div class="tree">
                                        <ul>
                                            <li class="main" v-for="(teamTree , index) in teamTreeLeft"
                                                @click="delCity(index)"><a>{{teamTree.schoolName}}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </table>
            </div>

            <div class="form-group" style="text-align: center">
                <div class="col-sm-2 control-label"></div>
                <input v-show="sureShow" type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="clearReload" value="返回"/>
            </div>
        </form>
    </div>
</div>

<script src="../../js/modules/hnsl/hnslusercardpool.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>