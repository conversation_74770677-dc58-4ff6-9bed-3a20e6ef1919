<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta
	content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
	name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet"
	href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet"
	href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css"
	rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script
	src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script
	src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
</head>
<body>
	<div id="rrapp" v-cloak>
		<div v-show="showList" id="isShowList">

			<div class="row">
				<div class="form-group col-md-2">
					<label>团队名称:</label> <input type="text" class="form-control"
						placeholder="团队名称" v-model="seachTeam.teamName" />
				</div>
				<div class="form-group col-md-2">
					<label>团队编码:</label> <input type="text" class="form-control"
						placeholder="团队编码" v-model="seachTeam.teamCode" />
				</div>
				<div class="form-group col-md-2" style="height: 32px;">
					<label>创建日期:</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							id="dateTimeRange" @keyup.enter="query" value="" type="text"
							placeholder="创建日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
						</span> <input name="beginTime" id="beginTime" type="hidden"> <input
							name="endTime" id="endTime" type="hidden">
					</div>
				</div>
				<div class="form-group col-md-2">
					<label>所属城市:</label> <select class="form-control"
						v-model="seachTeam.cityCode">
						<option value=''>全部</option>
						<option v-for="itme in city" v-bind:value="itme.cityCode">
							{{itme.cityName}}</option>
					</select>
				</div>
			</div>

			<div class="row2"></div>

			<div class="grid-btn" style="margin-left: 19px;">
				<a v-if="hasPermission('hnsdteam:query')" class="btn btn-primary"
					@click="query">&nbsp;查询</a> <a
					v-if="hasPermission('hnsdteam:save')" class="btn btn-primary"
					@click="add"><i class="fa fa-plus"></i>&nbsp;新增</a> <a
					v-if="hasPermission('hnsdteam:update')" class="btn btn-primary"
					@click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a> <a
					v-if="hasPermission('hnsdteam:delete')" class="btn btn-primary"
					@click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a>
				<a class="btn btn-primary"  @click="templateShowI">&nbsp;导入</a>
			</div>
			<table id="jqGrid"></table>
			<div id="jqGridPager"></div>
		</div>

		<div v-show="!showList" class="panel panel-default">
			<div class="panel-heading">{{title}}</div>
			<form class="form-horizontal">

				<div class="form-group">
					<div class="col-sm-2 control-label">团体编码</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnsdTeam.teamCode" placeholder="团体编码" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">团体名称</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnsdTeam.teamName" placeholder="团体名称" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">所属城市:</div>
					<div class="col-sm-10">
						<select v-model="hnsdTeam.cityCode"
							style="width: 100%; height: 30px;">
							<option value="0">-请选择-</option>
							<option v-for="itme in city" v-bind:value="itme.cityCode">
								{{itme.cityName}}</option>
						</select>
					</div>
				</div>
				<div class="form-group" id="reload">
					<div class="col-sm-2 control-label"></div>
					<input type="button" class="btn btn-primary" @click="saveOrUpdate"
						   value="确定" /> &nbsp;&nbsp;<input type="button"
															class="btn btn-warning" @click="reload" value="返回" />
				</div>
			</form>
		</div>

		<div v-show="!templateShow" id="templateShow"
			 class="panel panel-default">
			<div class="panel-heading">{{title}}</div>
			<div class="form-horizontal" style="padding-top: 0px; width: 100%;">
				<form id="uploadImg" enctype="multipart/form-data">
					<div class="templateShow-Info">
						<p>下载模板：</p>
						<p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
					</div>
					<div style="margin-left: 125px;">
						<a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
					</div>
					<div class="templateShow-Info">
						<p>上传文件：</p>
						<p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
					</div>
					<div style="margin-left: 125px;">
						<a class="btn btn-primary" @click="importFile">&nbsp;开始导入</a> <input
							style="display: none;" name="uploadFile" id="uploadFile"
							type="file" @change="uploadFile" />
					</div>
					<div style="width: 100%; text-align: center;">
						<input type="button" class="btn btn-warning" @click="htmlReolad"
							   value="返回" />
					</div>
				</form>
			</div>
		</div>
	</div>

	<script src="../../js/modules/hnsd/hnsdteam.js"></script>
	<script src="../../js/components.js"></script>
</body>
</html>