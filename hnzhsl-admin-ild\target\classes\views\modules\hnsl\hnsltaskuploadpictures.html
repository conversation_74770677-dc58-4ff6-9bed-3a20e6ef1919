<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta
	content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
	name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet"
	href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet"
	href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css"
rel="stylesheet">
<link rel="stylesheet" href="../../css/task.css">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script
	src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script
	src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
<style type="text/css">

.templateShow-Info {
	float: left;
	width: 100%;
	font-size: 20px;
	padding: 10px 25px 0;
}

.templateShow-Info p {
	font-size: 20px;
	float: left;
	text-align: center;
	margin: 10px 0 10px;
	margin: 10px 0 10px;
}

.templateShow-Info p:nth-child(2) {
	color: #999 !important;
	font-size: 16px;
	line-height: 28px;
}
</style>
</head>
<body>
	<div id="rrapp" v-cloak>
		<div v-show="showList" id="isShowList">

			<div class="row">

               <div class="form-group col-md-2" style="height: 32px;">
					<label>上传时间:</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							id="dateTimeRange" @keyup.enter="query" value="" type="text"
							placeholder="日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
						</span> <input name="beginTime" id="beginTime" type="hidden"> <input
							name="endTime" id="endTime" type="hidden">
					</div>
				</div>

				<div class="form-group col-md-2">
					<label>截图类型:</label> <select class="form-control"
						style="height: 32px;" v-model="uploadType" >
						<option v-for="itme in status" v-bind:value="itme.statusId">
							{{itme.statusName}}</option>
					</select>
					<input type="hidden" id="userPhone"></input>
				</div>
				<a class="layui-btn layui-btn-small sxBtn"  @click="close()"  title="关闭">
				<i class="layui-icon">×</i></a>
		        </div>

			<div class="grid-btn" style="margin-left: 17Px;">
				<!--<a v-if="hasPermission('hnsluser:query')" class="btn btn-primary"
					style="padding: 0.5% 2.2%;"
					@click="query">&nbsp;查询</a>--> <a class="btn btn-primary"
					@click="query()">&nbsp;查询</a>
				 <a v-if="hasPermission('hnsluser:update')" class="btn btn-primary" @click="utTaskUploadPictures()">
					&nbsp;导出</a>
			</div>
              
			<table id="jqGrid"></table>
			<div id="jqGridPager"></div>
		</div>  
		
		<!-- 详细信息 -->
		<!-- <div v-show="!showList" class="panel panel-default">
			<div class="panel-heading" style="font-size: 23px">{{title}}</div>
			<form class="form-horizontal">

				<div class="form-group">
					<div class="col-sm-2 control-label"></div>
					<input type="button" v-show="updateButton"
						   style="margin-left: 95%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
						   class="btn btn-warning" @click="saveOrUpdate" value="修改" />
					&nbsp;&nbsp;<input type="button"
						style="margin-left: 95%;margin-top: -8%;padding: 1.4% 10%;font-size: 120%;"
						class="btn btn-warning" @click="reload" value="返回" />
				</div>
		</div> -->
	</div>

	<script src="../../js/modules/hnsl/hnsltaskuploadpictures.js"></script>
	<script src="../../js/components.js"></script>
</body>
</html>