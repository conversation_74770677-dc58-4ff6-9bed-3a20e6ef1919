<!DOCTYPE html>
<html>
<head>
<title>审核管理</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
	<link rel="stylesheet" href="../../css/popup.css">
	<link rel="stylesheet" href="../../css/bootstrap-datetimepicker.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>


<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

	<script src="../../plugins/Daterangepicker/js/common.js"></script>
	<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

	<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
	<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
	<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.min.js"></script>
<script src="../../js/common.js"></script>
	<style type="text/css">

		.templateShow-Info {
			float: left;
			width: 100%;
			font-size: 20px;
			padding: 10px 25px 0;
		}

		.templateShow-Info p {
			font-size: 20px;
			float: left;
			text-align: center;
			margin: 10px 0 10px;
			margin: 10px 0 10px;
		}

		.templateShow-Info p:nth-child(2) {
			color: #999 !important;
			font-size: 16px;
			line-height: 28px;
		}
	</style>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">
	
	<div class="row">
<!--				<div class="form-group col-md-2">-->
<!--					<label>地市</label>-->
<!--					<select class="form-control" @change="onChange()"-->
<!--							style="height: 32px;" v-model="queryCondition.cityCode">-->
<!--						<option value=''>全部</option>-->
<!--						<option v-for="itme in city" v-bind:value="itme.cityCode">-->
<!--							{{itme.cityName}}</option>-->
<!--					</select>-->
<!--				</div>-->
				 <div class="form-group col-md-2">
					<label>学校名称:</label>
					 <input type="text" class="form-control" placeholder="请输入学校名称"  v-model='queryCondition.schoolName'/>
				 </div>
				
				<div class="form-group col-md-2">
					<label>团队类型:</label>
					<select class="form-control" style="height: 32px;" v-model="queryCondition.teamLevel">
					    <option value='0'>全部</option>
						<option value="核心团队长">核心团队长</option>
						<option value="普通团队长">普通团队长</option>
						<option value="直销员">直销员</option>
					</select>
				</div>

				<div class="form-group col-md-2" style="height:40px">
				   <label>姓名/工号:</label>
		          <input type="text" class="form-control" placeholder="请输入姓名/手机号"  v-model='queryCondition.userValue'/>
		         </div>
				<div class="form-group col-md-2" style="height: 32px;">
					<label>提交日期:</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker"
							   id="dateTimeRange1" @keyup.enter="query" value="" type="text"
							   placeholder="日期"> <span class="input-group-addon">
											<i class="fa fa-calendar bigger-110"></i>
										</span>
					</div>
				</div>
				<div class="form-group col-md-2" style="height: 32px;">
 					<label>账期:</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							id="dateTimeRange" @keyup.enter="query" value="" type="text" 
							placeholder="日期"> <span class="input-group-addon"> 
							<i class="fa fa-calendar bigger-110"></i> 
 						</span>
						<input name="beginTime" id="beginTime" type="hidden">
						<input name="endTime" id="endTime" type="hidden">
 					</div>
				</div>


	</div>

		<div class="row2" style="overflow: hidden;">
			<div class="form-group col-md-4" style="height:40px">
				<label >个人积分:</label>
				<div class="form-group_flex">
					<input type="text"  class="form-control" placeholder="请输入积分区间段" v-model="queryCondition.userJF1"/>
					<span class="line">-</span>
					<input type="text"  class="form-control" placeholder="请输入积分区间段" v-model="queryCondition.userJF2"/>
				</div>
			</div>
			<div class="form-group col-md-4" style="height:40px">
				<label >团队积分:</label>
				<div class="form-group_flex">
					<input type="text"  class="form-control" placeholder="请输入积分区间段" v-model="queryCondition.teamJF1"/>
					<span class="line">-</span>
					<input type="text"  class="form-control" placeholder="请输入积分区间段" v-model="queryCondition.teamJF2"/>
				</div>
			</div>
			<div class="form-group col-md-2">
				<label>状态:</label>
				<select class="form-control" style="height: 32px;" v-model="queryCondition.status">
					<option value='-1'>全部</option>
					<option value="1">审核中</option>
					<option value="2">市级审核通过</option>
					<option value="3">市级驳回</option>
					<option value="4">省级审核通过</option>
					<option value="5">省级驳回</option>
				</select>
			</div>
		</div>
		<input style="display: none;" name="uploadFile" id="uploadFile1"
				type="file" @change="uploadFileImg" />
	  <div class="grid-btn" style="margin-left: 19px;">
		  <a v-if="hasPermission('hnslsalaryreview:query')" class="btn btn-primary" @click="query" style="margin-top: 1%">&nbsp;查询</a>
		  <a v-if="hasPermission('hnslsalaryreview:dsquery')" class="btn btn-primary"  @click="through('add')" style="margin-top: 1%;" >&nbsp;通过</a>
		  <a v-if="hasPermission('hnslsalaryreview:dsquery')" class="btn btn-primary"  @click="through(3)" style="margin-top: 1%;" >&nbsp;驳回</a>
		  <a v-if="hasPermission('hnslsalaryreview:sjquery')" class="btn btn-primary"  @click="throughAdmin(4)" style="margin-top: 1%;" >&nbsp;省级通过</a>
		  <a v-if="hasPermission('hnslsalaryreview:sjquery')" class="btn btn-primary"  @click="throughAdmin(5)" style="margin-top: 1%;" >&nbsp;省级驳回</a>
		  <a v-if="hasPermission('hnslsalaryreview:plsubmit')" class="btn btn-primary"  @click="templateShowI" style="margin-top: 1%;" >&nbsp;批量审批</a>
		  <a v-if="hasPermission('hnslsalaryreview:query')" class="btn btn-primary"  @click="outXZOrder" style="margin-top: 1%;" >&nbsp;导出</a>
	  </div>
	
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
	<!--模板下载-->
	<div v-show="!templateShow" id="templateShow"
		 class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<div class="form-horizontal" style="padding-top: 0px; width: 100%;">
			<form id="uploadImg" enctype="multipart/form-data">
				<div class="templateShow-Info">
					<p>下载模板：</p>
					<p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
				</div>
				<div style="margin-left: 125px;">
					<a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
				</div>
				<div class="templateShow-Info">
					<p>上传文件：</p>
					<p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
				</div>
				<div style="margin-left: 125px;">
					<a v-if="hasPermission('hnslsalaryreview:dsquery')"
					   class="btn btn-primary" @click="importUser">&nbsp;开始导入</a>
					<a v-if="hasPermission('hnslsalaryreview:sjquery')"
					   class="btn btn-primary" @click="importAdmin">&nbsp;开始导入</a>
					<input
						style="display: none;" name="uploadFile" id="uploadFile"
						type="file" @change="uploadFile" />
				</div>
				<div style="width: 100%; text-align: center;">
					<input type="button" class="btn btn-warning" @click="reload"
						   value="返回" />
				</div>
			</form>
		</div>
	</div>

	<div class="modal" id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog" style="padding: 70px">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true">×
					</button>
					<h4 class="modal-title" id="myModalLabel1" style="text-align: center">数据下载审批人选择</h4>
					<div style="display: flex">
						<input placeholder="请输入审批人姓名或手机号" v-model="searchText"
							   @input="onInput"/>
					</div>

					<table class="table table-bordered" style="width: 100%; text-align: center;margin-top: 10px">
						<tbody id="myTable">
						<tr>
							<td></td>
							<td></td>
						</tr>
						<tr>
							<td></td>
							<td></td>
						</tr>
						</tbody>
					</table>
					<div style="width: 100%;text-align: center;">
						<a href="#" id="prevPage" @click="prevPage">上一页</a>
						<span id="currentPage">1</span>
						<a href="#" id="nextPage" @click="nextPage">下一页</a>
					</div>

					<div style="text-align: right;margin-top: 20px;">
						<div class="col-sm-2 control-label"></div>
						<input type="button" class="btn btn-warning" @click="submitApprover" value="确定"/>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="modal" id="myModal2" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog" style="padding: 70px">
			<div class="modal-content">
				<div class="modal-header">
					<div style="display: flex">
						合伙人姓名：<input v-model="hnslUserInfoId.userName" disabled/>
					</div>
					<div style="display: flex;margin-top: 10px">
						合伙人手机号：<input v-model="hnslUserInfoId.userPhone" disabled/>
					</div>
<!--					<div style="display: flex;margin-top: 10px">-->
<!--						银行卡号：<input v-model="hnslUserInfoId.bankNumber" disabled/>-->
<!--					</div>-->
<!--					<div style="display: flex;margin-top: 10px">-->
<!--						提交人：<input v-model="reviewInfo.createdUser" disabled/>-->
<!--					</div>-->
					<div style="text-align: center;margin-top: 20px;">
						<div class="col-sm-2 control-label"></div>
						<input type="button" class="btn btn-warning" @click="closeDe" value="确定"/>
					</div>
				</div>
			</div>
		</div>
	</div>

</div>

<script src="../../js/modules/hnsl/hnslAuditManagement.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>