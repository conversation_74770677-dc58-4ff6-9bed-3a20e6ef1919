<!DOCTYPE html>
<html>
<head>
<title>海报配置</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">
		<div class="row">
			<div class="form-group col-md-2" style="height:40px">
				<label>海报名称</label>
				<input type="text" class="form-control" placeholder="海报名称" v-model="seachPoster.posterName"/>
			</div>
			<div class="row2">
				<div class="form-group col-md-2" style="height: 32px;">
					<label>创建日期:</label>
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							   id="dateTimeRange"  value="" type="text" placeholder="创建日期">
							<span class="input-group-addon">
								<i class="fa fa-calendar bigger-110"></i>
							</span>
						<input name="beginTime" id="beginTime" type="hidden" >
						<input name="endTime" id="endTime" type="hidden">
					</div>
				</div>
			<div class="form-group col-md-2">
				<label>状态</label>
				<select class="form-control" style="height: 32px;" v-model="seachPoster.status">
					<option value='' >全部</option>
					<option value="1">在架</option>
					<option value="0">下架</option>
				</select>
			</div>
		</div>
		<div class="grid-btn">
			<a class="btn btn-primary" @click="query"><i class="fa fa-trash-o"></i>&nbsp;查询</a>
			<a v-if="hasPermission('hnslpostertemplate:save')" class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
			<!--<a v-if="hasPermission('hnslpostertemplate:update')" class="btn btn-primary" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
			<a v-if="hasPermission('hnslpostertemplate:delete')" class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a>-->
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
	</div>
    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal" style="width: 700px;">
			<div class="form-group">
				<div class="col-sm-2 control-label">海报名称</div>
				<div class="col-sm-10">
					<input type="text" class="form-control" v-model="hnslPosterTemplate.posterName" placeholder="海报名称"/>
				</div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">海报图片地址</div>
			   	<div class="col-sm-10">
					<img :src="url+hnslPosterTemplate.posterUrl" class="fileUp" alt="" id="url" height="100" width="100" />
					<input type="file" @change="upload('1')" class="form-control" id="file" placeholder="点击上传海报图片"/>
			    </div>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label">海报有效时间</div>
				<div class="col-sm-10">
					<div class="input-group col-ms-2 ">
						<input class="form-control pull-left dateRange date-picker "
							   id="dateTime"  value="" type="text" placeholder="创建日期">
						<span class="input-group-addon">
								<i class="fa fa-calendar bigger-110"></i>
							</span>
						<input name="beginDate" v-model="hnslPosterTemplate.beginDate" type="hidden" >
						<input name="endDate" v-model="hnslPosterTemplate.endDate" type="hidden">
					</div>
				</div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">状态
			</div>
			   	<div class="col-sm-10">
					<select class="form-control" v-model="hnslPosterTemplate.status">
						<option value='0' >无效</option>
						<option value='1' >有效</option>
					</select>
			    </div>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div>
		</form>
	</div>

</div>

<script src="../../js/modules/hnsl/hnslpostertemplate.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>