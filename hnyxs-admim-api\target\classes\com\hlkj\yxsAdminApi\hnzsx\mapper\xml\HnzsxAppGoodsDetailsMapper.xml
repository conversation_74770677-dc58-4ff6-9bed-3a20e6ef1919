<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsDetailsMapper">
    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT
        details.*,
        goods.GIFT_PACKAGE_ID as giftPackageId,
        goods.GIFT_PACKAGE_NAME as giftPackageName
        FROM hnzsx_goods_details details
        INNER JOIN hnzsx_goods goods ON goods.id = details.goods_id
        <where>
            <if test="param.goodesUsedName !=null and param.goodesUsedName !=''">
                AND details.GOODES_USED_NAME LIKE concat(concat('%',#{param.goodesUsedName}),'%')
            </if>
            <if test="param.status !=null and param.status !=''">
                AND details.STATUS = #{param.status}
            </if>
            <if test="param.moduleCategorys !=null and param.moduleCategorys.size() > 0">
                AND details.MODULE_CATEGORY IN
                <foreach collection="param.moduleCategorys" index="index" item="typeId" open="(" close=")" separator=",">
                    #{typeId}
                </foreach>
            </if>
            <if test="param.moduleCategory !=null and param.moduleCategory != ''">
                AND details.MODULE_CATEGORY = #{param.moduleCategory}
            </if>
            <if test="param.cityCode !=null and param.cityCode !=''">
                AND details.CITY_CODE = #{param.cityCode}
            </if>
            <if test="param.packageCategory !=null and param.packageCategory !=''">
                AND details.PACKAGE_CATEGORY = #{param.packageCategory}
            </if>
            <if test="param.totalFree !=null and param.totalFree!=''">
                AND details.TOTAL_FREE = #{param.totalFree}
            </if>
            <if test="param.goodsType !=null and param.goodsType !=''">
                AND details.GOODS_TYPE = #{param.goodsType}
            </if>
            <if test="param.id!=null and param.id!=''">
                AND details.ID = #{param.id}
            </if>
            <if test="param.createdStartDate !=null and param.createdStartDate !=''">
                AND date_format(details.CREATED_DATE,'%Y-%m-%d') >= #{param.createdStartDate}
            </if>
            <if test="param.createdDate !=null and param.createdEndTime !=''">
                AND date_format(details.CREATED_DATE,'%Y-%m-%d') &lt;= #{param.createdEndTime}
            </if>
            <if test="param.updateStartTime!=null and param.updateStartTime!=''">
                AND date_format(details.UPDATE_DATE,'%Y-%m-%d') >= #{param.updateStartTime}
            </if>
            <if test="param.updateEndTime!=null and param.updateEndTime!=''">
                AND date_format(details.UPDATE_DATE,'%Y-%m-%d') &lt;= #{param.updateEndTime}
            </if>
            <if test="param.giftPackageId!=null and param.giftPackageId!=''">
                AND goods.GIFT_PACKAGE_ID = #{param.giftPackageId}
            </if>
            <if test="param.eqpackIdsList != null and param.eqpackIdsList!=''">
                AND EQPACK_IDS_LIST like concat(concat('%',#{param.eqpackIdsList}),'%')
            </if>
        </where>
        order by details.CREATED_DATE desc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsDetails">
        <include refid="selectSql">
        </include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsDetails">
        <include refid="selectSql">
        </include>
    </select>

    <!--  按工号查询人员是否存在  -->
    <select id="queryObjByStaffCode" resultType="Map">
        select ID,STAFF_CODE,USER_NAME from HNZSX_USER where STATUS=1
        <if test="staffCode != null and staffCode != ''">
            AND STAFF_CODE = #{staffCode}
        </if>
        <if test="cityCode != null and cityCode != 0">
            AND CITYCODE = #{cityCode}
        </if>
    </select>

    <!--  查询商品标签  -->
    <select id="queryGoodsTag" resultType="map">
        select *
        from hnzsx_goods_tag
        where P_ID = #{pId}
        order by SORT
    </select>

    <update id="createdSortCode">
        update hnzsx_GOODS_DETAILS d
        set d.SORT_CODE = d.SORT_CODE + 1
        where d.CITY_CODE = #{cityCode}
          and #{updateSortCode} &lt;= d.SORT_CODE
          and d.SORT_CODE is not null
    </update>

    <select id="querySortCodeAndCityCode" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsDetails">
        select details.*,
               goods.OFFER_PACK_TYPE      as g_offerPackType,
               goods.OFFER_PACK_TYPE_NAME as g_offerPackTypeName,
               goods.GIFT_PACKAGE_NAME    as g_giftPackageName,
               goods.GIFT_PACKAGE_ID      as g_giftPackageId
        from hnzsx_GOODS_DETAILS details
                 INNER JOIN hnzsx_GOODS goods
                            ON goods.id = details.goods_id
        WHERE 1 = 1
          AND details.SORT_CODE = #{updateSortCode}
          AND details.CITY_CODE = #{cityCode}
    </select>

    <insert id="saveDetaisCgr">
        INSERT INTO hnzsx_GOODS_DETAILS_CGR
        (GOODS_DETAILS_ID,
         GOODS_CGR_ID,
         CREATED_DATE)
        values (#{detailsId},
                #{cgrId},
                sysdate())
    </insert>

    <delete id="delDetaisCgr">
        delete
        from hnzsx_GOODS_DETAILS_CGR
        where GOODS_DETAILS_ID = #{id}
    </delete>

    <!-- 查询全部 -->
    <select id="exportExcel" resultType="com.hlkj.yxsAdminApi.hnzsx.excelEntity.HnzsxAppGoodsDetailsExportExcel">
        <include refid="selectSql">
        </include>
    </select>

    <!-- 查询流量加装销售品 -->
    <select id="queryGoodsCgrFlow" resultType="string">
        select SALE_CODE from hnzsx_goods_cgr_flow group by SALE_CODE
    </select>
</mapper>
