<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5OrderInfoFuncinstlistMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsxh5_order_info_funcinstlist a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.orderNo != null">
                AND a.ORDER_NO LIKE CONCAT('%', #{param.orderNo}, '%')
            </if>
            <if test="param.sceneInstId != null">
                AND a.SCENE_INST_ID LIKE CONCAT('%', #{param.sceneInstId}, '%')
            </if>
            <if test="param.custOrderId != null">
                AND a.CUST_ORDER_ID LIKE CONCAT('%', #{param.custOrderId}, '%')
            </if>
            <if test="param.templateId != null">
                AND a.TEMPLATE_ID LIKE CONCAT('%', #{param.templateId}, '%')
            </if>
            <if test="param.actionType != null">
                AND a.ACTION_TYPE LIKE CONCAT('%', #{param.actionType}, '%')
            </if>
            <if test="param.prodName != null">
                AND a.PROD_NAME LIKE CONCAT('%', #{param.prodName}, '%')
            </if>
            <if test="param.prodFuncType != null">
                AND a.PROD_FUNC_TYPE LIKE CONCAT('%', #{param.prodFuncType}, '%')
            </if>
            <if test="param.prodId != null">
                AND a.PROD_ID LIKE CONCAT('%', #{param.prodId}, '%')
            </if>
            <if test="param.prodInstId != null">
                AND a.PROD_INST_ID LIKE CONCAT('%', #{param.prodInstId}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5OrderInfoFuncinstlist">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5OrderInfoFuncinstlist">
        <include refid="selectSql"></include>
    </select>

</mapper>
