package com.hlkj.yxsAdminApi.hnzsxH5.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

/**
 * H5即时受理-商品类型配置表
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "Hnzsxh5GoodsType对象", description = "H5即时受理-商品类型配置表")
public class Hnzsxh5GoodsType implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "商品类型名称")
    @TableField("GOODS_TYPE")
    private String goodsType;

    @ApiModelProperty(value = "商品类型编码")
    @TableField("GOODS_TYPE_CODE")
    private String goodsTypeCode;

    @ApiModelProperty(value = "状态（1：有效 2：无效）")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdDate;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updatedDate;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "排序")
    @TableField("RANK")
    private Integer rank;

    @ApiModelProperty(value = "读证权限是否可配置（1：可配置 0：不可配置）")
    @TableField("CAN_CONFIG_READ_PERMISSION")
    private Integer canConfigReadPermission;

    @ApiModelProperty(value = "获取用户资料是否可配置（1：可配置 0：不可配置）")
    @TableField("CAN_CONFIG_USER_INFO")
    private Integer canConfigUserInfo;

    @ApiModelProperty(value = "模板是否可选是否可配置（1：可配置 0：不可配置）")
    @TableField("CAN_CONFIG_OPTIONAL")
    private Integer canConfigOptional;

    @ApiModelProperty(value = "一号一拍是否可配置（1：可配置 0：不可配置）")
    @TableField("CAN_CONFIG_ONE_BEAT")
    private Integer canConfigOneBeat;

    @ApiModelProperty(value = "一证十卡校验是否可配置（1：可配置 0：不可配置）")
    @TableField("CAN_CONFIG_ONE_PROVE_TEN_KA")
    private Integer canConfigOneProveTenKa;

    @ApiModelProperty(value = "URL类型是否可配置（1：可配置 0：不可配置）")
    @TableField("CAN_CONFIG_URL_TYPE")
    private Integer canConfigUrlType;

    @ApiModelProperty(value = "设备终端列表是否可配置（1：可配置 0：不可配置）")
    @TableField("CAN_CONFIG_TERMINAL_LIST")
    private Integer canConfigTerminalList;

    @ApiModelProperty(value = "终端回收是否可配置（1：可配置 0：不可配置）")
    @TableField("CAN_CONFIG_RECYCLING")
    private Integer canConfigRecycling;

    @ApiModelProperty(value = "是否需要活体（1：是 0：否）")
    @TableField("CAN_CONFIG_LIVENESS")
    private Integer canConfigLiveness;
} 