# 开发环境配置

# 数据源配置
spring:
  datasource:
    #    url: ********************************************************************************************************************
    #    username: root
    #    password: 123456
    #公司测试环境
#    url: ***************************************************************************************************************
#    username: hlkj_yxs
#    password: TJ56iuKqed2kjHL^T
    #    本地mysql
    url: ********************************************************************************************************************************************
    username: hnkj_yxs_app
    password: 'Aah7z9M8eGPPm!v9'
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
  redis:
   # password: OSV2FUz#koesGl%S
#    password:
    timeout: 6000ms
    #集群模式
    cluster:
      nodes: 127.0.0.1:6380,127.0.0.1:6381,127.0.0.1:6382,127.0.0.1:6383,127.0.0.1:6384,127.0.0.1:6385
    lettuce:
      pool:
        max-active: 100
        max-idle: 20
        max-wait: -1ms
        min-idle: 20
    jmx:
      default-domain: jsga_managerJmxPros

# 日志配置
logging:
  level:
    com.hlkj.yxsAdminApi: INFO
    com.baomidou.mybatisplus: WARN
    root: WARN

# 系统配置
config:
  hnzsxFileImgPath: C:\upload\file\ #掌上销图片上传地址