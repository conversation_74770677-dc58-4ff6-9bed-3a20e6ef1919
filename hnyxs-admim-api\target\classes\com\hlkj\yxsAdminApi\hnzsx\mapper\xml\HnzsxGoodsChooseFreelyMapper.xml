<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxGoodsChooseFreelyMapper">

    <resultMap id="resultMap1" type="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsChooseFreely">
        <id column="id" property="id"/>
        <result column="equity_name" property="equityName"/>
        <result column="bps_show_module" property="bpsShowModule"/>
        <result column="bps_show_module_name" property="bpsShowModuleName"/>
        <result column="equipment_list" property="equipmentList"/>
        <result column="sale_code" property="saleCode"/>
        <result column="sale_name" property="saleName"/>
        <result column="status" property="status"/>
        <result column="created_date" property="createdDate"/>
        <association property="saleList" column="id" select="getSaleInfo">
            <id column="id" property="id"/>
            <result column="sale_number" property="saleNumber"/>
            <result column="sale_name" property="saleName"/>
            <result column="action" property="action"/>
            <result column="product" property="product"/>
            <result column="sale_parameter" property="saleParameter"/>
            <result column="type_code" property="typeCode"/>
            <result column="type_remarks" property="typeRemarks"/>
            <result column="association_id" property="associationId"/>
            <result column="status" property="status"/>
            <result column="ACCEPTANCE_STEPS" property="acceptanceSteps"/>
        </association>
    </resultMap>


    <resultMap id="resultMap2" type="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsChooseFreely">
        <id column="id" property="id"/>
        <result column="equity_name" property="equityName"/>
        <result column="bps_show_module" property="bpsShowModule"/>
        <result column="bps_show_module_name" property="bpsShowModuleName"/>
        <result column="equipment_list" property="equipmentList"/>
        <result column="sale_code" property="saleCode"/>
        <result column="sale_name" property="saleName"/>
        <result column="status" property="status"/>
        <result column="created_date" property="createdDate"/>
        <association property="saleList" column="id" select="bySaleInfoId">
            <id column="id" property="id"/>
            <result column="sale_number" property="saleNumber"/>
            <result column="sale_name" property="saleName"/>
            <result column="action" property="action"/>
            <result column="product" property="product"/>
            <result column="sale_parameter" property="saleParameter"/>
            <result column="type_code" property="typeCode"/>
            <result column="type_remarks" property="typeRemarks"/>
            <result column="association_id" property="associationId"/>
            <result column="status" property="status"/>
            <result column="ACCEPTANCE_STEPS" property="acceptanceSteps"/>
        </association>
    </resultMap>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_choose_freely a
        <where>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.equityName != null">
                AND a.equity_name LIKE CONCAT('%', #{param.equityName}, '%')
            </if>
            <if test="param.bpsShowModule != null">
                AND a.bps_show_module = #{param.bpsShowModule}
            </if>
            <if test="param.bpsShowModuleName != null">
                AND a.bps_show_module_name LIKE CONCAT('%', #{param.bpsShowModuleName}, '%')
            </if>
            <if test="param.equipmentList != null">
                AND a.equipment_list LIKE CONCAT('%', #{param.equipmentList}, '%')
            </if>
            <if test="param.saleCode != null">
                AND a.sale_code LIKE CONCAT('%', #{param.saleCode}, '%')
            </if>
            <if test="param.saleName != null">
                AND a.sale_name LIKE CONCAT('%', #{param.saleName}, '%')
            </if>
            <if test="param.status != null">
                AND a.status = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.created_date LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsChooseFreely">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsChooseFreely">
        <include refid="selectSql"></include>
    </select>

    <!--  查询随意选合约  -->
    <select id="getChooseFreelySale" resultMap="resultMap1">
        <include refid="selectSql"></include>
    </select>

    <!--  查询随意选合约关联销售品信息  -->
    <select id="getSaleInfo" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSaleInfo">
        select * from hnzsx_sale_info
        where type_code = 3 and association_id = #{id} and status = 1
    </select>

    <!--  查询商品关联随意选信息  -->
    <select id="queryGoodsChooseFreelySale" resultMap="resultMap2">
        select hgcf.*,hsi.* from hnzsx_goods_choose_freely hgcf
         inner join hnzsx_goods_choose_freely_rel hgcfr on hgcf.id = hgcfr.choose_freely_id
         inner join hnzsx_sale_info hsi on hsi.id = hgcfr.sale_id
        where hgcfr.goods_details_id = #{goodsId} and hgcfr.choose_freely_id = #{chooseFreelyId}
    </select>

    <select id="bySaleInfoId" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSaleInfo">
        select * from hnzsx_sale_info where id = #{id}
    </select>

</mapper>
