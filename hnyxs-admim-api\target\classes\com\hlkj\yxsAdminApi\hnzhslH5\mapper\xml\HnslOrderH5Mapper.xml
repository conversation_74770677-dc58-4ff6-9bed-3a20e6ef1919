<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslOrderH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        select tt.*,
        t1.user_name,t1.status_sf,t2.goods_name,t3.school_name,t1.sales_code,t1.CHANNEL_TYPE
        from
        (
        select a.citycode,a.created_date,a.status,a.customer_name,a.safl_type,a.order_status
        ,a.customer_phone,a.order_id,a.bps_order_id,a.goods_number,a.id,a.crm_order_id,a.USER_SOLE_ID,
        a.customer_contact_phone,a.order_price,a.iccid,a.SIGNATURE_STATUS,a.SCHOOL_CHANNEL_TYPE,
        a.CHANNEL_NAME, a.CHANNEL_CODE,a.BPS_FORMAL_SYNCHRO, a.CRM_SURE_SYNCHRO, a.ORDER_ACTIVATE_DATE
        ,a.USER_HHR_NAME
        from hnsl_h5_order a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.orderId != null and param.orderId != ''">
                AND a.ORDER_ID LIKE CONCAT('%', #{param.orderId}, '%')
            </if>
            <if test="param.goodsNumber != null">
                AND a.GOODS_NUMBER LIKE CONCAT('%', #{param.goodsNumber}, '%')
            </if>
            <if test="param.customerName != null and param.customerName != ''">
                AND (a.CUSTOMER_NAME LIKE CONCAT('%', #{param.customerName}, '%')
                or a.CUSTOMER_CARD = #{param.customerName}
                or a.CUSTOMER_PHONE = #{param.customerName} )
            </if>
            <if test="param.customerContactPhone != null">
                AND a.CUSTOMER_CONTACT_PHONE LIKE CONCAT('%', #{param.customerContactPhone}, '%')
            </if>
            <if test="param.citycode != null and param.citycode != ''">
                AND a.CITYCODE LIKE CONCAT('%', #{param.citycode}, '%')
            </if>
            <if test="param.payChannel != null">
                AND a.PAY_CHANNEL LIKE CONCAT('%', #{param.payChannel}, '%')
            </if>
            <if test="param.businessOrder != null">
                AND a.BUSINESS_ORDER LIKE CONCAT('%', #{param.businessOrder}, '%')
            </if>
            <if test="param.orderStatus != null and param.orderStatus != ''">
                AND a.ORDER_STATUS = #{param.orderStatus}
            </if>
            <if test="param.orderSubmitDate != null">
                AND a.ORDER_SUBMIT_DATE LIKE CONCAT('%', #{param.orderSubmitDate}, '%')
            </if>
            <if test="param.orderActivateDate != null">
                AND a.ORDER_ACTIVATE_DATE LIKE CONCAT('%', #{param.orderActivateDate}, '%')
            </if>
            <if test="param.crmOrderId != null and param.crmOrderId != ''">
                AND a.CRM_ORDER_ID LIKE CONCAT('%', #{param.crmOrderId}, '%')
            </if>
            <if test="param.bpsOrderId != null and param.bpsOrderId != ''">
                AND a.BPS_ORDER_ID LIKE CONCAT('%', #{param.bpsOrderId}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.beginTime != null and param.beginTime.trim() != ''">
                AND a.CREATED_DATE between
                #{param.beginTime} and
                #{param.endTime}
            </if>
            <if test="param.hhid != null">
                AND a.HHID LIKE CONCAT('%', #{param.hhid}, '%')
            </if>
            <if test="param.saflType != null">
                AND a.SAFL_TYPE = #{param.saflType}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.schoolChannelType!='' and param.schoolChannelType !=null">
                AND a.SCHOOL_CHANNEL_TYPE =#{param.schoolChannelType}
            </if>
        </where>
        ) tt
        left join hnsl_h5_user t1 on tt.user_sole_id=t1.id
        left join hnsl_h5_goods t2 on tt.goods_number=t2.goods_number
        left join hnsl_h5_school t3 on tt.CHANNEL_CODE=t3.school_code
        <where>
            <if test="param.schoolName!=null and param.schoolName!=''">
                AND t3.SCHOOL_NAME like concat(concat('%',#{param.schoolName}),'%')
            </if>
            <if test="param.goodsName!=null and param.goodsName!=''">
                AND t2.goods_name like concat(concat('%',#{param.goodsName}),'%')
            </if>
            <if test="param.statusSf !='-1' and param.statusSf !=null and param.statusSf!=''">
                AND t1.STATUS_SF =#{param.statusSf}
            </if>
        </where>
        order by tt.created_date desc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Order">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Order">
        <include refid="selectSql"></include>
    </select>


    <select id="queryObject" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Order">
        select t.*,a.user_name,b.goods_name,c.name,c.school_code,c.building_id
        ,c.room_id,c.enroltime,bb.school_name,dd.building_name,ff.room_name from HNSL_h5_ORDER t left join HNSL_h5_USER
        a on
        t.HHID = a.user_phone left join HNSL_h5_GOODS b ON t.goods_number = b.goods_number
        left join HNSL_h5_SCHOOL bb ON t.hhid_school = bb.school_code left join
        HNSL_h5_BUILDING dd on dd.building_id = C.building_id left join HNSL_h5_ROOM ff ON ff.room_number = C.room_id
        <where>
            ${ew.sqlSegment}
        </where>
        LIMIT 1
    </select>

    <update id="updateSynchroStatus" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Order">
        update HNSL_h5_ORDER
        <set>
            <if test="bpsFormalSynchro != null and bpsFormalSynchro != ''">
                BPS_FORMAL_SYNCHRO = #{bpsFormalSynchro},
            </if>
            <if test="bpsRealnameSynchro != null and bpsRealnameSynchro != ''">
                BPS_REALNAME_SYNCHRO = #{bpsRealnameSynchro},
            </if>
            <if test="crmSureSynchro != null and crmSureSynchro != ''">
                CRM_SURE_SYNCHRO = #{crmSureSynchro},
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                ORDER_STATUS = #{orderStatus},
            </if>
            <if test="bpsOrderId != null and bpsOrderId != ''">
                BPS_ORDER_ID = #{bpsOrderId},
            </if>
            <if test="updatedDate != null">UPDATED_DATE = sysdate(),</if>
            <if test="bandOrderId != null and bandOrderId != ''">BAND_ORDER_ID = #{bandOrderId},</if>
            <if test="orderReviewedDate != null">ORDER_REVIEWED_DATE = #{orderReviewedDate},</if>
            <if test="orderActivateDate != null">ORDER_ACTIVATE_DATE = #{orderActivateDate},</if>
            <if test="crmOrderId != null">CRM_ORDER_ID = #{crmOrderId},</if>
            <if test="grade != null">GRADE = #{grade},</if>
            <if test="bpsActivateNotification != 0 and bpsActivateNotification != null">BPS_ACTIVATE_NOTIFICATION =
                #{bpsActivateNotification},
            </if>
            <if test="bpsActivateRemark != null">BPS_ACTIVATE_REMARK = #{bpsActivateRemark},</if>
            <if test="bpsActivateDate != null">BPS_ACTIVATE_DATE = sysdate(),</if>
            <if test="bpsFormalActivate != 0 and bpsFormalActivate != null">BPS_FORMAL_ACTIVATE =
                #{bpsFormalActivate},
            </if>
            <if test="bpsRealnameActivate != 0 and bpsRealnameActivate != null">BPS_REALNAME_ACTIVATE =
                #{bpsRealnameActivate},
            </if>
            <if test="identityVideoImage1 != null">IDENTITY_VIDEO_IMAGE1 = #{identityVideoImage1},</if>
            <if test="identityVideoImage2 != null">IDENTITY_VIDEO_IMAGE2 = #{identityVideoImage2},</if>
            <if test="crmOrderPrice != null">CRM_ORDER_PRICE = #{crmOrderPrice},</if>
            <if test="crmAskId != null">CRM_ASK_ID = #{crmAskId}</if>
        </set>
        where ORDER_ID = #{orderId}
    </update>

    <select id="outputQueryList" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Order">
        SELECT tt.* FROM (
        select a.*,
        t1.user_name,t1.status_sf,t2.goods_name,t3.school_name,t1.sales_code,t1.CHANNEL_TYPE
        ,t1.HNSL_CHANNEL,hu1.user_name as sj_user_name,hu1.sales_code as sj_sales_code,t1.STATUS as state,
        hu2.user_name as cj_user_name, hu2.sales_code as cj_sales_code
        from
        (select tt.citycode,tt.created_date,tt.status,tt.customer_name,tt.safl_type,tt.order_status
        ,tt.customer_phone,tt.order_id,tt.bps_order_id,tt.hhid,tt.hhid_school ,tt.goods_number,tt.id,tt.crm_order_id,
        tt.customer_contact_phone, tt.order_price,tt.iccid,tt.SIGNATURE_STATUS,tt.SCHOOL_CHANNEL_TYPE, tt.USER_HHR_NAME,
        tt.USER_HHR_PHONE,tt.MANAGE_HHR_NAME, tt.MANAGE_HHR_PHONE, tt.MANAGERS_HHR_NAME,
        tt.MANAGERS_HHR_PHONE,tt.USER_SOLE_ID, tt.CHANNEL_CODE
        from hnsl_h5_order tt
        <where>
            <if test="cityCode !=null and cityCode !='' and cityCode != '-1'">
                tt.CITYCODE=#{cityCode}
            </if>
            <if test="beginTime != null and beginTime.trim() != ''">
                AND tt.CREATED_DATE between
                #{beginTime} and
                #{endTime}
            </if>
            <if test="status != '-1' and status !=null and status!= ''">
                AND tt.STATUS = #{status}
            </if>
            <if test="orderType!='-1' and orderType !=null and orderType!=''">
                AND tt.SAFL_TYPE =#{orderType}
            </if>
            <if test="orderStatus !='-1' and orderStatus!='' and orderStatus!=null">
                AND tt.ORDER_STATUS=#{orderStatus}
            </if>
            <if test="customerPhone!='' and customerPhone !=null">
                AND tt.CUSTOMER_PHONE =#{customerPhone}
            </if>
            <if test="orderId !='' and orderId != null">
                AND tt.ORDER_ID =#{orderId}
            </if>
            <if test="bpsOrderId !='' and bpsOrderId != null">
                AND tt.BPS_ORDER_ID =#{bpsOrderId}
            </if>
            <if test="crmOrderId !='' and crmOrderId != null">
                AND tt.CRM_ORDER_ID =#{crmOrderId}
            </if>
            <if test="customerName !='' and customerName !=null">
                AND tt.customer_name =#{customerName}
            </if>
            <if test="customerCard!='' and customerCard !=null">
                AND tt.customer_card =#{customerCard}
            </if>
            <if test="schoolChannelType!='' and schoolChannelType !=null">
                AND tt.SCHOOL_CHANNEL_TYPE =#{schoolChannelType}
            </if>
            <if test="hhid!='' and hhid !=null">
                AND tt.HHID =#{hhid}
            </if>
            <if test="schoolList !=null and schoolList.size() >0">
                AND tt.hhid_school in
                <foreach collection="schoolList" item="schoolCode" open="(" separator="," close=")">
                    #{schoolCode}
                </foreach>
            </if>
        </where>
        ) a
        left join hnsl_h5_user t1 on a.USER_SOLE_ID = t1.id
        left join hnsl_h5_user hu1 on t1.STATUS_SUPERIOR=hu1.user_phone
        left join hnsl_h5_user hu2 on hu1.STATUS_SUPERIOR=hu2.user_phone
        left join hnsl_h5_goods t2 on a.goods_number=t2.goods_number
        left join hnsl_h5_school t3 on a.CHANNEL_CODE=t3.school_code
        order by a.created_date desc
        ) tt
    </select>

    <update id="disableOrder" parameterType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Order">
        update hnsl_h5_order
        <set>
            <if test="status != null">STATUS = #{status}</if>
        </set>
        where ID = #{id}
    </update>
</mapper>
