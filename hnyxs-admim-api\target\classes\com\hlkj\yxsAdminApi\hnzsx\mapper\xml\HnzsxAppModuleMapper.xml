<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppModuleMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_module a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.moduleTypeCode != null">
                AND a.MODULE_TYPE_CODE = #{param.moduleTypeCode}
            </if>
            <if test="param.moduleTypeName != null">
                AND a.MODULE_TYPE_NAME LIKE CONCAT('%', #{param.moduleTypeName}, '%')
            </if>
            <if test="param.moduleName != null">
                AND a.MODULE_NAME LIKE CONCAT('%', #{param.moduleName}, '%')
            </if>
            <if test="param.moduleCode != null">
                AND a.MODULE_CODE LIKE CONCAT('%', #{param.moduleCode}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS LIKE CONCAT('%', #{param.status}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.url != null">
                AND a.URL LIKE CONCAT('%', #{param.url}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxModule">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxModule">
        <include refid="selectSql"></include>
    </select>

</mapper>
