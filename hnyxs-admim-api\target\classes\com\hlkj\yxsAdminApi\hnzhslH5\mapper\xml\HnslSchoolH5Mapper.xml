<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslSchoolH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_h5_school a
        <where>
            <if test="param.schoolName != null and param.schoolName != ''">
                AND a.SCHOOL_NAME LIKE CONCAT('%', #{param.schoolName}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.schoolCity != null and param.schoolCity != ''">
                AND a.SCHOOL_CITY LIKE CONCAT('%', #{param.schoolCity}, '%')
            </if>
            <if test="param.schoolCode != null and param.schoolCode != ''">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.schoolGradeType != null">
                AND a.SCHOOL_GRADE_TYPE = #{param.schoolGradeType}
            </if>
        </where>
        order by a.id desc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5School">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5School">
        <include refid="selectSql"></include>
    </select>


    <select id="queryList" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5School">
        SELECT tt.*
        FROM (
        select * from HNSL_h5_SCHOOL 	WHERE 1=1
        <if test="schoolName !=null and schoolName!=''">
            AND SCHOOL_NAME like concat(concat('%',#{schoolName}),'%')
        </if>
        <if test="status !=null and status !=''">
            AND STATUS =#{status}
        </if>
        <if test="schoolType !=null and schoolType!=''">
            AND SCHOOL_TYPE =#{schoolType}
        </if>
        <if test="schoolCity !=null and schoolCity!=''">
            AND SCHOOL_CITY =#{schoolCity}
        </if>
        <if test="schoolCode !=null and schoolCode!=''">
            AND SCHOOL_CODE =#{schoolCode}
        </if>
        <if test="hnslType ==2 ">
            AND SCHOOL_GRADE_TYPE in (1,2,5)
        </if>
        <if test="hnslType ==3 or hnslType ==4 ">
            AND SCHOOL_GRADE_TYPE=#{hnslType}
        </if>
        order by ID desc
        ) tt

    </select>

    <select id="querySchooleByUserPhoneOrSchoolCode" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5School">
        select * from hnsl_h5_school where school_code in(
        select school_code from HNSL_h5_USER_SCHOOL t where 1=1
        <if test="userPhone!=null and userPhone!=''">
            AND t.USER_PHONE=#{userPhone}
        </if>
        <if test="schoolCode!=null and schoolCode!=''">
            AND t.SCHOOL_CODE=#{schoolCode}
        </if>
        GROUP BY t.school_code
        )
    </select>

    <select id="queryObject" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5School">
        select * from hnsl_h5_school where 1=1
        <if test="id !=null and id !=''">
            AND ID = #{id}
        </if>
        <if test="schoolCode !=null and schoolCode!=''">
            AND SCHOOL_CODE=#{schoolCode}
        </if>
    </select>

</mapper>
