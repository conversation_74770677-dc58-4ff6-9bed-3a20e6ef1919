<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hlkj.yxsAdminApi.common.system.mapper.UserRoleMapper">

    <insert id="insertBatch">
        INSERT INTO hnyxs_sys_user_role(user_id, role_id) VALUES
        <foreach collection="roleIds" item="roleId" separator=",">
            (#{userId}, #{roleId})
        </foreach>
    </insert>

    <select id="selectByUserId" resultType="com.hlkj.yxsAdminApi.common.system.entity.Role">
        SELECT *
        FROM hnyxs_sys_role
        WHERE role_id IN (
            SELECT role_id
            FROM hnyxs_sys_user_role
            WHERE user_id = #{userId}
        )
          AND deleted = 0
    </select>

    <select id="selectByUserIds" resultType="com.hlkj.yxsAdminApi.common.system.entity.Role">
        SELECT a.user_id, b.*
        FROM hnyxs_sys_user_role a
        LEFT JOIN hnyxs_sys_role b ON a.role_id = b.role_id
        WHERE a.user_id IN
        <foreach collection="userIds" open="(" close=")" separator="," item="userId">
            #{userId}
        </foreach>
        AND b.deleted = 0
    </select>

</mapper>
