<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnChannelViewStoreMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hn_channel_view_store a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.gradation != null">
                AND a.GRADATION LIKE CONCAT('%', #{param.gradation}, '%')
            </if>
            <if test="param.gradationCode != null">
                AND a.GRADATION_CODE LIKE CONCAT('%', #{param.gradationCode}, '%')
            </if>
            <if test="param.storeOrSaleBoxName != null">
                AND a.STORE_OR_SALE_BOX_NAME LIKE CONCAT('%', #{param.storeOrSaleBoxName}, '%')
            </if>
            <if test="param.lanId != null">
                AND a.LAN_ID LIKE CONCAT('%', #{param.lanId}, '%')
            </if>
            <if test="param.regionId != null">
                AND a.REGION_ID LIKE CONCAT('%', #{param.regionId}, '%')
            </if>
            <if test="param.aLanName != null">
                AND a.A_LAN_NAME LIKE CONCAT('%', #{param.aLanName}, '%')
            </if>
            <if test="param.aCityCountryName != null">
                AND a.A_CITY_COUNTRY_NAME LIKE CONCAT('%', #{param.aCityCountryName}, '%')
            </if>
            <if test="param.aSalePointCode != null">
                AND a.A_SALE_POINT_CODE LIKE CONCAT('%', #{param.aSalePointCode}, '%')
            </if>
            <if test="param.aSalePointName != null">
                AND a.A_SALE_POINT_NAME LIKE CONCAT('%', #{param.aSalePointName}, '%')
            </if>
            <if test="param.aSaleBoxCode != null">
                AND a.A_SALE_BOX_CODE LIKE CONCAT('%', #{param.aSaleBoxCode}, '%')
            </if>
            <if test="param.aSaleBoxName != null">
                AND a.A_SALE_BOX_NAME LIKE CONCAT('%', #{param.aSaleBoxName}, '%')
            </if>
            <if test="param.chnBusiCode != null">
                AND a.CHN_BUSI_CODE LIKE CONCAT('%', #{param.chnBusiCode}, '%')
            </if>
            <if test="param.chnBusiName != null">
                AND a.CHN_BUSI_NAME LIKE CONCAT('%', #{param.chnBusiName}, '%')
            </if>
            <if test="param.salePointCountryOperCode != null">
                AND a.SALE_POINT_COUNTRY_OPER_CODE LIKE CONCAT('%', #{param.salePointCountryOperCode}, '%')
            </if>
            <if test="param.salePointCountryOperName != null">
                AND a.SALE_POINT_COUNTRY_OPER_NAME LIKE CONCAT('%', #{param.salePointCountryOperName}, '%')
            </if>
            <if test="param.salePointLanOperCode != null">
                AND a.SALE_POINT_LAN_OPER_CODE LIKE CONCAT('%', #{param.salePointLanOperCode}, '%')
            </if>
            <if test="param.salePointLanOperName != null">
                AND a.SALE_POINT_LAN_OPER_NAME LIKE CONCAT('%', #{param.salePointLanOperName}, '%')
            </if>
            <if test="param.salePointProvOperCode != null">
                AND a.SALE_POINT_PROV_OPER_CODE LIKE CONCAT('%', #{param.salePointProvOperCode}, '%')
            </if>
            <if test="param.salePointProvOperName != null">
                AND a.SALE_POINT_PROV_OPER_NAME LIKE CONCAT('%', #{param.salePointProvOperName}, '%')
            </if>
            <if test="param.salePointNationOperCode != null">
                AND a.SALE_POINT_NATION_OPER_CODE LIKE CONCAT('%', #{param.salePointNationOperCode}, '%')
            </if>
            <if test="param.salePointNationOperName != null">
                AND a.SALE_POINT_NATION_OPER_NAME LIKE CONCAT('%', #{param.salePointNationOperName}, '%')
            </if>
            <if test="param.aUnionOrgCode != null">
                AND a.A_UNION_ORG_CODE LIKE CONCAT('%', #{param.aUnionOrgCode}, '%')
            </if>
            <if test="param.a30000103Name != null">
                AND a.A30000103_NAME LIKE CONCAT('%', #{param.a30000103Name}, '%')
            </if>
            <if test="param.saleCategory != null">
                AND a.SALE_CATEGORY LIKE CONCAT('%', #{param.saleCategory}, '%')
            </if>
            <if test="param.provSupChannelTypeName != null">
                AND a.PROV_SUP_CHANNEL_TYPE_NAME LIKE CONCAT('%', #{param.provSupChannelTypeName}, '%')
            </if>
            <if test="param.provChannelTypeName != null">
                AND a.PROV_CHANNEL_TYPE_NAME LIKE CONCAT('%', #{param.provChannelTypeName}, '%')
            </if>
            <if test="param.provChannelSubtypeName != null">
                AND a.PROV_CHANNEL_SUBTYPE_NAME LIKE CONCAT('%', #{param.provChannelSubtypeName}, '%')
            </if>
            <if test="param.channelFirstClassifyName != null">
                AND a.CHANNEL_FIRST_CLASSIFY_NAME LIKE CONCAT('%', #{param.channelFirstClassifyName}, '%')
            </if>
            <if test="param.channelSecondClassifyName != null">
                AND a.CHANNEL_SECOND_CLASSIFY_NAME LIKE CONCAT('%', #{param.channelSecondClassifyName}, '%')
            </if>
            <if test="param.channelThirdClassifyName != null">
                AND a.CHANNEL_THIRD_CLASSIFY_NAME LIKE CONCAT('%', #{param.channelThirdClassifyName}, '%')
            </if>
            <if test="param.channelLevelCdName != null">
                AND a.CHANNEL_LEVEL_CD_NAME LIKE CONCAT('%', #{param.channelLevelCdName}, '%')
            </if>
            <if test="param.topOperatorsNbr != null">
                AND a.TOP_OPERATORS_NBR LIKE CONCAT('%', #{param.topOperatorsNbr}, '%')
            </if>
            <if test="param.topOperatorsName != null">
                AND a.TOP_OPERATORS_NAME LIKE CONCAT('%', #{param.topOperatorsName}, '%')
            </if>
            <if test="param.boxAOperatorsCode != null">
                AND a.BOX_A_OPERATORS_CODE LIKE CONCAT('%', #{param.boxAOperatorsCode}, '%')
            </if>
            <if test="param.boxAOperatorsName != null">
                AND a.BOX_A_OPERATORS_NAME LIKE CONCAT('%', #{param.boxAOperatorsName}, '%')
            </if>
            <if test="param.salesAddress != null">
                AND a.SALES_ADDRESS LIKE CONCAT('%', #{param.salesAddress}, '%')
            </if>
            <if test="param.lng != null">
                AND a.LNG LIKE CONCAT('%', #{param.lng}, '%')
            </if>
            <if test="param.lat != null">
                AND a.LAT LIKE CONCAT('%', #{param.lat}, '%')
            </if>
            <if test="param.channelId != null">
                AND a.CHANNEL_ID LIKE CONCAT('%', #{param.channelId}, '%')
            </if>
            <if test="param.branchId != null">
                AND a.BRANCH_ID LIKE CONCAT('%', #{param.branchId}, '%')
            </if>
            <if test="param.branchName != null">
                AND a.BRANCH_NAME LIKE CONCAT('%', #{param.branchName}, '%')
            </if>
            <if test="param.branchesId != null">
                AND a.BRANCHES_ID LIKE CONCAT('%', #{param.branchesId}, '%')
            </if>
            <if test="param.branchesName != null">
                AND a.BRANCHES_NAME LIKE CONCAT('%', #{param.branchesName}, '%')
            </if>
            <if test="param.claimantName != null">
                AND a.CLAIMANT_NAME LIKE CONCAT('%', #{param.claimantName}, '%')
            </if>
            <if test="param.claimantPhone != null">
                AND a.CLAIMANT_PHONE LIKE CONCAT('%', #{param.claimantPhone}, '%')
            </if>
            <if test="param.createTime != null">
                AND a.CREATE_TIME LIKE CONCAT('%', #{param.createTime}, '%')
            </if>
            <if test="param.updateTime != null">
                AND a.UPDATE_TIME LIKE CONCAT('%', #{param.updateTime}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnChannelViewStore">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnChannelViewStore">
        <include refid="selectSql"></include>
    </select>

</mapper>
