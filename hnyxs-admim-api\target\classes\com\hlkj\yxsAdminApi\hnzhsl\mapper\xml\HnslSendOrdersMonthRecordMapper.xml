<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslSendOrdersMonthRecordMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_send_orders_month_record a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.activityLogo != null">
                AND a.ACTIVITY_LOGO LIKE CONCAT('%', #{param.activityLogo}, '%')
            </if>
            <if test="param.activityName != null">
                AND a.ACTIVITY_NAME LIKE CONCAT('%', #{param.activityName}, '%')
            </if>
            <if test="param.month != null">
                AND a.MONTH LIKE CONCAT('%', #{param.month}, '%')
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.statistic1 != null">
                AND a.STATISTIC1 = #{param.statistic1}
            </if>
            <if test="param.statistic2 != null">
                AND a.STATISTIC2 = #{param.statistic2}
            </if>
            <if test="param.statistic3 != null">
                AND a.STATISTIC3 = #{param.statistic3}
            </if>
            <if test="param.statistic4 != null">
                AND a.STATISTIC4 = #{param.statistic4}
            </if>
            <if test="param.statistic5 != null">
                AND a.STATISTIC5 = #{param.statistic5}
            </if>
            <if test="param.statistic6 != null">
                AND a.STATISTIC6 = #{param.statistic6}
            </if>
            <if test="param.statistic7 != null">
                AND a.STATISTIC7 = #{param.statistic7}
            </if>
            <if test="param.chance1 != null">
                AND a.CHANCE1 LIKE CONCAT('%', #{param.chance1}, '%')
            </if>
            <if test="param.chance2 != null">
                AND a.CHANCE2 LIKE CONCAT('%', #{param.chance2}, '%')
            </if>
            <if test="param.chance3 != null">
                AND a.CHANCE3 LIKE CONCAT('%', #{param.chance3}, '%')
            </if>
            <if test="param.chance4 != null">
                AND a.CHANCE4 LIKE CONCAT('%', #{param.chance4}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrdersMonthRecord">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrdersMonthRecord">
        <include refid="selectSql"></include>
    </select>

</mapper>
