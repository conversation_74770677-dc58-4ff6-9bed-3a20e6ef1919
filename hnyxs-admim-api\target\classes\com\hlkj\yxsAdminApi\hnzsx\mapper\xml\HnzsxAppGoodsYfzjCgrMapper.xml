<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsYfzjCgrMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_yfzj_cgr a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.contractTypeCode != null">
                AND a.CONTRACT_TYPE_CODE LIKE CONCAT('%', #{param.contractTypeCode}, '%')
            </if>
            <if test="param.contractTypeName != null">
                AND a.CONTRACT_TYPE_NAME LIKE CONCAT('%', #{param.contractTypeName}, '%')
            </if>
            <if test="param.monthlyPaymentGrade != null">
                AND a.MONTHLY_PAYMENT_GRADE LIKE CONCAT('%', #{param.monthlyPaymentGrade}, '%')
            </if>
            <if test="param.instalmentOfferId != null">
                AND a.INSTALMENT_OFFER_ID LIKE CONCAT('%', #{param.instalmentOfferId}, '%')
            </if>
            <if test="param.installmentTypeName != null">
                AND a.INSTALLMENT_TYPE_NAME LIKE CONCAT('%', #{param.installmentTypeName}, '%')
            </if>
            <if test="param.installmentTypeCode != null">
                AND a.INSTALLMENT_TYPE_CODE LIKE CONCAT('%', #{param.installmentTypeCode}, '%')
            </if>
            <if test="param.createDate != null">
                AND a.CREATE_DATE LIKE CONCAT('%', #{param.createDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.UPDATE_DATE LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
            <if test="param.instalmentOfferName != null">
                AND a.INSTALMENT_OFFER_NAME LIKE CONCAT('%', #{param.instalmentOfferName}, '%')
            </if>
            <if test="param.monthlyPaymentGradeName != null">
                AND a.MONTHLY_PAYMENT_GRADE_NAME LIKE CONCAT('%', #{param.monthlyPaymentGradeName}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsYfzjCgr">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsYfzjCgr">
        <include refid="selectSql"></include>
    </select>

</mapper>
