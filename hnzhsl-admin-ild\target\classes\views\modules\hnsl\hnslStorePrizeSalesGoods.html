<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
<META HTTP-EQUIV="expires" CONTENT="0"> 
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script>

<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>

	<div v-show="showList">
	   <div class="row">
	     <div class="form-group col-md-2">
					<label>销售品名称:</label> <input type="text" class="form-control"
						placeholder="销售品名称" v-model="hnslStorePrizeSalesGoods.salesName" />
		 </div>
		   <div class="form-group col-md-2">
			   <label>销售品ID:</label> <input type="text" class="form-control"
										   placeholder="销售品ID" v-model="hnslStorePrizeSalesGoods.salesId" />
		   </div>
		 <div class="form-group col-md-2">
					<label>状态:</label> <select class="form-control"
						style="height: 32px;" v-model="hnslStorePrizeSalesGoods.status">
						<option value=''>全部</option>
						<option value='1'>启用</option>
						<option value='0'>禁用</option>

					</select>
		 </div>
	   </div>
		<div class="grid-btn" style="height: 40px;margin-left: 1%;">
			<a v-if="hasPermission('hnslStorePrizeSalesGoods:list')" class="btn btn-primary" @click="query">&nbsp;查询</a>
			<a v-if="hasPermission('hnslStorePrizeSalesGoods:save')" class="btn btn-primary" @click="add">&nbsp;新增</a>
<!--			<a v-if="hasPermission('hnslschool:update')" class="btn btn-primary" @click="update">&nbsp;修改</a>-->
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
    
    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
		    <div class="form-group" style="width: 161%;">
			   	<div class="col-sm-2 control-label">销售品ID</div>
			   	<div class="col-sm-10" style="width: 28%">
			      <input type="text" class="form-control" v-model="hnslStorePrizeSalesGoodsAdd.salesId" placeholder="销售品ID"/>
			    </div>
			</div>
			<div class="form-group" style="width: 161%;">
			   	<div class="col-sm-2 control-label">销售品名称</div>
			   	<div class="col-sm-10" style="width: 28%">
			      <input type="text" class="form-control" v-model="hnslStorePrizeSalesGoodsAdd.salesName" placeholder="销售品名称"/>
			    </div>
			</div>
			<div class="form-group" style="width: 161%;">
			   	<div class="col-sm-2 control-label">店奖规则ID</div>
			   	<div class="col-sm-10" style="width: 28%">
			      <input type="text" class="form-control" v-model="hnslStorePrizeSalesGoodsAdd.settlementRulesId" placeholder="店奖规则ID"/>
			    </div>
			</div>
			<div class="form-group" style="width: 161%;">
			   	<div class="col-sm-2 control-label">结算规则名称</div>
			   	<div class="col-sm-10" style="width: 28%">
			      <input type="text" class="form-control" v-model="hnslStorePrizeSalesGoodsAdd.settlementRulesName" placeholder="结算规则名称"/>
			    </div>
			</div>
			<div class="form-group" style="width: 161%;margin-top:-2%">
				<div class="col-sm-2 control-label"></div>

				<input type="button" v-show="determine" class="btn btn-primary" style="margin-left: 3%" @click="format" value="确定"/>
				&nbsp;&nbsp;
				<input type="button" style="margin-left: 3%" class="btn btn-warning" @click="back" value="返回"/>
			</div>
		</form>
	</div>
</div>

<script src="../../js/modules/hnsl/hnslStorePrizeSalesGoods.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>