<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5MasterPackageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5MasterPackage">
        <id column="ID" property="id" />
        <result column="PACKAGE_CODE" property="packageCode" />
        <result column="PACKAGE_NAME" property="packageName" />
        <result column="STATUS" property="status" />
        <result column="CREATED_USER" property="createdUser" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="UPDATED_USER" property="updatedUser" />
        <result column="UPDATED_DATE" property="updatedDate" />
    </resultMap>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultMap="BaseResultMap">
        SELECT * FROM hnzsxh5_master_package
        <where>
            <if test="param.packageCode != null and param.packageCode != ''">
                AND PACKAGE_CODE LIKE CONCAT('%', #{param.packageCode}, '%')
            </if>
            <if test="param.packageName != null and param.packageName != ''">
                AND PACKAGE_NAME LIKE CONCAT('%', #{param.packageName}, '%')
            </if>
            <if test="param.status != null">
                AND STATUS = #{param.status}
            </if>
            <if test="param.createdDateStart != null and param.createdDateStart != ''">
                AND CREATED_DATE &gt;= #{param.createdDateStart}
            </if>
            <if test="param.createdDateEnd != null and param.createdDateEnd != ''">
                AND CREATED_DATE &lt;= #{param.createdDateEnd}
            </if>
        </where>
        <if test="page.orderBy != null">
            ORDER BY ${page.orderBy}
        </if>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultMap="BaseResultMap">
        SELECT * FROM hnzsxh5_master_package
        <where>
            <if test="packageCode != null and packageCode != ''">
                AND PACKAGE_CODE LIKE CONCAT('%', #{packageCode}, '%')
            </if>
            <if test="packageName != null and packageName != ''">
                AND PACKAGE_NAME LIKE CONCAT('%', #{packageName}, '%')
            </if>
            <if test="status != null">
                AND STATUS = #{status}
            </if>
            <if test="createdDateStart != null and createdDateStart != ''">
                AND CREATED_DATE &gt;= #{createdDateStart}
            </if>
            <if test="createdDateEnd != null and createdDateEnd != ''">
                AND CREATED_DATE &lt;= #{createdDateEnd}
            </if>
        </where>
    </select>

    <!-- 检查主套餐编码是否已存在 -->
    <select id="checkPackageCodeExists" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM hnzsxh5_master_package
        WHERE PACKAGE_CODE = #{packageCode}
        <if test="id != null">
            AND ID != #{id}
        </if>
    </select>

</mapper> 