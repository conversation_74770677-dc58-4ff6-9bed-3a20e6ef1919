<!DOCTYPE html>
<html>
<head>
	<title>2</title>
	<meta charset="UTF-8">
	<META HTTP-EQUIV="pragma" CONTENT="no-cache">
	<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
	<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
	<META HTTP-EQUIV="expires" CONTENT="0">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
	<link rel="stylesheet" href="../../css/bootstrap.min.css">
	<link rel="stylesheet" href="../../css/font-awesome.min.css">
	<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
	<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
	<link rel="stylesheet" href="../../css/main.css">
	<script src="../../libs/jquery.min.js"></script>
	<script src="../../plugins/layer/layer.js"></script>
	<script src="../../libs/bootstrap.min.js"></script>
	<script src="../../libs/vue.min.js"></script>
	<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
	<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
	<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

	<link href="../../plugins/Daterangepicker/css/daterangepicker.css" rel="stylesheet">
	<!-- select2组件 -->
	<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

	<!-- select2组件 -->
	<script src="../../plugins/select2/js/select2.min.js"></script>

	<script src="../../plugins/Daterangepicker/js/common.js"></script>
	<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

	<script src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
	<script src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
	<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

	<script src="../../js/common.js"></script>
	<style type="text/css">

		.templateShow-Info {
			float: left;
			width: 100%;
			font-size: 20px;
			padding: 10px 25px 0;
		}

		.templateShow-Info p {
			font-size: 20px;
			float: left;
			text-align: center;
			margin: 10px 0 10px;
			margin: 10px 0 10px;
		}

		.templateShow-Info p:nth-child(2) {
			color: #999 !important;
			font-size: 16px;
			line-height: 28px;
		}
	</style>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">

		<div class="row">
			<div class="form-group col-md-2">
				<label>下单人身份</label>
				<select class="form-control" style="height: 32px;" v-model='seachOrder.statusSf'>
					<option value='-1' >全部</option>
					<option v-for="itme in role" v-bind:value="itme.roleId">
						{{itme.roleName}}
					</option>
				</select>
			</div>
			<div class="form-group col-md-2">
				<label>订单类型:</label>
				<select class="form-control" style="height: 32px;" v-model='saflType'>
					<option value='-1'>全部</option>
					<option value="1">号卡新装</option>
					<option value="2">一人一码(本地)</option>
					<option value="3">一人一码(飞young)</option>
					<option value="4">加装订单</option>
					<option value="6">未成年开卡</option>
					<option value="7">自助下单</option>
					<option value="8">熟卡</option>
					<option value="9">未成年熟卡</option>
					<option value="10">商机单</option>
				</select>
			</div>

			<div class="form-group col-md-2">
				<label>状态:</label>
				<select class="form-control" style="height: 32px;" v-model="orderStatus">
					<option value='-1'>全部</option>
					<option value="1">实名提交</option>
					<option value="2">审核通过</option>
					<option value="3">激活成功</option>
					<option value="4">已受理</option>
					<option value="5">待受理</option>
					<option value="8">已支付未过费</option>
					<option value="9">已超时</option>
					<option value="10">待支付</option>
					<option value="11">预约</option>
					<option value="12">制卡</option>
					<option value="13">发货</option>
					<option value="15">一人一码激活成功</option>
					<option value="16">一人一码激活失败</option>
					<option value="17">一人一码激活异常</option>
					<option value="21">未加装</option>
					<option value="22">加装失败</option>
					<option value="23">加装成功</option>
					<option value="24">已退款</option>
				</select>
			</div>
			<div class="form-group col-md-2" style="height:40px">
				<label>身份证|姓名|预占号码:</label>
				<input type="text" class="form-control" placeholder="预占手机号码"  v-model='seachOrder.customerPhone'/>
			</div>
			<div class="form-group col-md-2" style="height:40px">
				<label>学校名称:</label>
				<input type="text" class="form-control" placeholder="学校名称" v-model='seachOrder.schoolName'/>
			</div>
			<div class="form-group col-md-2" style="height: 32px;">
				<label>创建日期:</label>
				<div class="input-group col-ms-2 ">
					<input class="form-control pull-left dateRange date-picker "
						   id="dateTimeRange" @keyup.enter="query" value="" type="text"
						   placeholder="日期"> <span class="input-group-addon">
							<i class="fa fa-calendar bigger-110"></i>
 						</span>
					<input name="beginTime" id="beginTime" type="hidden">
					<input name="endTime" id="endTime" type="hidden">
				</div>
			</div>
		</div>
		<div class="row">
			<div class="form-group col-md-2" style="height:40px">
				<label >订单号:</label>
				<input type="text" class="form-control" placeholder="订单号" v-model="seachOrder.orderId"/>
			</div>
			<div class="form-group col-md-2" style="height:40px">
				<label >BPS订单号:</label>
				<input type="text" class="form-control" placeholder="订单号" v-model="seachOrder.bpsOrderId"/>
			</div>
			<div class="form-group col-md-2" style="height:43px">
				<label>渠道类型:</label>
				<select class="form-control"  v-model="seachOrder.schoolChannelType">
					<option value='-1'>全部</option>
					<option v-for="itme in channelTypeList" v-bind:value="itme.index">
						{{itme.name}}
					</option>
				</select>
			</div>
			<div class="form-group col-md-2"  style="height:43px">
				<label>所属城市:</label>
				<select class="form-control" style="height: 32px;" v-model="seachOrder.cityCode">
					<option value='-1'>全部</option>
					<option v-for="itme in city" v-bind:value="itme.cityCode">
						{{itme.cityName}}
					</option>
				</select>
			</div>
			<div class="form-group col-md-2" style="height:40px">
				<label >商品名称:</label>
				<input type="text" class="form-control" placeholder="输入商品名称" v-model="seachOrder.goodsName"/>
			</div>
			<div class="form-group col-md-2" style="height:40px">
				<label >CRM订单号:</label>
				<input type="text" class="form-control" placeholder="CRM订单号" v-model="seachOrder.crmOrderId"/>
			</div>
		</div>
		<div class="row">
			<div class="form-group col-md-2" style="height:40px">
				<label >合伙人手机号:</label>
				<input type="text" class="form-control" placeholder="请输入合伙人手机号" v-model="seachOrder.hhid"/>
			</div>
		</div>
		<div class="grid-btn" style="margin-left: 19px;">
			<a v-if="hasPermission('hnsduser:query')" class="btn btn-primary" @click="query" style="margin-top: 1%">&nbsp;查询订单</a>
			<a v-if="hasPermission('hnsluser:outUserOrder')" class="btn btn-primary"  @click="outUserOrder" style="margin-top: 1%;" >&nbsp;批量导出</a>
			<a v-if="hasPermission('hnsluser:outUserOrder')" class="btn btn-primary"  @click="outQrUserOrder" style="margin-top: 1%;" >&nbsp;一人一码导出</a>
			<a v-if="hasPermission('hnsluser:save')"  class="btn btn-primary"  @click="replacePicture" style="margin-top: 1%;" >&nbsp;图片替换</a>
			<a v-if="hasPermission('hnsluser:adminUpdate')"  class="btn btn-primary"  @click="outResetOrder" style="margin-top: 1%;" >&nbsp;导出充值记录</a>
			<a v-if="hasPermission('hnsluser:adminUpdate')" class="btn btn-primary" @click="adminUpdate" style="margin-top: 1%;">&nbsp;admin修改</a>
			<a v-if="hasPermission('hnslorder:outputAgentOrder')" class="btn btn-primary"  @click="outAgentOrder" style="margin-top: 1%;" >&nbsp;订单导出</a>
			<a class="btn btn-primary" @click="templateShowIOrder" style="margin-top: 1%;">批量导入物流单号</a>
		</div>
		<table id="jqGrid"></table>
		<div id="jqGridPager"></div>
	</div>

	<div v-show="detailShow" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">

			<table class="textTable">
				<span style="font-size: 18px;">&nbsp;&nbsp;&nbsp;订单信息</span>
				<tr>
					<td class="leftTd" ><label style="width:42%;">订单编号:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.orderId}}
					</td>
					<td class="leftTd"><label>下单合伙人：</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.userName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">所属城市:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.cityName}}
					<td class="leftTd"><label>联系电话:</label></td>
					<td  style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.customerContactPhone}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">所属楼栋:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.buildingName}}
					<td class="leftTd"><label>所属宿舍:</label></td>
					<td  style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.roomName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;" >下单时间:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.createdDate}}
					</td>
					<td class="leftTd"><label>套餐名称:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.goodsName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">ICCID号:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.iccid}}
					</td>
					<td class="leftTd"><label>订购号码:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.customerPhone}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">订单状态:</label></td>
					<td style="width: 28%;" >
						<select disabled="disabled"  v-model="hnslOrder.orderStatus">
							<option value="1">实名提交</option>
							<option value="2">审核通过</option>
							<option value="3">激活成功</option>
							<option value="4">已受理</option>
							<option value="5">待受理</option>
							<option value="10">待支付</option>
							<option value="11">预约</option>
							<option value="12">制卡</option>
							<option value="13">发货</option>
							<option value="21">未加装</option>
							<option value="22">加装失败</option>
							<option value="23">加装成功</option>
							<option value="24">已退款</option>
						</select>
					</td>
					<td class="leftTd"><label>学校名称:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.schoolName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label  style="width:42%;">保底消费:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.phoneNbrprice}}
					</td>
					<td class="leftTd" ><label>预存话费:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.phonePreprice}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">订单金额:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.orderPrice}}
					</td>
					<td class="leftTd"><label>CRM订单号:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.crmOrderId}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">BPS订单号:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.bpsOrderId}}
					</td>
					<td class="leftTd" v-if="hnslOrderUpdate.orderStatus != '3'"><label>失败原因:</label></td>
					<td v-if="hnslOrderUpdate.orderStatus != '3'">
						&nbsp;&nbsp;&nbsp;&nbsp;{{hnslOrderUpdate.orderResult}}
					</td>
				</tr>
				<tr >
					<td class="leftTd" ><label style="width:42%;">激活时间:</label></td>
					<td>
						{{hnslOrder.ftpActivateDate?hnslOrder.ftpActivateDate:'无'}}
					</td>
					<td class="leftTd" ><label>下单地址:</label></td>
					<td>
						{{hnslOrder.preventionControlAddress?hnslOrder.preventionControlAddress:'无'}}
					</td>
				</tr>
				<tr v-if="hnslOrderUpdate.saflType==2">
					<td class="leftTd" ><label style="width:42%;">邮寄地址:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.mailingAddress}}{{hnslOrderUpdate.mailingDetailedAddress}}
					</td>
				</tr>
				<tr v-if="hnslOrder.saflType==10">
					<td class="leftTd" ><label style="width:42%;">预约订单号:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.reservationOrderId}}
					</td>
					<td class="leftTd"><label>预约客户姓名:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.reservationCustomerName}}
					</td>
				</tr>
				<tr v-if="hnslOrder.saflType==10">
					<td class="leftTd" ><label style="width:42%;">预约客户身份证:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.reservationCustomerCard}}
					</td>
					<td class="leftTd"><label>预约联系号码:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.reservationContactPhone}}
					</td>
				</tr>
				<tr v-if="hnslOrder.saflType==10">
					<td class="leftTd" ><label style="width:42%;">预约时间:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.reservationDate}}
					</td>
					<td class="leftTd"><label>预约地址:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.reservationContactAddress}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">宽带单号:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.bpsBroadbandOrderId}}
					</td>
					<td class="leftTd"><label>宽带进度:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{widebandProgressStatusText(hnslOrder.widebandProgressStatus)}}
					</td>
				</tr>
			</table>

			<div class="form-group">
				<div class="col-sm-2 control-label"></div>
				<!-- 				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                 -->				&nbsp;&nbsp;<input type="button"  style="margin-left: 101%; margin-top: 1%;padding: 2.4% 12%;font-size: 120%;"  class="btn btn-warning" @click="cancle" value="返回" />
			</div>
		</form>
	</div>
	<div v-show="!templateShowOrder" id="templateShowOrder"
		 class="panel panel-default">
		<div class="panel-heading">批量导入物流单号</div>
		<div class="form-horizontal" style="padding-top: 0px; width: 100%;">
			<form id="uploadImgOrder" enctype="multipart/form-data">
				<div class="templateShow-Info">
					<p>下载模板：</p>
					<p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
				</div>
				<div style="margin-left: 125px;">
					<a class="btn btn-primary" @click="getTemplateOrder">&nbsp;下载模板</a>
				</div>
				<div class="templateShow-Info">
					<p>上传文件：</p>
					<p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
				</div>
				<div style="margin-left: 125px;">
					<a v-if="hasPermission('hnsduser:importUser')"
					   class="btn btn-primary" @click="importOrder">&nbsp;开始导入</a> <input
						style="display: none;" name="uploadFileOrder" id="uploadFileOrder"
						type="file" @change="uploadFileOrder" />
				</div>
				<div style="width: 100%; text-align: center;">
					<input type="button" class="btn btn-warning" @click="reload"
						   value="返回" />
				</div>
			</form>
		</div>
	</div>
	<!--admin修改-->
	<div v-show="updateShow" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">

			<table class="textTable">
				<span style="font-size: 18px;">&nbsp;&nbsp;&nbsp;订单信息</span>
				<tr>
					<td class="leftTd" ><label style="width:42%;">订单编号:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.orderId}}
					</td>
					<td class="leftTd"><label>下单合伙人：</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.userName}}
					</td>
					<a style="margin-left: 10px" @click="noDesensitization(hnslOrder.hhrUserId)" class="btn btn-primary">免脱敏</a>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">所属城市:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.cityName}}
					<td class="leftTd"><label>联系电话:</label></td>
					<td  style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.customerContactPhone}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">下单合伙人ID:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.hhrUserId}}
					<td class="leftTd"><label>所属宿舍:</label></td>
					<td  style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.roomName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;" >下单时间:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.createdDate}}
					</td>
					<td class="leftTd"><label>套餐名称:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.goodsName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">ICCID号:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.iccid}}
					</td>
					<td class="leftTd"><label>订购号码:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.customerPhone}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">订单状态:</label></td>
					<td style="width: 28%;" >
						<select  v-model="orderStatusUpdate">
							<option value="1">实名提交</option>
							<option value="2">审核通过</option>
							<option value="3">激活成功</option>
							<option value="4">已受理</option>
							<option value="10">待支付</option>
							<option value="11">预约</option>
							<option value="12">制卡</option>
							<option value="13">发货</option>
							<option value="21">未加装</option>
							<option value="22">加装失败</option>
							<option value="23">加装成功</option>
							<option value="24">已退款</option>
						</select>
					</td>
					<td class="leftTd"><label>学校名称:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.schoolName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label  style="width:42%;">保底消费:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.phoneNbrprice}}
					</td>
					<td class="leftTd" ><label>预存话费:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.phonePreprice}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">订单金额:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.orderPrice}}
					</td>
					<td class="leftTd"><label>CRM订单号:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.crmOrderId}}
					</td>
				</tr>
				<tr >
					<td class="leftTd" ><label style="width:42%;">激活时间:</label></td>
					<td>
						{{hnslOrderUpdate.ftpActivateDate?hnslOrderUpdate.ftpActivateDate:'无'}}
					</td>
					<td class="leftTd" ><label>下单地址:</label></td>
					<td>
						{{hnslOrderUpdate.preventionControlAddress?hnslOrderUpdate.preventionControlAddress:'无'}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">BPS订单号:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.bpsOrderId}}
					</td>
					<td class="leftTd" v-if="hnslOrderUpdate.orderStatus != '3'"><label>失败原因:</label></td>
					<td v-if="hnslOrderUpdate.orderStatus != '3'">
						&nbsp;&nbsp;&nbsp;&nbsp;{{hnslOrderUpdate.orderResult}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">BPS正式同步结果:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.bpsFormalSynchro==1?'已同步':'未同步'}}
					</td>
					<td class="leftTd" ><label>BPS实名同步结果:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.bpsRealnameSynchro==1?'已同步':'未同步'}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">CRM过费结果:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.crmSureSynchro==1?'已过费':'未过费'}}
					</td>
					<td class="leftTd" ><label>重新同步BPS和过费:</label></td>
					<td>
						&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="resynchronization" value="开始同步"/>
					</td>
				</tr>
				<tr >
					<td class="leftTd" ><label style="width:42%;">修改过费状态:</label></td>
					<td>
						&nbsp;&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="updateCrmStatus" value="确认过费"/>
					</td>
					<td class="leftTd" ><label>重新提交CRM:</label></td>
					<td>
						&nbsp;&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="sumbitCrm" value="确认提交"/>
					</td>
				</tr>
				<tr >
					<td class="leftTd" ><label style="width:42%;">更新BPS熟卡订单信息:</label></td>
					<td>
						&nbsp;&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="tbbps" value="确认过费"/>
					</td>
					<td class="leftTd" ><label style="width:42%;">同步BPS活体视频:</label></td>
					<td>
						&nbsp;&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="tbbpsVideo" value="确认同步"/>
					</td>
				</tr>
				<tr >
					<td class="leftTd" ><label style="width:42%;">同步支付记录:</label></td>
					<td>
						&nbsp;&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="paybps" value="确认同步"/>
					</td>
				</tr>
				<tr v-if="hnslOrderUpdate.saflType==2">
					<td class="leftTd" ><label style="width:42%;">邮寄地址:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.mailingAddress}}{{hnslOrderUpdate.mailingDetailedAddress}}
					</td>
				</tr>
				<tr v-if="hnslOrderUpdate.saflType==10">
					<td class="leftTd" ><label style="width:42%;">预约订单号:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.reservationOrderId}}
					</td>
					<td class="leftTd"><label>预约客户姓名:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.reservationCustomerName}}
					</td>
				</tr>
				<tr v-if="hnslOrderUpdate.saflType==10">
					<td class="leftTd" ><label style="width:42%;">预约客户身份证:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.reservationCustomerCard}}
					</td>
					<td class="leftTd"><label>预约联系号码:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.reservationContactPhone}}
					</td>
				</tr>
				<tr v-if="hnslOrderUpdate.saflType==10">
					<td class="leftTd" ><label style="width:42%;">预约时间:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrderUpdate.reservationDate}}
					</td>
					<td class="leftTd"><label>预约地址:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrderUpdate.reservationContactAddress}}
					</td>
				</tr>
			</table>

			<div class="form-group">
				<div class="col-sm-2 control-label"></div>
				<input type="button" class="btn btn-warning" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button"   class="btn btn-warning" @click="cancle" value="返回" />
			</div>
		</form>
	</div>

	<div v-show="replaceImg" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
			<table class="textTable">
				<span style="font-size: 18px;">&nbsp;&nbsp;&nbsp;订单信息</span>
				<tr>
					<td class="leftTd" ><label style="width:42%;">订单编号:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.orderId}}
					</td>
					<td class="leftTd"><label>下单人：</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.userName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;" >下单时间:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.createdDate}}
					</td>
					<td class="leftTd"><label>套餐名称:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.goodsName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">ICCID号:</label></td>
					<td style="width: 28%;">
						&nbsp;&nbsp;{{hnslOrder.iccid}}
					</td>
					<td class="leftTd"><label>订购号码:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.customerPhone}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">订单状态:</label></td>
					<td style="width: 28%;" >
						<select disabled="disabled"  v-model="hnslOrder.orderStatus">
							<option value="1">实名提交</option>
							<option value="2">审核通过</option>
							<option value="3">激活成功</option>
							<option value="4">已受理</option>
							<option value="5">待受理</option>
							<option value="10">待支付</option>
							<option value="11">预约</option>
							<option value="12">制卡</option>
							<option value="13">发货</option>
							<option value="21">未加装</option>
							<option value="22">加装失败</option>
							<option value="23">加装成功</option>
							<option value="24">已退款</option>
						</select>
					</td>
					<td class="leftTd"><label>学校名称:</label></td>
					<td style="width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.schoolName}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">订单金额:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.orderPrice}}
					</td>
					<td class="leftTd"><label>CRM订单号:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.crmOrderId}}
					</td>
				</tr>

				<tr v-if="hnslOrder.saflType==10">
					<td class="leftTd" ><label style="width:42%;">预约订单号:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.reservationOrderId}}
					</td>
					<td class="leftTd"><label>预约客户姓名:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.reservationCustomerName}}
					</td>
				</tr>
				<tr v-if="hnslOrder.saflType==10">
					<td class="leftTd" ><label style="width:42%;">预约客户身份证:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.reservationCustomerCard}}
					</td>
					<td class="leftTd"><label>预约联系号码:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.reservationContactPhone}}
					</td>
				</tr>
				<tr v-if="hnslOrder.saflType==10">
					<td class="leftTd" ><label style="width:42%;">预约时间:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.reservationDate}}
					</td>
					<td class="leftTd"><label>预约地址:</label></td>
					<td style="width: 28%;width: 47%;">
						&nbsp;&nbsp;{{hnslOrder.reservationContactAddress}}
					</td>
				</tr>
				<tr>
					<td class="leftTd" ><label style="width:42%;">受理方式:</label></td>
					<td>
						&nbsp;&nbsp;{{hnslOrder.numberPoolType==2?'集团选号':'省内3.0'}}
					</td>
				</tr>
			</table>
			<!-- 原订单身份信息图片 -->
			<table class="textTable">
				<tr style="width: 100%">
					<td class="leftTd" style="height: 150px;width: 172px;"><label style="text-align: center"><span>身份证正面照:</span></label></td>
					<td>
						<img :src="url+imgUrl1" class="fileUp" alt="" style="width: 28%"  height="100" width="100" />
					</td>
					<td class="leftTd" style="height: 150px;width: 160px;"><label style="text-align: center"><span>替换身份证正面照:</span></label></td>
					<td style="width: 48%;">
						<img  class="fileUp" alt="" id="zm"  />
						<input class="fileUp" type="file" @change="upload('1')" accept="image/gif,image/jpeg,image/jpg,image/png" id="file1" value="" />
					</td>
				</tr>
				<tr>
					<td class="leftTd" style="height:150px;"><label style="text-align: center"><span>身份证反面照:</span></label></td>
					<td>
						<img :src="url+imgUrl2" class="fileUp" alt=""  height="100" width="100" />
					</td>
					<td class="leftTd" style="height:150px;"><label><span>替换身份证反面照:</span></label></td>
					<td>
						<img class="fileUp" alt="" id="fm"  />
						<input class="fileUp" type="file" @change="upload('2')" accept="image/gif,image/jpeg,image/jpg,image/png" id="file2" value="" />
					</td>
				</tr>
				<tr>
					<td class="leftTd" style="height:150px;"><label style="text-align: center"><span>免冠照:</span></label></td>
					<td>
						<img :src="url+imgUrl3" class="fileUp" alt=""  height="100" width="100" />
					</td>
					<td class="leftTd" style="height:150px;"><label><span>替换免冠照:</span></label></td>
					<td>
						<img  class="fileUp" alt="" id="mg"  />
						<input class="fileUp" type="file" @change="upload('3')" accept="image/gif,image/jpeg,image/jpg,image/png" id="file3" value="" />
					</td>
				</tr>
				<!--经办人图片替换-->
				<tr style="width: 100%">
					<td class="leftTd" style="height: 150px;width: 172px;"><label style="text-align: center"><span>经办人身份证正面照:</span></label></td>
					<td>
						<img :src="url+imgUrl4" class="fileUp" alt="" style="width: 28%"  height="100" width="100" />
					</td>
					<td class="leftTd" style="height: 150px;width: 160px;"><label style="text-align: center"><span>替换身份证正面照:</span></label></td>
					<td style="width: 48%;">
						<img  class="fileUp" alt="" id="jzm"  />
						<input class="fileUp" type="file" @change="upload('4')" accept="image/gif,image/jpeg,image/jpg,image/png" id="file4" value="" />
					</td>
				</tr>
				<tr>
					<td class="leftTd" style="height:150px;"><label style="text-align: center"><span>经办人身份证反面照:</span></label></td>
					<td>
						<img :src="url+imgUrl5" class="fileUp" alt=""  height="100" width="100" />
					</td>
					<td class="leftTd" style="height:150px;"><label><span>替换身份证反面照:</span></label></td>
					<td>
						<img class="fileUp" alt="" id="jfm"  />
						<input class="fileUp" type="file" @change="upload('5')" accept="image/gif,image/jpeg,image/jpg,image/png" id="file5" value="" />
					</td>
				</tr>
				<tr>
					<td class="leftTd" style="height:150px;"><label style="text-align: center"><span>经办人免冠照:</span></label></td>
					<td>
						<img :src="url+imgUrl6" class="fileUp" alt=""  height="100" width="100" />
					</td>
					<td class="leftTd" style="height:150px;"><label><span>替换免冠照:</span></label></td>
					<td>
						<img  class="fileUp" alt="" id="jmg"  />
						<input class="fileUp" type="file" @change="upload('6')" accept="image/gif,image/jpeg,image/jpg,image/png" id="file6" value="" />
					</td>
				</tr>
				<!--工作证图片替换-->
				<tr style="width: 100%">
					<td class="leftTd" style="height: 150px;width: 172px;"><label style="text-align: center"><span>工作证图片1:</span></label></td>
					<td>
						<img :src="url+imgUrl7" class="fileUp" alt="" style="width: 28%"  height="100" width="100" />
					</td>
					<td class="leftTd" style="height: 150px;width: 160px;"><label style="text-align: center"><span>替换工作证图片1:</span></label></td>
					<td style="width: 48%;">
						<img  class="fileUp" alt="" id="gzz1"  />
						<input class="fileUp" type="file" @change="upload('7')" accept="image/gif,image/jpeg,image/jpg,image/png" id="file7" value="" />
					</td>
				</tr>
				<tr>
					<td class="leftTd" style="height:150px;"><label style="text-align: center"><span>工作证图片2:</span></label></td>
					<td>
						<img :src="url+imgUrl8" class="fileUp" alt=""  height="100" width="100" />
					</td>
					<td class="leftTd" style="height:150px;"><label><span>替换工作证图片2:</span></label></td>
					<td>
						<img class="fileUp" alt="" id="gzz2"  />
						<input class="fileUp" type="file" @change="upload('8')" accept="image/gif,image/jpeg,image/jpg,image/png" id="file8" value="" />
					</td>
				</tr>
				<tr>
					<td class="leftTd" style="height:150px;"><label style="text-align: center"><span>工作证图片3:</span></label></td>
					<td>
						<img :src="url+imgUrl9" class="fileUp" alt=""  height="100" width="100" />
					</td>
					<td class="leftTd" style="height:150px;"><label><span>替换工作证图片2:</span></label></td>
					<td>
						<img  class="fileUp" alt="" id="gzz3"  />
						<input class="fileUp" type="file" @change="upload('9')" accept="image/gif,image/jpeg,image/jpg,image/png" id="file9" value="" />
					</td>
				</tr>
			</table>

			<div class="form-group">
				<div class="col-sm-2 control-label" @click="replace">
					<input type="button"  style="margin-left: 101%; margin-top: 1%;padding: 2.4% 12%;font-size: 120%;" class="btn btn-primary"  value="替换"/>
				</div>
				<div class="col-sm-2 control-label">
					<input type="button"  style="margin-left: 101%; margin-top: 1%;padding: 2.4% 12%;font-size: 120%;" class="btn btn-warning" @click="cancle"  value="返回" />
				</div>
			</div>

		</form>
	</div>

	<div class="modal" id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog" style="padding: 70px">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true">×
					</button>
					<h4 class="modal-title" id="myModalLabel1" style="text-align: center">数据下载审批人选择</h4>
					<div style="display: flex">
						<input placeholder="请输入审批人姓名或手机号" v-model="searchText"
							   @input="onInput"/>
					</div>

					<table class="table table-bordered" style="width: 100%; text-align: center;margin-top: 10px">
						<tbody id="myTable">
						<tr>
							<td></td>
							<td></td>
						</tr>
						<tr>
							<td></td>
							<td></td>
						</tr>
						</tbody>
					</table>
					<div style="width: 100%;text-align: center;">
						<a href="#" id="prevPage" @click="prevPage">上一页</a>
						<span id="currentPage">1</span>
						<a href="#" id="nextPage" @click="nextPage">下一页</a>
					</div>

					<div style="text-align: right;margin-top: 20px;">
						<div class="col-sm-2 control-label"></div>
						<input type="button" class="btn btn-warning" @click="submitApprover" value="确定"/>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!--模板下载-->
	<!--<div v-show="!templateShow" id="templateShow"
		 class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<div class="form-horizontal" style="padding-top: 0px; width: 100%;">
			<form id="uploadImg" enctype="multipart/form-data">
				<div class="templateShow-Info">
					<p>下载模板：</p>
					<p>为提高导入的成功率，请下载并使用系统提供的模板:</p>
				</div>
				<div style="margin-left: 125px;">
					<a class="btn btn-primary" @click="getTemplate">&nbsp;下载模板</a>
				</div>
				<div class="templateShow-Info">
					<p>上传文件：</p>
					<p>仅支持:.xlsx，.xls,;文件大小:≤4M</p>
				</div>
				<div style="margin-left: 125px;">
					<a v-if="hasPermission('hnsduser:importUser')"
					   class="btn btn-primary" @click="importUser">&nbsp;开始导入</a> <input
						style="display: none;" name="uploadFile" id="uploadFile"
						type="file" @change="uploadFile" />
				</div>
				<div style="width: 100%; text-align: center;">
					<input type="button" class="btn btn-warning" @click="reload"
						   value="返回" />
				</div>
			</form>
		</div>
	</div>-->

	<div class="modal" id="myModal2" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog" style="padding: 70px">
			<div class="modal-content">
				<div class="modal-header">
					<div style="display: flex">
						合伙人姓名：<input v-model="hnslUserInfoId.userName" disabled/>
					</div>
					<div style="display: flex;margin-top: 10px">
						合伙人手机号：<input v-model="hnslUserInfoId.userPhone" disabled/>
					</div>
					<div style="text-align: center;margin-top: 20px;">
						<div class="col-sm-2 control-label"></div>
						<input type="button" class="btn btn-warning" @click="closeDe" value="确定"/>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script src="../../js/modules/hnsl/hnslorder.js"></script>
<script src="../../js/components.js"></script>
</body>
</html>