<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsNumber != null">
                AND a.GOODS_NUMBER = #{param.goodsNumber}
            </if>
            <if test="param.originalCost != null">
                AND a.ORIGINAL_COST = #{param.originalCost}
            </if>
            <if test="param.goodsName != null">
                AND a.GOODS_NAME LIKE CONCAT('%', #{param.goodsName}, '%')
            </if>
            <if test="param.addTime != null">
                AND a.ADD_TIME LIKE CONCAT('%', #{param.addTime}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.updateTime != null">
                AND a.UPDATE_TIME LIKE CONCAT('%', #{param.updateTime}, '%')
            </if>
            <if test="param.packageDetails != null">
                AND a.PACKAGE_DETAILS LIKE CONCAT('%', #{param.packageDetails}, '%')
            </if>
            <if test="param.packageFlow != null">
                AND a.PACKAGE_FLOW LIKE CONCAT('%', #{param.packageFlow}, '%')
            </if>
            <if test="param.packageVoice != null">
                AND a.PACKAGE_VOICE LIKE CONCAT('%', #{param.packageVoice}, '%')
            </if>
            <if test="param.packageAnswer != null">
                AND a.PACKAGE_ANSWER LIKE CONCAT('%', #{param.packageAnswer}, '%')
            </if>
            <if test="param.bandwidthOfferName != null">
                AND a.BANDWIDTH_OFFER_NAME LIKE CONCAT('%', #{param.bandwidthOfferName}, '%')
            </if>
            <if test="param.bandwidthOfferId != null">
                AND a.BANDWIDTH_OFFER_ID LIKE CONCAT('%', #{param.bandwidthOfferId}, '%')
            </if>
            <if test="param.itvOfferName != null">
                AND a.ITV_OFFER_NAME LIKE CONCAT('%', #{param.itvOfferName}, '%')
            </if>
            <if test="param.itvOfferId != null">
                AND a.ITV_OFFER_ID LIKE CONCAT('%', #{param.itvOfferId}, '%')
            </if>
            <if test="param.guhuaOfferName != null">
                AND a.GUHUA_OFFER_NAME LIKE CONCAT('%', #{param.guhuaOfferName}, '%')
            </if>
            <if test="param.guhuaOfferId != null">
                AND a.GUHUA_OFFER_ID LIKE CONCAT('%', #{param.guhuaOfferId}, '%')
            </if>
            <if test="param.giftPackageName != null">
                AND a.GIFT_PACKAGE_NAME LIKE CONCAT('%', #{param.giftPackageName}, '%')
            </if>
            <if test="param.giftPackageId != null">
                AND a.GIFT_PACKAGE_ID LIKE CONCAT('%', #{param.giftPackageId}, '%')
            </if>
            <if test="param.giftPackageInfo != null">
                AND a.GIFT_PACKAGE_INFO LIKE CONCAT('%', #{param.giftPackageInfo}, '%')
            </if>
            <if test="param.offerPackType != null">
                AND a.OFFER_PACK_TYPE LIKE CONCAT('%', #{param.offerPackType}, '%')
            </if>
            <if test="param.offerPackTypeName != null">
                AND a.OFFER_PACK_TYPE_NAME LIKE CONCAT('%', #{param.offerPackTypeName}, '%')
            </if>
            <if test="param.isPrestore != null">
                AND a.IS_PRESTORE LIKE CONCAT('%', #{param.isPrestore}, '%')
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoods">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoods">
        <include refid="selectSql"></include>
    </select>

    <select id="queryGoodsByOfferPackageId" resultType="Integer">
        SELECT  g.id FROM  hnzsx_goods g where g.GIFT_PACKAGE_ID = #{giftPackageId} AND g.CITY_CODE = #{cityCode}
    </select>

</mapper>
