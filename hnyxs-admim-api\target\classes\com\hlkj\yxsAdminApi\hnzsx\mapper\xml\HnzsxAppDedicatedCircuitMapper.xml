<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppDedicatedCircuitMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_dedicated_circuit a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsDetailsId != null">
                AND a.GOODS_DETAILS_ID = #{param.goodsDetailsId}
            </if>
            <if test="param.dedicatedCircuitName != null">
                AND a.DEDICATED_CIRCUIT_NAME LIKE CONCAT('%', #{param.dedicatedCircuitName}, '%')
            </if>
            <if test="param.dedicatedCircuitType != null">
                AND a.DEDICATED_CIRCUIT_TYPE = #{param.dedicatedCircuitType}
            </if>
            <if test="param.dedicatedCircuitNumber != null">
                AND a.DEDICATED_CIRCUIT_NUMBER LIKE CONCAT('%', #{param.dedicatedCircuitNumber}, '%')
            </if>
            <if test="param.dedicatedCircuitToLink != null">
                AND a.DEDICATED_CIRCUIT_TO_LINK LIKE CONCAT('%', #{param.dedicatedCircuitToLink}, '%')
            </if>
            <if test="param.dedicatedCircuitSource != null">
                AND a.DEDICATED_CIRCUIT_SOURCE LIKE CONCAT('%', #{param.dedicatedCircuitSource}, '%')
            </if>
            <if test="param.fixedLineTerminal != null">
                AND a.FIXED_LINE_TERMINAL LIKE CONCAT('%', #{param.fixedLineTerminal}, '%')
            </if>
            <if test="param.debugFee != null">
                AND a.DEBUG_FEE = #{param.debugFee}
            </if>
            <if test="param.remark != null">
                AND a.REMARK LIKE CONCAT('%', #{param.remark}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.UPDATE_DATE LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuit">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuit">
        <include refid="selectSql"></include>
    </select>

    <!--  保存专线关联主套餐  -->
    <insert id="savePackagerel">
        INSERT INTO hnkj_yxs.hnzsx_dedicated_circuit_main_package_rel
        (DEDICATED_CIRCUIT_ID, MAIN_PACKAGE_ID)
        VALUES
        <foreach collection="packageIds" item="item"  separator="," >
            (#{DedicatedCircuitId}, #{item})
        </foreach>
    </insert>
    <!--  删除专线关联主套餐  -->
    <delete id="removePackagerel">
        delete from hnzsx_dedicated_circuit_main_package_rel where DEDICATED_CIRCUIT_ID = #{id}
    </delete>

    <!--  保存专线主套餐关联权益  -->
    <insert id="savePackageEquityRel">
        INSERT INTO hnzsx_dedicated_circuit_main_package_equity_rel
        (MAIN_PACKAGE_ID, EQUITY_ID, EQUITY_DEPOSIT,DEDICATED_CIRCUIT_ID)
        VALUES
        <foreach collection="equityIds" item="item"  separator="," >
            (#{item.packageId}, #{item.equityId}, #{item.depositAmount},#{DedicatedCircuitId})
        </foreach>
    </insert>

    <!--  删除专线主套餐关联权益  -->
    <delete id="removePackageEquityRel">
        delete from hnzsx_dedicated_circuit_main_package_equity_rel where DEDICATED_CIRCUIT_ID = #{id}
    </delete>

    <!--  保存专线主套餐关联IP  -->
    <insert id="savePackageIpRel">
        INSERT INTO hnzsx_dedicated_circuit_main_package_ip_rel
        (MAIN_PACKAGE_ID, IP_ID,DEDICATED_CIRCUIT_ID)
        VALUES
        <foreach collection="ipIds" item="item"  separator="," >
            (#{item.packageId}, #{item.ipId},#{DedicatedCircuitId})
        </foreach>
    </insert>

    <!--  删除专线主套餐关联IP  -->
    <delete id="removePackageIpRel">
        delete from hnzsx_dedicated_circuit_main_package_ip_rel where DEDICATED_CIRCUIT_ID = #{id}
    </delete>

    <!--  保存专线关联发票  -->
    <insert id="saveInvoiceRel">
        INSERT INTO hnzsx_dedicated_circuit_invoice_rel
        (DEDICATED_CIRCUIT_ID, INVOICE_ID)
        VALUES
        <foreach collection="invoiceIds" item="item"  separator=",">
            (#{DedicatedCircuitId}, #{item})
        </foreach>
    </insert>

    <!--  删除专线关联发票  -->
    <delete id="removeInvoiceRel">
        delete from hnzsx_dedicated_circuit_invoice_rel where DEDICATED_CIRCUIT_ID = #{id}
    </delete>


    <!--  查询专线关联权益说明  -->
    <select id="getDedicatedCircuitEquity" resultType="map" parameterType="Integer">
        select
            distinct hdcmpi.IP_OFFER_NAME,
            hdcmp.CITY_NAME,
            hdcmp.GOODS_NAME,
            hdcmpe.EQUITY_LEVEL_NAME,
            hdcmpe.DEPOSIT_AMOUNT,
            hdcmp.ID as MAIN_PACKAGE_ID,
            hdcmpe.ID as EQUITY_ID,
            hdcmpi.ID as IP_ID,
            hdc.ID as DEDICATED_CIRCUIT_ID
        from
            hnzsx_dedicated_circuit hdc
            left join hnzsx_dedicated_circuit_main_package_rel hdcmpr on
            hdc.ID = hdcmpr.DEDICATED_CIRCUIT_ID
            left join hnzsx_dedicated_circuit_main_package hdcmp on
            hdcmp.ID = hdcmpr.MAIN_PACKAGE_ID
            left join hnzsx_dedicated_circuit_main_package_equity_rel hdcmper on
            hdc.ID = hdcmper.DEDICATED_CIRCUIT_ID
                and hdcmp.ID = hdcmper.MAIN_PACKAGE_ID
            left join hnzsx_dedicated_circuit_main_package_equity hdcmpe on
            hdcmpe.ID = hdcmper.EQUITY_ID
            left join hnzsx_dedicated_circuit_main_package_ip_rel hdcmpir on
            hdc.id = hdcmpir.DEDICATED_CIRCUIT_ID
                and hdcmp.ID = hdcmpir.MAIN_PACKAGE_ID
            left join hnzsx_dedicated_circuit_main_package_ip hdcmpi on
            hdcmpi.ID = hdcmpir.IP_ID
        where hdc.GOODS_DETAILS_ID =  #{id}
    </select>

    <!--  查询专线关联发票内容  -->
    <select id="getinvoiceShow" parameterType="Integer" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxDedicatedCircuitInvoice">
        select hdci.* from hnzsx_dedicated_circuit_invoice_rel hdcir
        inner join hnzsx_dedicated_circuit_invoice hdci on hdcir.INVOICE_ID = hdci.ID
        where hdcir.DEDICATED_CIRCUIT_ID = #{id}
    </select>

</mapper>
