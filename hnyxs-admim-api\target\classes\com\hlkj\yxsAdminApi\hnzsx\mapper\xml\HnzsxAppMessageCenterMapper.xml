<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppMessageCenterMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_message_center a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.messageTitle != null">
                AND a.MESSAGE_TITLE LIKE CONCAT('%', #{param.messageTitle}, '%')
            </if>
            <if test="param.messageContent != null">
                AND a.MESSAGE_CONTENT LIKE CONCAT('%', #{param.messageContent}, '%')
            </if>
            <if test="param.announcementTypeCode != null">
                AND a.ANNOUNCEMENT_TYPE_CODE = #{param.announcementTypeCode}
            </if>
            <if test="param.announcementTypeName != null">
                AND a.ANNOUNCEMENT_TYPE_NAME = #{param.announcementTypeName}
            </if>
            <if test="param.messageTypeCode != null">
                AND a.MESSAGE_TYPE_CODE = #{param.messageTypeCode}
            </if>
            <if test="param.messageTypeName != null">
                AND a.MESSAGE_TYPE_NAME LIKE CONCAT('%', #{param.messageTypeName}, '%')
            </if>
            <if test="param.popupSwitch != null">
                AND a.POPUP_SWITCH = #{param.popupSwitch}
            </if>
            <if test="param.popupStartDate != null">
                AND a.POPUP_START_DATE LIKE CONCAT('%', #{param.popupStartDate}, '%')
            </if>
            <if test="param.popupEndDate != null">
                AND a.POPUP_END_DATE LIKE CONCAT('%', #{param.popupEndDate}, '%')
            </if>
            <if test="param.cityCodeList != null">
                AND a.CITY_CODE_LIST LIKE CONCAT('%', #{param.cityCodeList}, '%')
            </if>
            <if test="param.readingsNumber != null">
                AND a.READINGS_NUMBER = #{param.readingsNumber}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxMessageCenter">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxMessageCenter">
        <include refid="selectSql"></include>
    </select>

</mapper>
