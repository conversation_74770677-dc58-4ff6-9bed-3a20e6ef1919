<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppGoodsYfzjEqpackCgrDataMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_goods_yfzj_eqpack_cgr_data a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.instalmentName != null">
                AND a.INSTALMENT_NAME LIKE CONCAT('%', #{param.instalmentName}, '%')
            </if>
            <if test="param.instalmentId != null">
                AND a.INSTALMENT_ID LIKE CONCAT('%', #{param.instalmentId}, '%')
            </if>
            <if test="param.yfzjEqpackCgrId != null">
                AND a.YFZJ_EQPACK_CGR_ID LIKE CONCAT('%', #{param.yfzjEqpackCgrId}, '%')
            </if>
            <if test="param.saleName != null">
                AND a.SALE_NAME LIKE CONCAT('%', #{param.saleName}, '%')
            </if>
            <if test="param.eqAction != null">
                AND a.EQ_ACTION LIKE CONCAT('%', #{param.eqAction}, '%')
            </if>
            <if test="param.acceptanceSteps != null">
                AND a.ACCEPTANCE_STEPS LIKE CONCAT('%', #{param.acceptanceSteps}, '%')
            </if>
            <if test="param.exhibitStatus != null">
                AND a.EXHIBIT_STATUS LIKE CONCAT('%', #{param.exhibitStatus}, '%')
            </if>
            <if test="param.createDate != null">
                AND a.CREATE_DATE LIKE CONCAT('%', #{param.createDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.UPDATE_DATE LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
            <if test="param.salesProductType != null">
                AND a.SALES_PRODUCT_TYPE LIKE CONCAT('%', #{param.salesProductType}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsYfzjEqpackCgrData">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxGoodsYfzjEqpackCgrData">
        <include refid="selectSql"></include>
    </select>

</mapper>
