<!DOCTYPE html>
<html>
<head>
<title>2</title>
<meta charset="UTF-8">
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<META HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate">
<META HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT">
<META HTTP-EQUIV="expires" CONTENT="0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta
	content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
	name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet"
	href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet"
	href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>

<link href="../../plugins/Daterangepicker/css/daterangepicker.css"
	rel="stylesheet">
<!-- select2组件 -->
<link href="../../plugins/select2/css/select2.min.css" rel="stylesheet" />

<!-- select2组件 -->
<script src="../../plugins/select2/js/select2.min.js"></script>

<script src="../../plugins/Daterangepicker/js/common.js"></script>
<script src="../../plugins/Daterangepicker/js/date-time/moment.js"></script>

<script
	src="../../plugins/Daterangepicker/js/date-time/bootstrap-datetimepicker.zh-CN.js"></script>
<script
	src="../../plugins/Daterangepicker/js/date-time/daterangepicker.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-daterangepicker/2.1.25/moment.min.js"></script> -->

<script src="../../js/common.js"></script>
<style type="text/css">
.form-horizontal .form-group {
	margin-left: 0px;
}
</style>
</head>
<body>
	<div id="rrapp" v-cloak>
		<div v-show="showList">
			<div class="grid-btn">
				<div class="row">
					<div class="form-group col-md-2" align="left">
						<select class="form-control" v-model="hnsdBusinessModule.type"
							style="width: auto;">
							<option value='0'>-请选择查询选项-</option>
							<option value='1'>类别</option>
							<option value='2'>子模块</option>
							<option value='3'>模块详情</option>
						</select>
					</div>
					<div class="form-group col-md-2" style="width: auto;">
						<input type="text"
							class="form-control pull-left dateRange date-picker "
							v-model="condition" @keyup.enter="query" placeholder="类别名|模块名"
							style="width: auto; margin-right: 15px;" /> <a
							v-if="hasPermission('hnsdbusinessmodule:query')"
							class="btn btn-default" @click="query">&nbsp;查询</a> <a
							v-if="hasPermission('hnsdbusinessmodule:save')"
							class="btn btn-primary" @click="addCategory"><i
							class="fa fa-plus"></i>&nbsp;新增类别</a> <a
							v-if="hasPermission('hnsdbusinessmodule:save')"
							class="btn btn-primary" @click="addModuleSubclass"><i
							class="fa fa-plus"></i>&nbsp;新增子模块</a> <a
							v-if="hasPermission('hnsdbusinessmodule:save')"
							class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增模块</a>
						<a v-if="hasPermission('hnsdbusinessmodule:update')"
							class="btn btn-primary" @click="update"><i
							class="fa fa-pencil-square-o"></i>&nbsp;修改</a> <a
							v-if="hasPermission('hnsdbusinessmodule:delete')"
							class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a>
					</div>
				</div>

			</div>
			<table id="jqGrid"></table>
			<div id="jqGridPager"></div>
		</div>

		<div v-show="!showList" class="panel panel-default">
			<div class="panel-heading">{{title}}</div>
			<form class="form-horizontal" style="margin: auto;">
				<!--修改 -->
				<div v-if="showType==1" class="form-group">
					<div class="col-sm-2 control-label">类别名称</div>
					<div class="col-sm-10">
						<select class="form-control"
							v-model="hnsdBusinessModule.moduleName" id="moduleName">
							<option value='00'>请选择</option>
							<option v-for="itme in moduleList"
								v-bind:value="itme.moduleName"
								v-bind:title="itme.moduleName">{{itme.moduleName}}</option>
						</select>
					</div>
				</div>
				<!--增加 -->
				<div v-else-if="showType==2" class="form-group">
					<div class="col-sm-2 control-label">类别名称</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnsdBusinessModule.moduleName" placeholder="类别名称" />
					</div>
				</div>

				<div v-if="showType==1" class="form-group">
					<div class="col-sm-2 control-label">子类别名称</div>
					<div class="col-sm-10">
						<select class="form-control"
							v-model="hnsdBusinessModule.moduleSubclassName"
							id="moduleSubclassName">
							<option value='00'>请选择</option>
							<option v-for="itme in hnsdBusinessModuleList"
								v-bind:value="itme.moduleSubclassName"
								v-bind:title="itme.moduleSubclassName">
								{{itme.moduleSubclassName}}</option>
						</select>
					</div>
				</div>
				<div v-else-if="showType==3" class="form-group">
					<div class="col-sm-2 control-label">子类别名称</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnsdBusinessModule.moduleSubclassName"
							placeholder="子类别名称" />
					</div>
				</div>

				<div v-if="showType==1" class="form-group">
					<div class="col-sm-2 control-label">模块图片链接</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnsdBusinessModule.moduleImg" placeholder="模块图片" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">备注</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnsdBusinessModule.remark" placeholder="备注" />
					</div>
				</div>
				<div v-if="showType==1" class="form-group">
					<div class="col-sm-2 control-label">跳转URL</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnsdBusinessModule.redirectUrl" placeholder="跳转URL" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">状态</div>
					<div class="col-sm-10">
						<select class="form-control" v-model="hnsdBusinessModule.status">
							<option value=''>请选择</option>
							<option value='0'>下架</option>
							<option value='1'>上架</option>
						</select>
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label">排序</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnsdBusinessModule.sort" placeholder="排序" />
					</div>
				</div>
				<div v-if="showType==1" class="form-group">
					<div class="col-sm-2 control-label">绑定的事件名</div>
					<div class="col-sm-10">
						<input type="text" class="form-control"
							v-model="hnsdBusinessModule.bindtap" placeholder="绑定的事件名" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-2 control-label"></div>
					<input type="button" class="btn btn-primary" @click="saveOrUpdate"
						value="确定" /> &nbsp;&nbsp;<input type="button"
						class="btn btn-warning" @click="previousPage" value="返回" />
				</div>
			</form>
		</div>
	</div>

	<script src="../../js/modules/hnsd/hnsdbusinessmodule.js"></script>
	<script src="../../js/components.js"></script>
</body>
</html>