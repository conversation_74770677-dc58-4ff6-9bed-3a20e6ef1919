<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5GoodsStaffRelMapper">

    <!-- 批量插入商品工号关联关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO hnzsxh5_goods_staff_rel
        (GOODS_ID, STAFF_CODE, CITY, CITY_CODE, CREATED_DATE)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.goodsId}, #{item.staffCode}, #{item.city}, #{item.cityCode}, NOW())
        </foreach>
    </insert>

    <!-- 根据商品ID删除关联关系 -->
    <delete id="deleteByGoodsId" parameterType="java.lang.Integer">
        DELETE FROM hnzsxh5_goods_staff_rel WHERE GOODS_ID = #{goodsId}
    </delete>

    <!-- 根据商品ID获取关联的用户列表 -->
    <select id="getStaffByGoodsId" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5User">
        SELECT DISTINCT u.ID, u.STAFF_CODE, u.USER_NAME, u.CITY, u.CITYCODE, u.ORG_NAME
        FROM hnzsxh5_user u
        INNER JOIN hnzsxh5_goods_staff_rel r ON r.STAFF_CODE = u.STAFF_CODE AND r.CITY_CODE = u.CITYCODE
        WHERE r.GOODS_ID = #{goodsId}
        <if test="staffCodeAndCityCode != null and staffCodeAndCityCode.size() > 0">
            AND (
            <foreach collection="staffCodeAndCityCode" item="item" separator=" OR ">
                (u.STAFF_CODE = #{item.staffCode} AND u.CITYCODE = #{item.cityCode})
            </foreach>
            )
        </if>
    </select>

    <!-- 批量保存商品工号关联（带地市信息） -->
    <insert id="batchSave" parameterType="java.util.List">
        INSERT INTO hnzsxh5_goods_staff_rel
        (GOODS_ID, STAFF_CODE, CITY, CITY_CODE, CREATED_DATE)
        VALUES
        <foreach collection="relList" item="item" separator=",">
            (#{item.goodsId}, #{item.staffCode}, #{item.city}, #{item.cityCode}, NOW())
        </foreach>
    </insert>

</mapper> 