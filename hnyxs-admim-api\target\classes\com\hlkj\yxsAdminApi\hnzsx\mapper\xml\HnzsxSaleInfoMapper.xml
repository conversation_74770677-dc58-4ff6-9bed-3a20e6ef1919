<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxSaleInfoMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_sale_info a
        <where>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.saleNumber != null">
                AND a.sale_number LIKE CONCAT('%', #{param.saleNumber}, '%')
            </if>
            <if test="param.saleName != null">
                AND a.sale_name LIKE CONCAT('%', #{param.saleName}, '%')
            </if>
            <if test="param.action != null">
                AND a.action LIKE CONCAT('%', #{param.action}, '%')
            </if>
            <if test="param.product != null">
                AND a.product LIKE CONCAT('%', #{param.product}, '%')
            </if>
            <if test="param.saleParameter != null">
                AND a.sale_parameter LIKE CONCAT('%', #{param.saleParameter}, '%')
            </if>
            <if test="param.typeCode != null">
                AND a.type_code = #{param.typeCode}
            </if>
            <if test="param.typeRemarks != null">
                AND a.type_remarks LIKE CONCAT('%', #{param.typeRemarks}, '%')
            </if>
            <if test="param.associationId != null">
                AND a.association_id = #{param.associationId}
            </if>
            <if test="param.createDate != null">
                AND a.create_date LIKE CONCAT('%', #{param.createDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.update_date LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.status = #{param.status}
            </if>
            <if test="param.acceptanceSteps != null">
                AND a.ACCEPTANCE_STEPS = #{param.acceptanceSteps}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSaleInfo">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSaleInfo">
        <include refid="selectSql"></include>
    </select>

    <!--  查询随意选合约关联销售品  -->
    <select id="getChooseFreelySale" parameterType="Integer" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSaleInfo">
        select hsi.*,CONCAT(hgcf.equity_name,'(',hgcf.bps_show_module_name,')') as equity_name from hnzsx_sale_info hsi
        inner join hnzsx_goods_choose_freely hgcf on hsi.association_id = hgcf.id and hsi.type_code = 3
        where hsi.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

</mapper>
