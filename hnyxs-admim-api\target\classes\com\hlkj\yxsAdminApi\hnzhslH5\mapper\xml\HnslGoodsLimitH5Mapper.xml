<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhslH5.mapper.HnslGoodsLimitH5Mapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_h5_goods_limit a
        <where>
            <if test="param.goodsNumber != null and param.goodsNumber != ''">
                AND a.GOODS_NUMBER = #{param.goodsNumber}
            </if>
            <if test="param.goodsLimitNumber != null and param.goodsLimitNumber != ''">
                AND a.GOODS_LIMIT_NUMBER = #{param.goodsLimitNumber}
            </if>
            <if test="param.schoolCode != null and param.schoolCode != ''">
                AND a.SCHOOL_CODE = #{param.schoolCode}
            </if>
            <if test="param.status != null and param.status != ''">
                AND a.STATUS = #{param.status}
            </if>
        </where>
        order by a.id desc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsLimit">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5GoodsLimit">
        <include refid="selectSql"></include>
    </select>

</mapper>
