<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5GoodsInfoMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*,
               GROUP_CONCAT(DISTINCT t.TEMPLATE_ID) AS templateIdStr
        FROM hnzsxh5_goods_info a
        LEFT JOIN (
            SELECT ht.ID, ht.TEMPLATE_ID 
            FROM hnzsxh5_template_info ht
        ) t ON FIND_IN_SET(t.ID, a.TEMPLATE_ID_LIST)
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsTagId != null">
                AND FIND_IN_SET(#{param.goodsTagId}, a.GOODS_TAG_LIST) > 0
            </if>
            <if test="param.giftPackageId != null and param.giftPackageId !=''">
                 AND FIND_IN_SET(#{param.giftPackageId}, a.GIFT_PACKAGE_IDS) > 0
            </if>
            <if test="param.goodsName != null and param.goodsName !=''">
                AND a.GOODS_NAME LIKE CONCAT('%', #{param.goodsName}, '%')
            </if>
            <if test="param.goodsTagList != null and param.goodsTagList !=''">
                AND a.GOODS_TAG_LIST LIKE CONCAT('%', #{param.goodsTagList}, '%')
            </if>
            <if test="param.goodsAttributeTypeId != null ">
                AND a.GOODS_ATTRIBUTE_TYPE_ID = #{param.goodsAttributeTypeId}
            </if>
            <if test="param.goodsPrice != null">
                AND a.GOODS_PRICE LIKE CONCAT('%', #{param.goodsPrice}, '%')
            </if>
            <if test="param.salesVolume != null">
                AND a.SALES_VOLUME = #{param.salesVolume}
            </if>
            <if test="param.templateIdList != null and param.templateIdList !='' ">
                AND FIND_IN_SET(#{param.templateIdList}, a.TEMPLATE_ID_LIST) > 0
            </if>
            <if test="param.templateNums != null">
                AND a.TEMPLATE_NUMS = #{param.templateNums}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.rank != null">
                AND a.RANK = #{param.rank}
            </if>
            <if test="param.moduleId != null ">
                AND a.MODULE_ID = #{param.moduleId}
            </if>
            <if test="param.moduleName != null and param.moduleName != ''">
                AND a.MODULE_NAME LIKE CONCAT('%', #{param.moduleName}, '%')
            </if>
            <if test="param.moduleCode != null and param.moduleCode != ''">
                AND a.MODULE_CODE LIKE CONCAT('%', #{param.moduleCode}, '%')
            </if>
            <if test="param.cityCodeList != null and param.cityCodeList != '' ">
                AND a.CITY_CODE_LIST LIKE CONCAT('%', #{param.cityCodeList}, '%')
            </if>
            <if test="param.cityNameList != null and param.cityNameList != ''">
                AND a.CITY_NAME_LIST LIKE CONCAT('%', #{param.cityNameList}, '%')
            </if>
            <if test="param.readingPermission != null">
                AND a.READING_PERMISSION = #{param.readingPermission}
            </if>
            <if test="param.userInfoStatus != null">
                AND a.USER_INFO_STATUS = #{param.userInfoStatus}
            </if>
            <if test="param.oneProveTenKa != null">
                AND a.ONE_PROVE_TEN_KA = #{param.oneProveTenKa}
            </if>
            <if test="param.isOptional != null">
                AND a.IS_OPTIONAL = #{param.isOptional}
            </if>
            <if test="param.isOneBeat != null">
                AND a.IS_ONE_BEAT = #{param.isOneBeat}
            </if>
            <if test="param.goodsType != null and param.goodsType != ''">
                AND a.GOODS_TYPE = #{param.goodsType}
            </if>
            <if test="param.goodsTypeCode != null and param.goodsTypeCode != ''">
                AND a.GOODS_TYPE_CODE = #{param.goodsTypeCode}
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                AND a.CREATED_DATE &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                AND a.CREATED_DATE &lt;= #{param.createTimeEnd}
            </if>
        </where>
        GROUP BY a.ID
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5GoodsInfo">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5GoodsInfo">
        <include refid="selectSql"></include>
    </select>

</mapper>
