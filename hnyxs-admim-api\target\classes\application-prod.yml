# 生产环境配置

# 数据源配置
spring:
  datasource:
    url: **********************************************************************************************************************
    username: hnkj_yxs_app
    password: h^dye3fuEl0tG4dj
    driver-class-name: com.mysql.cj.jdbc.Driver
#    type: com.alibaba.druid.pool.DruidDataSource
#    url: ********************************************************************************************************************************************
#    username: hnkj_yxs_app
#    password: 'Aah7z9M8eGPPm!v9'
#    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
  redis:
    database: 0
    password: 'CTG_ItMv_5doh5Ar'
    timeout: 6000ms
    #哨兵模式
#    sentinel:
#      master: mymaster
#      nodes: 134.176.97.81:26399,134.176.97.82:26379,134.176.97.82:26389
    #集群模式
    cluster:
      nodes:
        - 134.178.197.190:20260
        - 134.178.197.191:20260
        - 134.178.197.190:20261
        - 134.178.197.191:20261
        - 134.178.197.190:20262
        - 134.178.197.191:20262
        - 134.178.197.190:20263
        - 134.178.197.191:20263
    jedis:
      pool:
        max-active: 100
        max-idle: 5
        max-wait: -1ms
        min-idle: 5
  jmx:
    default-domain: jsga_managerJmxPros

# 日志配置
logging:
  file:
    name: hnyxs-admim-api.log
  level:
    root: WARN
    com.hlkj.yxsAdminApi: ERROR
    com.baomidou.mybatisplus: ERROR

# 系统配置
config:
  hnzsxFileImgPath: /app/nfs2/WM/hnzsx/uploads/ #掌上销图片上传地址
  hnzhslFilePath: /zhsl/ #智慧扫楼文件上传地址
