<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslExportApprovalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslExportApprovalEntity">
        <id column="id" property="id"/>
        <result column="submit_user_id" property="submitUserId"/>
        <result column="submit_user" property="submitUser"/>
        <result column="submit_date" property="submitDate"/>
        <result column="approve_user_phone" property="approveUserPhone"/>
        <result column="approve_user" property="approveUser"/>
        <result column="approve_date" property="approveDate"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="data_source_status" property="dataSourceStatus"/>
        <result column="file_name" property="fileName"/>
        <result column="file_path" property="filePath"/>
        <result column="filter_conditions" property="filterConditions"/>
        <result column="type" property="type"/>
        <result column="data_count" property="dataCount"/>
        <result column="rule_id" property="ruleId"/>
        <result column="export_number" property="exportNumber"/>
        <result column="create_time" property="createTime"/>
        <result column="active_event" property="activeEvent"/>
        <result column="data_source_name" property="dataSourceName"/>
        <result column="sms_code" property="smsCode"/>
    </resultMap>

    <!-- 分页查询条件 -->
    <select id="selectPageRel" resultMap="BaseResultMap">
        SELECT a.*
        FROM hnsl_export_approval a
        <where>
            <if test="param.userId != null">
                AND (a.submit_user_id = #{param.userId} OR a.approve_user_phone = #{param.mobile})
            </if>
            <if test="param.submitUser != null and param.submitUser != ''">
                AND a.submit_user LIKE CONCAT('%', #{param.submitUser}, '%')
            </if>
            <if test="param.approveUser != null and param.approveUser != ''">
                AND a.approve_user LIKE CONCAT('%', #{param.approveUser}, '%')
            </if>
            <if test="param.status != null">
                AND a.status = #{param.status}
            </if>
        </where>
        ORDER BY a.submit_date DESC
    </select>

    <!-- 查询列表条件 -->
    <select id="selectListRel" resultMap="BaseResultMap">
        SELECT a.*
        FROM hnsl_export_approval a
        <where>
            <if test="param.type != null">
                AND a.type = #{param.type}
            </if>
            <if test="param.userId != null">
                AND (a.submit_user_id = #{param.userId} OR a.approve_user_phone = #{param.mobile})
            </if>
            <if test="param.submitUser != null and param.submitUser != ''">
                AND a.submit_user LIKE CONCAT('%', #{param.submitUser}, '%')
            </if>
            <if test="param.approveUser != null and param.approveUser != ''">
                AND a.approve_user LIKE CONCAT('%', #{param.approveUser}, '%')
            </if>
            <if test="param.status != null">
                AND a.status = #{param.status}
            </if>
        </where>
        ORDER BY a.submit_date DESC
    </select>

</mapper> 