<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxSanRongPackageMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_san_rong_package a
        <where>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.equityType != null">
                AND a.equity_type LIKE CONCAT('%', #{param.equityType}, '%')
            </if>
            <if test="param.status != null">
                AND a.status = #{param.status}
            </if>
            <if test="param.createDate != null">
                AND a.create_date LIKE CONCAT('%', #{param.createDate}, '%')
            </if>
            <if test="param.updateDate != null">
                AND a.update_date LIKE CONCAT('%', #{param.updateDate}, '%')
            </if>
            <if test="param.typeCode != null">
                AND a.type_code = #{param.typeCode}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSanRongPackage">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSanRongPackage">
        <include refid="selectSql"></include>
    </select>

    <!--  保存商品关联三融加装配置  -->
    <insert id="saveSanRongPackage" parameterType="map">
        INSERT INTO hnkj_yxs.hnzsx_goods_details_san_rong_rel
            (goods_details_id, san_rong_id, create_date)
        VALUES(#{goodsDetailsid}, #{sanRongId}, CURRENT_TIMESTAMP);
    </insert>
    <!--  查询商品关联三融加装配置  -->
    <select id="querySanRongRel" parameterType="Integer" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxSanRongPackage">
        select p.* from hnzsx_san_rong_package p
        inner join hnzsx_goods_details_san_rong_rel h on p.id = h.san_rong_id
        where h.goods_details_id = #{id}
    </select>

    <!--  删除商品关联三融加装配置  -->
    <delete id="delSanRongRel" parameterType="Integer">
        delete
        from hnzsx_goods_details_san_rong_rel
        where goods_details_id = #{id};
    </delete>

</mapper>
