<?xml version="1.0" encoding="UTF-8"?>
<!-- 根节点，设置为调试模式 自动重扫描配置文件 间隔为10秒 -->
<configuration scan="true" scanPeriod="10 seconds" debug="false">
    <contextName>hnxtJobAdmin</contextName>
<!--    <include resource="org/springframework/boot/logging/logback/base.xml" />-->
    <!-- 定义常量 ，下面可以引用 -->
    <!-- log目录 -->
    <property name="LOG_HOME" value="${catalina.home}/logs/WM/hnyxsAdmin/" />
    <property name="BACKUP_HOME" value="${LOG_HOME}/backups" />

    <!-- 定义控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned by default the type ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
        <encoder>
            <pattern>
                [%X{userName}] - [%X{uuid}] - %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
            </pattern>
        </encoder>
    </appender>


    <!-- All 定义指定文件名称的输出 此处设定的文件名称是动态设置的 即 每天生成一个文件 用于记录当天的文件信息 -->
    <appender name="DAILY_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/all.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>
                ${BACKUP_HOME}/%d{yyyy-MM-dd}/all.%d{yyyyMMdd}.log
            </FileNamePattern>
            <maxHistory>10</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[%X{staffCode}] - [%X{customerInfoToken}] - [%X{uuid}] - %date %level [%thread]-[%logger{0}:%line] %msg%n</pattern>
        </encoder>
    </appender>
    <!-- DEBUG 定义指定文件名称的输出 此处设定的文件名称是动态设置的 即 每天生成一个文件 用于记录当天的文件信息 -->
    <appender name="DAILY_DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>
                ${BACKUP_HOME}/%d{yyyy-MM-dd}/debug.%d{yyyyMMdd}.log
            </FileNamePattern>
            <maxHistory>10</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[%X{staffCode}] - [%X{customerInfoToken}] - [%X{uuid}] - %date %level [%thread]-[%logger{0}:%line] %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- WARN  定义指定文件名称的输出 此处设定的文件名称是动态设置的 即 每天生成一个文件 用于记录当天的文件信息 -->
    <appender name="DAILY_WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>
                ${BACKUP_HOME}/%d{yyyy-MM-dd}/warn.%d{yyyyMMdd}.log
            </FileNamePattern>
            <maxHistory>10</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[%X{staffCode}] - [%X{customerInfoToken}] - [%X{uuid}] - %date %level [%thread]-[%logger{0}:%line] %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 定义指定文件名称的输出 此处设定的文件名称是动态设置的 即 每天生成一个文件 用于记录当天的文件信息 -->
    <appender name="DAILY_INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>
                ${BACKUP_HOME}/%d{yyyy-MM-dd}/info.%d{yyyyMMdd}.log
            </FileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%X{staffCode}] - [%X{customerInfoToken}] - [%X{uuid}] - %date %level [%thread]-[%logger{0}:%line] %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="DAILY_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>
                ${BACKUP_HOME}/%d{yyyy-MM-dd}/error.%d{yyyyMMdd}.log
            </FileNamePattern>
            <maxHistory>10</maxHistory>

        </rollingPolicy>
        <encoder>
            <pattern>[%X{staffCode}] - [%X{customerInfoToken}] - [%X{uuid}] - %date %level [%thread]-[%logger{0}:%line] %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- 定义指定文件名称的输出 此处设定的文件名称是动态设置的 即 每天生成一个文件 用于记录当天的文件信息 -->
    </appender>
    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
<!--            <appender-ref ref="DAILY_ALL" />-->
<!--            <appender-ref ref="DAILY_INFO" />-->
<!--            <appender-ref ref="DAILY_DEBUG" />-->
<!--            <appender-ref ref="DAILY_WARN" />-->
<!--            <appender-ref ref="DAILY_ERROR" />-->
        </root>
    </springProfile>

    <!-- 测试环境+开发环境. 多个使用逗号隔开. -->
    <springProfile name="test">
        <logger name="com.hlkj" level="DEBUG" additivity="false">
            <appender-ref ref="DAILY_ALL"></appender-ref>
            <appender-ref ref="DAILY_DEBUG"></appender-ref>
            <appender-ref ref="DAILY_INFO"></appender-ref>
            <appender-ref ref="DAILY_WARN"></appender-ref>
            <appender-ref ref="DAILY_ERROR"></appender-ref>
        </logger>
        <logger name="org.springboot" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ALL"></appender-ref>
            <appender-ref ref="DAILY_DEBUG"></appender-ref>
            <appender-ref ref="DAILY_INFO"></appender-ref>
            <appender-ref ref="DAILY_WARN"></appender-ref>
            <appender-ref ref="DAILY_ERROR"></appender-ref>
        </logger>
        <logger name="org.springframework" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ALL"></appender-ref>
            <appender-ref ref="DAILY_DEBUG"></appender-ref>
            <appender-ref ref="DAILY_INFO"></appender-ref>
            <appender-ref ref="DAILY_WARN"></appender-ref>
            <appender-ref ref="DAILY_ERROR"></appender-ref>
        </logger>
        <root level="INFO">
            <appender-ref ref="DAILY_ALL" />
            <appender-ref ref="DAILY_INFO" />
            <appender-ref ref="DAILY_DEBUG" />
            <appender-ref ref="DAILY_WARN" />
            <appender-ref ref="DAILY_ERROR" />
        </root>
    </springProfile>
    <!-- 生产环境. -->
    <springProfile name="prod">
        <logger name="com.hlkj" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ALL"></appender-ref>
            <appender-ref ref="DAILY_DEBUG"></appender-ref>
            <appender-ref ref="DAILY_INFO"></appender-ref>
            <appender-ref ref="DAILY_WARN"></appender-ref>
            <appender-ref ref="DAILY_ERROR"></appender-ref>
        </logger>
        <logger name="org.springboot" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ALL"></appender-ref>
            <appender-ref ref="DAILY_DEBUG"></appender-ref>
            <appender-ref ref="DAILY_INFO"></appender-ref>
            <appender-ref ref="DAILY_WARN"></appender-ref>
            <appender-ref ref="DAILY_ERROR"></appender-ref>
        </logger>
        <logger name="org.springframework" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ALL"></appender-ref>
            <appender-ref ref="DAILY_DEBUG"></appender-ref>
            <appender-ref ref="DAILY_INFO"></appender-ref>
            <appender-ref ref="DAILY_WARN"></appender-ref>
            <appender-ref ref="DAILY_ERROR"></appender-ref>
        </logger>
        <root level="INFO">
            <appender-ref ref="DAILY_ALL" />
            <appender-ref ref="DAILY_INFO" />
            <appender-ref ref="DAILY_DEBUG" />
            <appender-ref ref="DAILY_WARN" />
            <appender-ref ref="DAILY_ERROR" />
        </root>
    </springProfile>
    <!-- 单独对指定的日志设定级别，使该日志对象输出地日志级别限定在：“INFO”级别，不受跟级别的限制 目标可以是类或者包 -->


</configuration>
