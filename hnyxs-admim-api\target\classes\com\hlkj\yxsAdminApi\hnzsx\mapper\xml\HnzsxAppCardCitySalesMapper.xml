<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzsx.mapper.HnzsxAppCardCitySalesMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnzsx_card_city_sales a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.cardCityId != null">
                AND a.CARD_CITY_ID LIKE CONCAT('%', #{param.cardCityId}, '%')
            </if>
            <if test="param.grade != null">
                AND a.GRADE = #{param.grade}
            </if>
            <if test="param.salesId != null">
                AND a.SALES_ID LIKE CONCAT('%', #{param.salesId}, '%')
            </if>
            <if test="param.salesName != null">
                AND a.SALES_NAME LIKE CONCAT('%', #{param.salesName}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxCardCitySales">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxCardCitySales">
        <include refid="selectSql"></include>
    </select>

</mapper>
